import { isMsgError, sendMsg } from "@/shared/send";
import { MsgAction } from "@/types";
import { I18nType, cookieLanguageKey, defaultLanguage, isValidLocale } from "@tg-fe/i18n";
import { encryptApiAuthorize as _encryptApiAuthorize } from "@tg-fe/shared";

/**
 * 检查是否是有效的url
 */
export const checkIsValidUrl = (url: string) => url.startsWith("http://") || url.startsWith("https://");

/**
 * 检查是否是dark theme
 */
export const checkIsDarkTheme = () => {
  if (window.matchMedia) {
    const darkModeMediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
    return darkModeMediaQuery.matches;
  } else {
    return false;
  }
};

export const getDefaultLanguage = async () => {
  const browserLanguage = navigator.language.toLowerCase() as I18nType.LangType;
  if (isValidLocale(browserLanguage)) return browserLanguage;
  return defaultLanguage;
};
export const getCookieLanguage = async () => {
  const res = await sendMsg<I18nType.LangType>({
    action: MsgAction.GetCookieValue,
    payload: {
      key: cookieLanguageKey,
    },
  });
  if (isMsgError(res)) {
    return getDefaultLanguage();
  }
  if (res && isValidLocale(res)) {
    return res;
  }
};
export async function getLanguage(): Promise<I18nType.LangType> {
  return (await getCookieLanguage()) || getDefaultLanguage();
}

export const encryptApiAuthorize: typeof _encryptApiAuthorize = (...params) => {
  const data = _encryptApiAuthorize(...params);
  return {
    ...data,
    Extension_version: import.meta.env.WXT_APP_VERSION,
  };
};
