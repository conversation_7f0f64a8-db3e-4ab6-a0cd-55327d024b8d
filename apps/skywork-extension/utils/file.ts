export function loadImg(url: string, isCrossOrigin?: boolean) {
  return new Promise<HTMLImageElement>((resolve, reject) => {
    const img = new Image();
    img.src = url;
    // crossOrigin: Anonymous 每次都会重新发起新的请求，导致缓存失效，必要时再设置crossOrigin
    if (isCrossOrigin) {
      img.crossOrigin = "Anonymous";
      img.setAttribute("referrerpolicy", "no-referrer");
    }
    img.onload = function () {
      resolve(img);
    };
    img.onerror = reject;
    if (img.complete) {
      // 浏览器已缓存图片，直接调用回调
      resolve(img);
    }
  });
}

export function loadFileImg(file: File) {
  return loadImg(URL.createObjectURL(file));
}

export function imgToBase64(url: string) {
  return new Promise<string>((resolve, reject) => {
    try {
      let canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d");
      const img = new Image();

      img.crossOrigin = "Anonymous";
      img.setAttribute("referrerpolicy", "no-referrer");
      img.src = url;
      img.onload = function () {
        canvas.height = img.height;
        canvas.width = img.width;
        ctx?.drawImage(img, 0, 0);
        const dataURL = canvas.toDataURL("image/png");
        resolve(dataURL);
        canvas = null as any;
      };
      img.onerror = reject;
    } catch (error) {
      console.log(error);
    }
  });
}
export function fileToBase64(file: File) {
  return new Promise<string>((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      resolve(reader.result as string);
    };
    reader.onerror = (err) => {
      reject(err);
    };
  });
}

export function base64toFile(base: string, filename: string) {
  const arr = base.split(",");
  const mime = arr[0].match(/:(.*?);/)?.[1];
  const suffix = mime?.split("/")[1];
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  //转换成file对象
  return new File([u8arr], `${filename}.${suffix}`, { type: mime });
}

export function base64toBlob(base: string, filename: string) {
  const byteCharacters = atob(base.split(",")[1]); // 解码 Base64 数据，去掉前缀部分
  const byteArrays = [];

  for (let offset = 0; offset < byteCharacters.length; offset += 1024) {
    const slice = byteCharacters.slice(offset, offset + 1024);
    const byteNumbers = new Array(slice.length);

    for (let i = 0; i < slice.length; i++) {
      byteNumbers[i] = slice.charCodeAt(i);
    }

    const byteArray = new Uint8Array(byteNumbers);
    byteArrays.push(byteArray);
  }

  return new Blob(byteArrays);
}

export function isBase64(url: string) {
  if (url.startsWith("data:image")) {
    return true;
  }
  return false;
}
