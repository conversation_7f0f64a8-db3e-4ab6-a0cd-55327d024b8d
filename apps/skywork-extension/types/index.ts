import { FetchEventSourceInit, HttpClientConfig } from "@tg-fe/shared";

export enum SiteType {
  Main = "main",
  External = "external",
}

export enum MsgAction {
  /** 用于background => content 心脏检测 */
  Ping = "Ping",

  /** Request */
  Http = "Http",
  Sse = "Sse",
  SseMessage = "SseMessage",
  SseClose = "SseClose",
  SseError = "SseError",
  SseAbort = "SseAbort",
  HttpCancelAll = "HttpCancelAll",
  SseCancel = "SseCancel",
  Ws = "Ws",
  Tracker = "Tracker",

  UpdateTrackBaseData = "UpdateTrackBaseData",

  /** 上传到oss */
  UploadImageToOss = "UploadImageToOss",

  SystemThemeChange = "SystemThemeChange",
  ToggleSidePanel = "ToggleSidePanel",

  /** Tab */
  GetCurrentTabInfo = "GetCurrentTabInfo",
  CurrentTabChange = "CurrentTabChange",

  /** cookie */
  GetCookieValue = "GetCookieValue",
  GetCookieToken = "GetCookieToken",
  RemoveCookieToken = "RemoveCookieToken",
  CookieTokenChange = "CookieTokenChange",
  CookieTokenRemoved = "CookieTokenRemoved",
}
export interface Response<T> {
  code: number;
  message: string;
  data: T;
  trace_id: string;
}

export type SseRequestParams<D = Record<string, any>> = Omit<FetchEventSourceInit, "onmessage"> & {
  url: RequestInfo;
  data?: Record<string, any>;
  onmessage?: (data: Response<D>) => void;
};
export type MsgPayloadData = {
  msgData: {
    tabId: number;
    id: string;
    appId: string;
  };
};

export type MsgPayload =
  | {
      action: MsgAction.Http;
      payload: {
        url: string;
        options: HttpClientConfig;
      } & MsgPayloadData;
    }
  | {
      action: MsgAction.Sse;
      payload: {
        options: SseRequestParams<Record<string, any>>;
      } & MsgPayloadData;
    }
  | {
      action: MsgAction.SseMessage;
      payload: {
        data: Record<string, any>;
      } & MsgPayloadData;
    }
  | {
      action: MsgAction.SseClose;
      payload: MsgPayloadData;
    }
  | {
      action: MsgAction.SseError;
      payload: {
        error: any;
      } & MsgPayloadData;
    }
  | {
      action: MsgAction.SseAbort;
      payload: MsgPayloadData;
    }
  | {
      action: MsgAction.HttpCancelAll;
      payload?: {
        msgData?: Omit<MsgPayloadData["msgData"], "id">;
      };
    }
  | {
      action: MsgAction.SseCancel;
      payload: MsgPayloadData;
    }
  | {
      action: MsgAction.UploadImageToOss;
      payload: {
        url: string;
        fileUrl: string;
        base64: string;
      } & MsgPayloadData;
    }
  | {
      action: MsgAction.Tracker;
      payload: {
        data: Record<string, any>;
      };
    }
  | {
      action: MsgAction.UpdateTrackBaseData;
      payload: {
        data: Record<string, any>;
      };
    }
  | {
      action: MsgAction.SystemThemeChange;
      payload: {
        theme: ThemeMode;
      };
    }
  | {
      action: MsgAction.CookieTokenChange;
      payload: {
        token: string;
      };
    }
  | {
      action: MsgAction.GetCookieValue;
      payload: {
        key: string;
      };
    }
  | {
      action: MsgAction.GetCookieToken;
    }
  | {
      action: MsgAction.RemoveCookieToken;
    }
  | {
      action: MsgAction.CookieTokenRemoved;
    }
  | {
      action: MsgAction.GetCurrentTabInfo;
    }
  | {
      action: MsgAction.CurrentTabChange;
      payload: {
        url: string;
        tab: chrome.tabs.Tab;
      };
    }
  | {
      action: MsgAction.ToggleSidePanel;
    }
  | {
      action: MsgAction.Ping;
    };

export enum ThemeType {
  System = 1,
  Light = 2,
  Dark = 3,
}
export enum ThemeMode {
  Light = "light",
  Dark = "dark",
}
