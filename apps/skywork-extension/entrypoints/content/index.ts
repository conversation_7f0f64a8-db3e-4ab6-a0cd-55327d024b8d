import { defineContentScript } from "wxt/sandbox";
import { createSidebar } from "./sidebar";
import { themeListener } from "./theme";

export default defineContentScript({
  // matches: ["<all_urls>"],
  matches: ["<all_urls>"],
  cssInjectionMode: "ui",
  async main(ctx) {
    /** 创建侧边栏 */
    createSidebar(ctx);
    /** 主题监听 */
    themeListener();

    window.addEventListener("error", (event) => {
      console.log("===skywork error", event);
      event.preventDefault();
      event.stopImmediatePropagation();
      event.stopPropagation();
    });

    window.addEventListener("unhandledrejection", (event) => {
      console.log("===skywork unhandledrejection", event.reason);
      event.preventDefault();
      event.stopImmediatePropagation();
      event.stopPropagation();
    });
  },
});
