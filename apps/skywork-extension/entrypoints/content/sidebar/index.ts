import { createApp } from "@/apps/sidebar/main";
import { sendMsg } from "@/shared/send";
import { MsgAction, MsgPayload } from "@/types";
import { ContentScriptContext, createShadowRootUi } from "wxt/client";
import { createGlobalStyle, removeGlobalStyle } from "./styles";

export const createSidebar = async (ctx: ContentScriptContext) => {
  const ui = await createShadowRootUi(ctx, {
    name: "skywork-sidebar",
    position: "inline",
    anchor: "body",
    append: "last",
    onMount: (container) => {
      const url = browser.runtime.getURL("" as any).slice(0, -1);
      const { app: root, store } = createApp({
        publicUrl: url,
        onCloseApp: () => {
          ui.remove();
        },
      });
      root.mount(container);
      createGlobalStyle(url);
      return { root, store };
    },
    onRemove: (elements) => {
      if (elements?.root) {
        sendMsg({
          action: MsgAction.HttpCancelAll,
          payload: {
            msgData: {
              appId: elements.store.state.value?.app?.appId,
              tabId: elements.store.state.value?.app?.tab?.id,
            },
          },
        });
        elements.root.unmount();
        removeGlobalStyle();
      }
    },
  });
  browser.runtime.onMessage.addListener((event: MsgPayload, _sender, sendResponse) => {
    switch (event.action) {
      case MsgAction.ToggleSidePanel: {
        if (ui.mounted?.root) {
          ui.remove();
        } else {
          ui.mount();
        }
        break;
      }
    }
  });
};
