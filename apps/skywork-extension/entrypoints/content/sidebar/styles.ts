const styleId = "skywork-sidebar-style";
export const createGlobalStyle = (url: string) => {
  const style = document.createElement("style");
  style.id = styleId;
  style.innerHTML = `
      html {
        width: calc(100% - 450px) !important;
        position: relative !important;
        min-height: 100vh !important;
        false
      }
@font-face {
  font-family: "Outfit";
  src: url("${url}/fonts/Outfit/Outfit-ExtraBold.ttf") format("truetype");
  font-weight: 800;
  /* ExtraBold */
  font-style: normal;
}

@font-face {
  font-family: "Outfit";
  src: url("${url}/fonts/Outfit/Outfit-Bold.ttf") format("truetype");
  font-weight: 700;
  /* Bold */
  font-style: normal;
}

@font-face {
  font-family: "Outfit";
  src: url("${url}/fonts/Outfit/Outfit-Black.ttf") format("truetype");
  font-weight: 900;
  /* Black */
  font-style: normal;
}

@font-face {
  font-family: "Outfit";
  src: url("${url}/fonts/Outfit/Outfit-SemiBold.ttf") format("truetype");
  font-weight: 600;
  /* SemiBold */
  font-style: normal;
}

@font-face {
  font-family: "Outfit";
  src: url("${url}/fonts/Outfit/Outfit-Light.ttf") format("truetype");
  font-weight: 300;
  /* Light */
  font-style: normal;
}

@font-face {
  font-family: "Outfit";
  src: url("${url}/fonts/Outfit/Outfit-Medium.ttf") format("truetype");
  font-weight: 500;
  /* Medium */
  font-style: normal;
}

@font-face {
  font-family: "Outfit";
  src: url("${url}/fonts/Outfit/Outfit-ExtraLight.ttf") format("truetype");
  font-weight: 200;
  /* ExtraLight */
  font-style: normal;
}

@font-face {
  font-family: "Outfit";
  src: url("${url}/fonts/Outfit/Outfit-Regular.ttf") format("truetype");
  font-weight: 400;
  /* Regular */
  font-style: normal;
}

@font-face {
  font-family: "Outfit";
  src: url("${url}/fonts/Outfit/Outfit-Thin.ttf") format("truetype");
  font-weight: 100;
  /* Thin */
  font-style: normal;
}

      `;
  document.head.appendChild(style);
};
export const removeGlobalStyle = () => {
  const node = document.getElementById(styleId);
  if (!node) return;
  node.remove();
};
