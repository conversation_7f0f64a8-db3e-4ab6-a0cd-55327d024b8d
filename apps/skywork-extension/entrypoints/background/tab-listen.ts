import { APP_DOMAIN } from "@/constants/app";
import { SESSION_KEYS } from "@/constants/cacheKey";
// import { META_DATA } from "@/entrypoints/background/constants";
import { currentTabListener } from "@/shared/listeners";
import { sendTabMsg } from "@/shared/send";
import { MsgAction, SiteType } from "@/types";
import { checkIsValidUrl } from "@/utils";
import { storage } from "wxt/storage";

export default ({ onChangeTab: _onChangeTab }: { onChangeTab: (tab: chrome.tabs.Tab) => void }) => {
  currentTabListener((tab) => {
    setSiteTypeByTab(tab);
    onChangeTab(tab);
    _onChangeTab(tab);
  });

  // browser.tabs.onCreated.addListener(async (tab) => {
  //   if (!tab.id) return;
  //   META_DATA.visitedTabs.push({
  //     windowId: tab.windowId,
  //     tabId: tab.id,
  //   });
  //   console.log("===META_DATA.visitedTabs", META_DATA.visitedTabs);
  //   // storage.setItem(LOCAL_KEYS.visitedTabs, [
  //   //   ...visitedTabs,
  //   //   {
  //   //     windowId: tab.windowId,
  //   //     tabId: tab.id,
  //   //   },
  //   // ]);
  // });
  // browser.windows.onRemoved.addListener(async (windowId) => {
  //   const visitedTabs = META_DATA.visitedTabs;
  //   META_DATA.visitedTabs = visitedTabs.filter((tab) => tab.windowId !== windowId);
  //   // const visitedTabs = await getVisitedTabs();
  //   // storage.setItem(
  //   //   LOCAL_KEYS.visitedTabs,
  //   //   visitedTabs.filter((tab) => tab.windowId !== windowId)
  //   // );
  // });
  // browser.runtime.onStartup.addListener(() => {
  //   console.log("===onStartup");
  //   // storage.removeItem(LOCAL_KEYS.visitedTabs);
  //   META_DATA.visitedTabs = [];
  // });
  // browser.runtime.onSuspend.addListener(() => {
  //   // storage.removeItem(LOCAL_KEYS.visitedTabs);
  //   META_DATA.visitedTabs = [];
  //   console.log("===onSuspend");
  // });
};

function setSiteTypeByTab(tab: chrome.tabs.Tab) {
  const url = tab?.pendingUrl || tab?.url;
  let type = SiteType.External;
  if (url?.includes(APP_DOMAIN)) {
    type = SiteType.Main;
  }
  storage.setItem(SESSION_KEYS.siteType, type);
}

let prevUrl = "";
function onChangeTab(tab: chrome.tabs.Tab) {
  if (tab.url && checkIsValidUrl(tab.url) && tab.status === "complete" && prevUrl !== tab.url) {
    prevUrl = tab.url;
    sendTabMsg(tab.id, {
      action: MsgAction.CurrentTabChange,
      payload: {
        url: tab.url,
        tab,
      },
    });
  }
}
