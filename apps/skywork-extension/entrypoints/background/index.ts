import { APP_PAGE } from "@/constants/app";
import { track } from "@/entrypoints/background/track";
import { isMsgError, sendTabMsg, sendTabMsgPoll } from "@/shared/send";
import { initTrackBaseData } from "@/shared/track";
import { MsgAction } from "@/types";
import cookieListen from "./cookie-listen";
import messageListen from "./message-listen";
import tabListen from "./tab-listen";

const delay = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(true);
    }, 100);
  });
};
const pollingPing = (id: number) => {
  let count = 0;
  const run = async () => {
    // sidebar 轮训content_script是否正常
    if (count > 2) return;
    count++;
    await delay();
    sendTabMsg(id, {
      action: MsgAction.Ping,
    }).then((res) => {
      let popup = "";
      if (isMsgError(res)) {
        popup = "sidebar-popup.html";
        run();
      }
      browser.action.setPopup({
        popup,
      });
    });
  };
  run();
};

const insertContentScript = async (tab: chrome.tabs.Tab) => {
  try {
    await browser.scripting.executeScript({
      target: { tabId: tab.id! },
      files: ["content-scripts/content.js"],
    });
  } catch (err) {
    console.log(`${tab.url} 插入content.js失败`, err);
  }
};

export default defineBackground(() => {
  browser.runtime.onInstalled.addListener((details) => {
    initTrackBaseData().finally(() => {
      console.log("===onInstalled", details);
      track.extension_install_successed_state();
    });

    /** 给已经存在的页面注入脚本 */
    browser.tabs.query({}, (tabs) => {
      tabs.forEach((tab) => {
        if (tab.id && tab.url && checkIsValidUrl(tab.url)) {
          insertContentScript(tab);
        }
      });
    });
  });

  self.addEventListener("error", (event) => {
    console.log("===error", event);
    event.preventDefault();
  });

  self.addEventListener("unhandledrejection", (event) => {
    console.log("===unhandledrejection", event.reason);
    event.preventDefault();
  });

  // message 监听
  messageListen();
  // cookie 监听
  cookieListen();
  // tab 监听
  tabListen({
    onChangeTab: (tab) => {
      if (tab.id && tab.url && checkIsValidUrl(tab.url) && tab.status === "complete") {
        pollingPing(tab.id);
      }
    },
  });

  // 点击扩展图片，打开侧边栏
  (browser.action ?? browser.browserAction).onClicked.addListener((tab) => {
    /*
        sidepanel模式方案
        同时执行两个事件，
        如果已经打开侧边栏, open将不会做任何事
        如果还没打开，open将打开侧边栏，close消息还不会发送至侧边栏，因为还没创建
      */
    // open 需要 用户手动操作
    // chrome.sidePanel.open({ windowId: tab.windowId, tabId: tab.id });
    // sendMsg({
    //   action: MsgAction.CloseSidePanel,
    // });
    if (tab.url && checkIsValidUrl(tab.url)) {
      sendTabMsgPoll(tab.id, {
        action: MsgAction.ToggleSidePanel,
      });
    } else {
      browser.tabs.create({
        url: APP_PAGE,
      });
    }
  });
});
