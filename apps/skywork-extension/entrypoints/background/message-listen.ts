import { LOCAL_KEYS } from "@/constants/cacheKey";
import { getCookieValueByName, getToken, removeToken } from "@/shared/cookie";
import { httpRequest, sseRequest } from "@/shared/request";
import { sendTabMsg } from "@/shared/send";
import { getCurrentTab } from "@/shared/tabs";
import { addCustomBaseData, alTracker } from "@/shared/track";
import { MsgAction, MsgPayload, MsgPayloadData } from "@/types";
import { storage } from "wxt/storage";

type Req = {
  controller: AbortController;
} & MsgPayloadData["msgData"];

const createReq = () => {
  let reqs = [] as Req[];
  const pushReq = (req: Req) => {
    reqs.push(req);
  };
  const removeReq = (data: MsgPayloadData["msgData"]) => {
    const index = reqs.findIndex((v) => v.id === data.id && v.appId === data.appId);
    if (index !== -1) {
      reqs.splice(index, 1);
    }
  };
  const abortReq = (data: MsgPayloadData["msgData"]) => {
    const sseReq = reqs.find((v) => v.id === data.id && v.appId === data.appId);
    sseReq?.controller.abort();
  };
  const abortReqs = (data?: Omit<MsgPayloadData["msgData"], "id">) => {
    const _reqs = reqs.filter((req) => {
      if (!data) return true;
      if (data.appId && req.appId !== data.appId) return false;
      if (data.tabId && req.tabId !== data.tabId) return false;
      return true;
    });
    _reqs.forEach((req) => {
      req.controller.abort();
    });
    reqs = [];
  };
  return {
    reqs,
    pushReq,
    removeReq,
    abortReq,
    abortReqs,
  };
};

export default () => {
  const { pushReq, removeReq, abortReqs } = createReq();
  const { pushReq: pushSseReq, removeReq: removeSseReq, abortReq: abortSseReq, abortReqs: abortSseReqs } = createReq();
  browser.runtime.onMessage.addListener((options: MsgPayload, _sender, sendResponse) => {
    const action = options.action;
    try {
      switch (action) {
        /** 普通http请求 */
        case MsgAction.Http: {
          const controller = new AbortController();
          const msgData = options.payload.msgData;
          pushReq({
            ...msgData,
            controller,
          });
          httpRequest(options.payload.url, {
            ...options.payload.options,
            signal: controller.signal,
          })
            .then((data) => {
              removeReq(msgData);
              sendResponse({
                data,
                msgData,
              });
            })
            .catch((error) => {
              removeReq(msgData);
              sendResponse({
                msgData,
                error,
              });
            });
          return true;
        }
        /** sse请求 */
        case MsgAction.Sse: {
          const msgData = options.payload.msgData;
          const res = sseRequest({
            ...options.payload.options,
            onmessage: (ev) => {
              sendTabMsg(msgData.tabId, {
                action: MsgAction.SseMessage,
                payload: {
                  msgData,
                  data: ev,
                },
              });
            },
            onabort() {
              removeSseReq(msgData);
              sendTabMsg(msgData.tabId, {
                action: MsgAction.SseAbort,
                payload: {
                  msgData,
                },
              });
            },
            onclose() {
              removeSseReq(msgData);
              sendTabMsg(msgData.tabId, {
                action: MsgAction.SseClose,
                payload: {
                  msgData,
                },
              });
            },
            onerror(error) {
              removeSseReq(msgData);
              sendTabMsg(msgData.tabId, {
                action: MsgAction.SseError,
                payload: {
                  msgData,
                  error,
                },
              });
            },
          });
          pushSseReq({
            ...msgData,
            controller: res.abortController!,
          });
          sendResponse({
            msgData,
          });
          return true;
        }
        /** 取消搜索请求 */
        case MsgAction.HttpCancelAll: {
          abortReqs(options.payload?.msgData);
          abortSseReqs(options.payload?.msgData);
          break;
        }
        /** 取消sse */
        case MsgAction.SseCancel: {
          abortSseReq(options.payload.msgData);
          break;
        }
        /** 上传oss */
        case MsgAction.UploadImageToOss: {
          const controller = new AbortController();
          const msgData = options.payload.msgData;
          pushReq({
            ...msgData,
            controller,
          });
          fetch(options.payload.fileUrl, {
            signal: controller.signal,
          })
            .then((response) => response.blob())
            .then((blob) => {
              fetch(options.payload.url, {
                method: "PUT",
                headers: {
                  "Content-Type": "application/octet-stream",
                },
                body: blob,
              })
                .then((res) => {
                  removeReq(msgData);
                  sendResponse({
                    data: res,
                    msgData,
                  });
                })
                .catch((error) => {
                  removeReq(msgData);
                  sendResponse({
                    error,
                    msgData,
                  });
                });
            });
          return true;
        }
        /** 埋点 */
        case MsgAction.Tracker: {
          alTracker(options.payload.data);
          break;
        }
        /** 埋点 */
        case MsgAction.UpdateTrackBaseData: {
          addCustomBaseData(options.payload.data);
          break;
        }
        /** 主题变更 */
        case MsgAction.SystemThemeChange: {
          storage.setItem(LOCAL_KEYS.systemTheme, options.payload.theme);
          break;
        }
        /** 获取cookie值 */
        case MsgAction.GetCookieValue: {
          const key = options.payload.key;
          getCookieValueByName(key).then((value) => {
            sendResponse(value);
          });
          return true;
        }
        /** 获取token */
        case MsgAction.GetCookieToken: {
          getToken().then((token) => {
            sendResponse(token);
          });
          return true;
        }
        /** 移除token */
        case MsgAction.RemoveCookieToken: {
          removeToken().then(() => {
            sendResponse();
          });
          return true;
        }
        /** 获取当前Tab */
        case MsgAction.GetCurrentTabInfo: {
          getCurrentTab().then((tab) => {
            sendResponse(tab);
          });
          return true;
        }
        /** toggle 侧边栏 */
        case MsgAction.ToggleSidePanel: {
          getCurrentTab().then((tab) => {
            sendTabMsg(tab.id, {
              action: MsgAction.ToggleSidePanel,
            });
          });
          break;
        }
      }
    } catch (error) {
      sendResponse({
        error,
      });
    }
  });
};
