import { tokenListener } from "@/shared/listeners";
import { sendTabMsg } from "@/shared/send";
import { MsgAction } from "@/types";
import { checkIsValidUrl } from "@/utils";

const eachTabs = async (callback: (tab: chrome.tabs.Tab) => void) => {
  browser.tabs.query(
    {
      status: "complete",
    },
    (tabs) => {
      tabs.forEach((tab) => {
        // if (tab.id && tab.url && checkIsValidUrl(tab.url) && META_DATA.visitedTabs.some((t) => t.tabId === tab.id)) {
        if (tab.id && tab.url && checkIsValidUrl(tab.url)) {
          callback(tab);
        }
      });
    }
  );
};

export default () => {
  tokenListener(
    (token) => {
      console.log("token change：", token);
      if (token) {
        eachTabs((tab) => {
          sendTabMsg(tab.id, {
            action: MsgAction.CookieTokenChange,
            payload: {
              token,
            },
          });
        });
      }
    },
    () => {
      console.log("token removed");
      eachTabs((tab) => {
        sendTabMsg(tab.id, {
          action: MsgAction.CookieTokenRemoved,
        });
      });
    }
  );
};
