import { APP_DOMAIN, APP_TOKEN_NAME } from "@/constants/app";
import { isMsgError, sendMsg } from "@/shared/send";
import { MsgAction } from "@/types";

// get token
export const getToken = () => {
  return getCookieValueByName(APP_TOKEN_NAME);
};
export const getTokenByClient = () => {
  return new Promise<string | null>((resolve, reject) => {
    sendMsg<string>({
      action: MsgAction.GetCookieToken,
    })
      .then((res) => {
        if (isMsgError(res)) {
          return reject(res);
        }
        resolve(res);
      })
      .catch(reject);
  });
};

// remove token
export const removeToken = async () => {
  await removeCookieValueByName(APP_TOKEN_NAME);
};
export const removeTokenByClient = async () => {
  await sendMsg<string>({
    action: MsgAction.RemoveCookieToken,
  });
};

export const removeCookieValueByName = async (tokenName: string, domain = APP_DOMAIN) => {
  const cookies = await browser.cookies.getAll({ domain: domain });
  const cookie = cookies.find((cookie) => cookie.name === tokenName);
  if (!cookie) {
    return console.warn(`❌ 未找到 ${tokenName} 在 ${domain} 下的 cookie`);
  }
  browser.cookies.remove({
    name: cookie.name,
    url: `https://${cookie.domain}${cookie.path}`,
  });
  console.log(`✅ 成功删除 ${tokenName} 在 ${domain} 下的 cookie`);
};

export const getCookieValueByName = async (tokenName: string, domain = APP_DOMAIN) => {
  const cookies = await browser.cookies.getAll({ domain: domain });
  const cookie = cookies.find((cookie) => cookie.name === tokenName);
  return cookie?.value ?? null;
};
