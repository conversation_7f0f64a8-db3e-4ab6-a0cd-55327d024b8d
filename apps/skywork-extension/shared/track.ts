import { get_k_device_hash_from_cookie } from "@/shared/deviceId";
import {
  addCustomBaseData as _addCustomBaseData,
  alTracker as _alTracker,
  initTrack,
} from "@tg-fe/track-help/src/track/extension";

initTrack({
  logstore: import.meta.env.MODE === "production" ? "fe-prod" : "fe-test",
  project: "stats", // 海外sls域名替换为：stats.skywork.ai
  host: "skywork.ai",
});

export const initTrackBaseData = async () => {
  const device_hash = await get_k_device_hash_from_cookie();
  _addCustomBaseData({
    device_hash,
  });
};
initTrackBaseData();
export const addCustomBaseData = async (data: Record<string, any>) => {
  _addCustomBaseData({ ...data });
};

/**
 * 相关参数格式化规则
 * @param data
 * @returns
 */
const formatTrackData = (data: Record<string, any>) => {
  const { event, event_info, other } = data;
  return {
    event,
    event_info: {
      origin: "extension",
      ...event_info,
    },
    other: {
      ...other,
    },
  };
};

export const alTracker = (data: Record<string, any>) => {
  const formatData = formatTrackData(data);
  _alTracker(formatData);
  return;
  if (import.meta.env.MODE === "development") {
    console.log("===track===", formatData);
  } else {
    _alTracker(formatData);
  }
};
