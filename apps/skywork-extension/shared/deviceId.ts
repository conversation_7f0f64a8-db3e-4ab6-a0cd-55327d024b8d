import { APP_DOMAIN, APP_PAGE } from "@/constants/app";
import { md5 } from "js-md5";

const _set_cookie = async (name: string, value: string) => {
  await browser.cookies.set({
    url: APP_PAGE,
    name: name,
    value: value,
    domain: APP_DOMAIN,
    path: "/",
    expirationDate: Math.floor(new Date().getTime() / 1000) + 365 * 24 * 60 * 60,
  });
};

export const get_k_device_hash_from_cookie = async () => {
  const res = await browser.cookies.get({
    url: APP_PAGE,
    name: "k_device_hash",
  });
  if (!res?.value) {
    const data = md5(navigator.userAgent + setRandomNumberWay());
    await _set_cookie("k_device_hash", data);
    return data;
  } else {
    await _set_cookie("k_device_hash", res.value);
    return res.value;
  }
};

// 随机生成字符串函数
function setRandomNumberWay() {
  let r;
  const data = [
    "0",
    "1",
    "2",
    "3",
    "4",
    "5",
    "6",
    "7",
    "8",
    "9",
    "a",
    "b",
    "c",
    "d",
    "e",
    "f",
    "g",
    "h",
    "i",
    "j",
    "k",
    "l",
    "m",
    "n",
    "o",
    "p",
    "q",
    "r",
    "s",
    "t",
    "u",
    "v",
    "w",
    "x",
    "y",
    "z",
  ];
  let result = "";
  for (let i = 0; i < 16; i++) {
    r = Math.floor(Math.random() * 36);
    result += data[r];
  }
  return result;
}
