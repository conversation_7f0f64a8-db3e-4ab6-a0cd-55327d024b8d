import { APP_DOMAIN, APP_TOKEN_NAME } from "@/constants/app";
import { getToken, getTokenByClient } from "@/shared/cookie";
import { getCurrentTab, getCurrentTabByClient } from "@/shared/tabs";
import { MsgAction, MsgPayload } from "@/types";

/** token变化 */
export const tokenListener = (
  updateFn: (token: string) => void,
  removeFn?: () => void,
  { immediate } = { immediate: true }
) => {
  if (immediate) {
    getToken().then((value) => {
      updateFn?.(value || "");
    });
  }
  browser.cookies.onChanged.addListener(async (changeInfo) => {
    const { cookie, removed, cause } = changeInfo;
    if (!cookie.domain.includes(APP_DOMAIN) || cookie.name !== APP_TOKEN_NAME) return;
    if (removed) {
      removeFn?.();
      return;
    }
    if (["explicit"].includes(cause)) {
      updateFn?.(changeInfo.cookie.value);
    }
  });
};

export const tokenClientListener = (
  updateFn: (token: string) => void,
  removeFn?: () => void,
  { immediate } = { immediate: true }
) => {
  if (immediate) {
    getTokenByClient().then((value) => {
      updateFn?.(value || "");
    });
  }
  const fn = (event: MsgPayload) => {
    if (event.action === MsgAction.CookieTokenChange) {
      updateFn?.(event.payload.token);
    }
    if (event.action === MsgAction.CookieTokenRemoved) {
      removeFn?.();
    }
  };
  browser.runtime.onMessage.addListener(fn);
  return () => {
    browser.runtime.onMessage.removeListener(fn);
  };
};

/** current tab 变化 */
export const currentTabListener = (onChange: (tab: chrome.tabs.Tab) => void, { immediate } = { immediate: true }) => {
  if (immediate) {
    getCurrentTab().then(onChange);
  }
  browser.tabs.onActivated.addListener(async (activeInfo) => {
    const _tab = await browser.tabs.get(activeInfo.tabId);
    onChange(_tab);
    return true;
  });
  browser.tabs.onUpdated.addListener((_tabId, changeInfo, tab) => {
    if (changeInfo.status === "complete" && tab.active) {
      onChange(tab);
    }
  });
};
/** client current tab 变化 */
export const currentTabClientListener = (
  onChange: (tab: chrome.tabs.Tab) => void,
  { immediate } = { immediate: true }
) => {
  if (immediate) {
    getCurrentTabByClient().then(onChange);
  }

  const fn = (event: MsgPayload) => {
    if (event.action === MsgAction.CurrentTabChange) {
      onChange?.(event.payload.tab);
    }
  };
  browser.runtime.onMessage.addListener(fn);
  return () => {
    browser.runtime.onMessage.removeListener(fn);
  };
};
