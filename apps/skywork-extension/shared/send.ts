import { MsgPayload, MsgPayloadData } from "@/types";

export interface SendMsgError {
  error: any;
}

export function isMsgError<T>(data: SendMsgError | T): data is SendMsgError {
  if (!data) return false;
  return !!(data as SendMsgError)?.error;
}

export async function sendMsg<T = unknown>(msg: MsgPayload) {
  try {
    return await browser.runtime.sendMessage<MsgPayload, T & MsgPayloadData>(msg);
  } catch (error) {
    console.log("===sendMsg error", error, msg);
    return {
      error,
    } as SendMsgError;
  }
}

export async function sendMsgPoll<T = unknown>(
  msg: MsgPayload,
  options?: {
    pollInterval?: number;
    count?: number;
  }
) {
  const pollInterval = options?.pollInterval ?? 100;
  const count = options?.count === undefined ? 2 : options.count;
  const res = await sendMsg<T>(msg);
  if (isMsgError(res)) {
    if (count > 0) {
      await new Promise((resolve) => setTimeout(resolve, pollInterval));
      return sendMsgPoll<T>(msg, {
        pollInterval,
        count: count - 1,
      });
    }
  }
}

export async function sendTabMsg<T = unknown>(id?: number, msg?: MsgPayload) {
  // console.log("===sendTabMsg", id, msg);
  if (!id || !msg) return;
  try {
    return await browser.tabs.sendMessage<MsgPayload, T>(id, msg);
  } catch (error) {
    console.log("===sendTabMsg error", error, msg);
    return {
      error,
    } as SendMsgError;
  }
}
export async function sendTabMsgPoll<T = unknown>(
  id?: number,
  msg?: MsgPayload,
  options?: {
    pollInterval?: number;
    count?: number;
  }
) {
  const pollInterval = options?.pollInterval ?? 100;
  const count = options?.count === undefined ? 3 : options.count;
  const res = await sendTabMsg<T>(id, msg);
  if (isMsgError(res)) {
    if (count > 0) {
      await new Promise((resolve) => setTimeout(resolve, pollInterval));
      return await sendTabMsgPoll<T>(id, msg, {
        pollInterval,
        count: count - 1,
      });
    }
  }
  return res;
}
