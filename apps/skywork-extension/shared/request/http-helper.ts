export const commonHeaders = {
  "Content-Type": "application/json",
  Accept: "application/json",
  Authorization: () => `Bear<PERSON> ${Math.random()}`,
} as const;

export const getHeader = (headers: HeadersInit | undefined, name: string, fallback?: string): string => {
  const fallbackValue = fallback ?? "";
  if (!headers) return fallbackValue;
  // 对 headers 进行类型收窄，分情况获取 value
  if (headers instanceof Headers) {
    return headers.get(name) ?? fallbackValue;
  } else if (Array.isArray(headers)) {
    const header = headers.find(([key]) => key === name);
    return header ? header[1] : fallbackValue;
  } else {
    return headers[name] ?? fallbackValue;
  }
};

export const setHeader = (
  container: {
    headers?: HeadersInit;
  },
  name: string,
  value: string
): void => {
  if (!container.headers) {
    container.headers = {};
  }
  if (container.headers instanceof Headers) {
    container.headers.set(name, value);
  } else if (Array.isArray(container.headers)) {
    container.headers.push([name, value]);
  } else {
    container.headers[name] = value;
  }
};

export const setHeaders = (
  container: {
    headers?: HeadersInit;
  },
  headers: Record<string, string>
): void => {
  Object.entries(headers).forEach(([name, value]) => {
    setHeader(container, name, value);
  });
};
