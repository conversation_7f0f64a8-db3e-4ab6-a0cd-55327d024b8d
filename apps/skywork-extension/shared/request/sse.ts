import { isMsgError, sendMsg } from "@/shared/send";
import { getCurrentTabByClient } from "@/shared/tabs";
import useAppStore from "@/store/app";
import { MsgAction, MsgPayloadData, Response, SseRequestParams } from "@/types";
import { encryptApiAuthorize } from "@/utils";
import { fetchEventSource } from "@tg-fe/shared";
import { uniqueId } from "lodash-es";

interface SseRequestReturnVal {
  data?: Record<string, any>;
  cancel: () => void;
  abortController?: AbortController;
}

export const sseRequest = <D extends Record<string, any> = Record<string, any>>(
  options: SseRequestParams<D>
): SseRequestReturnVal => {
  const { url, headers, ...rest } = options;
  const body = typeof options.data === "string" ? options.data : JSON.stringify(options.data);
  const abortController = new AbortController();
  const _url = `${import.meta.env.WXT_API_BASE_URL}${url}`;
  const encryptApiData = encryptApiAuthorize({
    method: "post",
    url: _url,
    data: options.data,
  });
  fetchEventSource(_url, {
    ...rest,
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      accept: "text/event-stream",
      device: "plugin",
      ...encryptApiData,
      ...headers,
    },
    withoutBrowser: true,
    openWhenHidden: true,
    credentials: "include",
    onclose() {
      rest.onclose?.();
    },
    onerror(e) {
      rest.onerror?.(e);
      throw e;
    },
    body,
    signal: abortController.signal,
    onmessage(ev) {
      const data = ev.data && JSON.parse(ev.data);
      rest.onmessage?.(data as Response<D>);
    },
  });

  return {
    data: options.data,
    cancel: () => {
      if (abortController && typeof abortController.abort == "function") {
        abortController.abort();
      }
    },
    abortController,
  };
};

export const sendSseRequest = async <D extends Record<string, any> = Record<string, any>>(
  options: SseRequestParams<D>,
  msgOptions?: Partial<MsgPayloadData["msgData"]>
) => {
  const fn = (event: any) => {
    const __msgData = (event as any).payload?.msgData;
    if (!__msgData) return;
    const id = __msgData?.id as string;
    const appId = __msgData?.appId as string;
    const tabId = __msgData?.tabId as number;
    if (id !== msgData.id || tabId !== msgData.tabId) {
      return;
    }
    if (appId !== useAppStore().appId) {
      browser.runtime.onMessage.removeListener(fn);
      return;
    }
    if (event.action === MsgAction.SseMessage) {
      options.onmessage?.(event.payload.data as Response<D>);
    }
    if (event.action === MsgAction.SseClose) {
      options.onclose?.();
      browser.runtime.onMessage.removeListener(fn);
    }
    if (event.action === MsgAction.SseError) {
      options.onerror?.(event.payload.error);
      browser.runtime.onMessage.removeListener(fn);
    }
    if (event.action === MsgAction.SseAbort) {
      options.onabort?.();
      browser.runtime.onMessage.removeListener(fn);
    }
  };
  const appStore = useAppStore();
  const msgData = {
    appId: appStore.appId,
    tabId: (appStore.tab?.id || (await getCurrentTabByClient()).id) as number,
    id: uniqueId(),
    ...msgOptions,
  };
  browser.runtime.onMessage.addListener(fn);
  const res = await sendMsg({
    action: MsgAction.Sse,
    payload: {
      options: options as SseRequestParams,
      msgData,
    },
  });

  if (isMsgError(res)) {
    throw res.error;
  }
  if (res.msgData.appId !== useAppStore().appId) {
    throw new Error("appId not match");
  }
  return {
    cancel: () => {
      sendMsg({
        action: MsgAction.SseCancel,
        payload: {
          msgData: res.msgData,
        },
      });
    },
  };
};
