import { get_k_device_hash_from_cookie } from "@/shared/deviceId";
import { isMsgError, sendMsg } from "@/shared/send";
import { getCurrentTabByClient } from "@/shared/tabs";
import useAppStore from "@/store/app";
import { MsgAction, MsgPayloadData, Response } from "@/types";
import { encryptApiAuthorize } from "@/utils";
import { HttpClient, HttpClientConfig } from "@tg-fe/shared";
import { uniqueId } from "lodash-es";
const commonRequestInterceptors: HttpClient["requestInterceptors"] = [
  [
    (config) => {
      const encryptApiData = encryptApiAuthorize({
        method: config.method,
        url: config.url,
        data: config.data,
        params: config.params,
      });
      Object.assign(config.headers, {
        device: "plugin",
        channel: "",
        ...encryptApiData,
      });
      return config;
    },
    null,
  ],
];

const whiteListCode = [200, 0, 101200];

export const hasAuthRequest = (error: any) => {
  if (error?.status === 401 || error?.response?.data?.code === 401) {
    return false;
  }
  return true;
};

export const httpClient = new HttpClient({
  baseURL: import.meta.env.WXT_API_BASE_URL,
  timeout: 200000,
  withCredentials: true,
  isError: (response) => !whiteListCode.includes(response.data.code),
  getData: (response) => response.data,
  requestInterceptors: commonRequestInterceptors,
});

export const httpRequest = async <Data = Record<string, any>>(url: string, options: HttpClientConfig) => {
  // eslint-disable-next-line no-useless-catch
  const headers = {
    device_id: await get_k_device_hash_from_cookie(),
    device_hash: await get_k_device_hash_from_cookie(),
  };
  const res = await httpClient.request<Response<Data>>({
    url,
    ...options,
    headers: {
      ...headers,
      ...options.headers,
    },
  });
  return res.data;
};

type Extra = {
  immediate?: boolean;
};

const request = async <Data>(
  url: string,
  options: HttpClientConfig,
  msgOptions?: Partial<MsgPayloadData["msgData"]>
) => {
  const method = options.method ?? "POST";
  options = {
    ...options,
    headers: {
      ...options.headers,
    },
    method,
  };
  const appStore = useAppStore();
  const res = await sendMsg<{
    data: Data;
  }>({
    action: MsgAction.Http,
    payload: {
      url,
      options,
      msgData: {
        appId: appStore.appId,
        tabId: (appStore.tab?.id || (await getCurrentTabByClient()).id) as number,
        id: uniqueId(),
        ...msgOptions,
      },
    },
  });
  if (isMsgError(res)) {
    throw res.error;
  }
  if (res.msgData.appId !== useAppStore().appId) {
    throw new Error("appId not match");
  }
  return res.data;
};

export const useHttpRequest = <ResponseData = unknown>(
  url: string,
  requestOptions: HttpClientConfig = {},
  extraOptions: Extra = { immediate: false }
) => {
  const isLoading = ref(false);
  const data = ref<ResponseData | null>(null);
  const error = ref<unknown | null>(null);
  const _request = async () => {
    isLoading.value = true;
    data.value = await request<ResponseData>(url, requestOptions)
      .catch((err) => {
        error.value = err;
        return data.value as ResponseData; // 报错时让 data.value = data.value，相当于对 data.value 什么都不做
      })
      .finally(() => {
        isLoading.value = false;
      });
  };
  if (extraOptions.immediate) {
    void _request();
  }
  return { data, error, isLoading, request: _request };
};

export const http = {
  get: <T = Record<string, any>>(
    url: string,
    options?: Omit<HttpClientConfig, "method">,
    msgOptions?: Partial<MsgPayloadData["msgData"]>
  ) => request<T>(url, { ...options, method: "GET" }, msgOptions),
  post: <T = Record<string, any>>(
    url: string,
    options?: Omit<HttpClientConfig, "method">,
    msgOptions?: Partial<MsgPayloadData["msgData"]>
  ) => request<T>(url, { ...options, method: "POST" }, msgOptions),
  put: <T = Record<string, any>>(
    url: string,
    options?: Omit<HttpClientConfig, "method">,
    msgOptions?: Partial<MsgPayloadData["msgData"]>
  ) => request<T>(url, { ...options, method: "PUT" }, msgOptions),
  delete: <T = Record<string, any>>(
    url: string,
    options?: Omit<HttpClientConfig, "method">,
    msgOptions?: Partial<MsgPayloadData["msgData"]>
  ) => request<T>(url, { ...options, method: "DELETE" }, msgOptions),
};
