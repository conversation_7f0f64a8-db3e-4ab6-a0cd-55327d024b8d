import { isMsgError, sendMsg } from "@/shared/send";
import { MsgAction } from "@/types";

export const getCurrentTab = () => {
  return new Promise<chrome.tabs.Tab>((resolve, reject) => {
    browser.tabs
      .query({
        active: true,
        currentWindow: true,
      })
      .then((tabs) => {
        resolve(tabs[0]);
      })
      .catch(reject);
  });
};

export const getCurrentTabByClient = async () => {
  const res = await sendMsg<chrome.tabs.Tab>({
    action: MsgAction.GetCurrentTabInfo,
  });
  if (isMsgError(res)) {
    throw res.error;
  }
  return res;
};
