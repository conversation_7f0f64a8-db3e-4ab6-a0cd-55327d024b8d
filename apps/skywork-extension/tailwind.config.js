/** @type {import('tailwindcss').Config} */
import tailwindTheme from "@tg-fe/design-token";

/** rem 转 px, 防止部分页面html fontSize 不是16 */
module.exports = {
  content: ["assets/**", "entrypoints/**", "apps/**", "components/**", "../../packages/ui/src/**"],
  theme: {
    ...tailwindTheme,
    spacing: Array.from({ length: 1000 })
      .map((_, i) => i / 2)
      .reduce((map, v, index) => {
        map[v] = `${v * 4}px`;
        return map;
      }, {}),
    fontFamily: {
      Outfit: [
        "Outfit",
        {
          fontFeatureSettings: '"liga" off, "clig" off',
        },
      ],
    },
    fontSize: {
      xs: ["12px", { lineHeight: "16px" }],
      sm: ["14px", { lineHeight: "20px" }],
      base: ["16px", { lineHeight: "16px" }],
      lg: ["18px", { lineHeight: "28px" }],
      xl: ["20px", { lineHeight: "28px" }],
      "2xl": ["16px", { lineHeight: "32px" }],
      "3xl": ["30px", { lineHeight: "36px" }],
      "4xl": ["36px", { lineHeight: "40px" }],
      "5xl": ["48px", { lineHeight: "1" }],
      "6xl": ["60px", { lineHeight: "1" }],
      "7xl": ["72px", { lineHeight: "1" }],
      "8xl": ["96px", { lineHeight: "1" }],
      "9xl": ["128px", { lineHeight: "1" }],
    },
  },
  plugins: [],
};
