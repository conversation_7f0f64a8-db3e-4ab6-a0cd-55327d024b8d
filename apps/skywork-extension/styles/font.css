@font-face {
  font-family: "Outfit";
  src: url("https://static.skywork.ai/fe/skywork-site-assets/fonts/Outfit/static/Outfit-ExtraBold.ttf")
    format("truetype");
  font-weight: 800;
  /* ExtraBold */
  font-style: normal;
}

@font-face {
  font-family: "Outfit";
  src: url("https://static.skywork.ai/fe/skywork-site-assets/fonts/Outfit/static/Outfit-Bold.ttf") format("truetype");
  font-weight: 700;
  /* Bold */
  font-style: normal;
}

@font-face {
  font-family: "Outfit";
  src: url("https://static.skywork.ai/fe/skywork-site-assets/fonts/Outfit/static/Outfit-Black.ttf") format("truetype");
  font-weight: 900;
  /* Black */
  font-style: normal;
}

@font-face {
  font-family: "Outfit";
  src: url("https://static.skywork.ai/fe/skywork-site-assets/fonts/Outfit/static/Outfit-SemiBold.ttf")
    format("truetype");
  font-weight: 600;
  /* SemiBold */
  font-style: normal;
}

@font-face {
  font-family: "Outfit";
  src: url("https://static.skywork.ai/fe/skywork-site-assets/fonts/Outfit/static/Outfit-Light.ttf") format("truetype");
  font-weight: 300;
  /* Light */
  font-style: normal;
}

@font-face {
  font-family: "Outfit";
  src: url("https://static.skywork.ai/fe/skywork-site-assets/fonts/Outfit/static/Outfit-Medium.ttf") format("truetype");
  font-weight: 500;
  /* Medium */
  font-style: normal;
}

@font-face {
  font-family: "Outfit";
  src: url("https://static.skywork.ai/fe/skywork-site-assets/fonts/Outfit/static/Outfit-ExtraLight.ttf")
    format("truetype");
  font-weight: 200;
  /* ExtraLight */
  font-style: normal;
}

@font-face {
  font-family: "Outfit";
  src: url("https://static.skywork.ai/fe/skywork-site-assets/fonts/Outfit/static/Outfit-Regular.ttf") format("truetype");
  font-weight: 400;
  /* Regular */
  font-style: normal;
}

@font-face {
  font-family: "Outfit";
  src: url("https://static.skywork.ai/fe/skywork-site-assets/fonts/Outfit/static/Outfit-Thin.ttf") format("truetype");
  font-weight: 100;
  /* Thin */
  font-style: normal;
}
