.md-container {
  --md-theme-border-color: var(----line-line-3);
  word-break: break-word;
  overflow: hidden;

  ul,
  ol {
    padding-left: 24px;
  }

  ul {
    list-style: disc;
  }

  ol {
    list-style: decimal;
  }

  p,
  h1,
  h2,
  h3,
  h4,
  h5,
  ol,
  ul,
  li {
    // line-height: 200%;
    line-height: 150%;
  }

  h1,
  h2,
  h3,
  h4,
  h5 {
    font-weight: 600;
    margin: 16px 0 4px;
    line-height: 28px;
  }

  p,
  ul,
  ol {
    font-size: 16px;
  }

  h1 {
    font-size: 24px;
  }

  h2 {
    font-size: 22px;
  }

  h3 {
    font-size: 20px;
  }

  h4 {
    font-size: 18px;
  }

  h5 {
    font-size: 16px;
  }

  ol,
  ul {
    margin: 0.6em 0;
  }

  ul {
    margin-bottom: 16px;
  }

  ol {
    margin-top: 16px;
  }

  ol li::marker {
    font-weight: bold;
  }

  p {
    line-height: 26px;
    margin: 0;
    padding: 0.5rem 0;
  }

  p+ul {
    margin-top: 0;
  }

  li {
    margin-bottom: 12px;
    // &:not(:last-child) {
    //   margin-bottom: 12px;
    // }

    p {
      line-height: 1.8;
      padding: 0;
    }
  }

  code {
    background-color: rgba(#616161, 0.1);
    color: #616161;
  }

  pre {
    overflow: auto;
    background: #0d0d0d;
    color: #fff;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;

    code {
      color: inherit;
      padding: 0;
      background: none;
      font-size: 0.8rem;
    }
  }

  // img {
  //   max-width: 100%;
  //   height: auto;
  // }

  // .page-image {
  //   max-width: 100%;
  //   height: auto;
  //   max-height: 380px;
  //   margin-left: auto;
  //   margin-right: auto;
  // }

  blockquote {
    padding-left: 1rem;
    color: #979fab;
    margin: 16px 0;
    position: relative;

    &::before {
      content: "";
      display: inline-block;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
      width: 2px;
      background-color: #b1b7c0;
      border-radius: 2px;
    }

    p {
      padding: 0px;
    }
  }

  hr {
    border: none;
    border-top: 2px solid rgba(#0d0d0d, 0.1);
    margin: 2rem 0;
  }

  em {
    font-synthesis: style;
    font-style: oblique 15deg;
  }

  strong {
    font-weight: 700;
  }

  table {
    border-collapse: inherit;
    table-layout: fixed;
    width: 100%;
    margin: 0;
    overflow: hidden;
    margin-bottom: 1em;
    margin-top: 1em;
    border-spacing: 0;
    border-right: 1px solid var(--md-theme-border-color);
    border-top: 1px solid var(--md-theme-border-color);
    border-radius: 10px;

    tr {

      th,
      td {
        border-top: 0;
        border-right: 0;
      }

      &:first-child,
      &:last-child {
        th:first-child {
          border-top-left-radius: 10px;
        }

        th:last-child {
          border-top-right-radius: 10px;
        }

        td:first-child {
          border-bottom-left-radius: 10px;
        }

        td:last-child {
          border-bottom-right-radius: 10px;
        }
      }
    }

    td,
    th {
      min-width: 1em;
      vertical-align: top;
      box-sizing: border-box;
      position: relative;
      word-wrap: break-word;
      padding: 12px 16px;
      border: 1px solid var(--md-theme-border-color);

      >* {
        margin-bottom: 0;
      }

      p {
        margin: 0;
        padding: 0;
      }
    }

    th {
      font-weight: 500;
      background-color: rgba(247, 248, 249, 0.6);
      // background-color: var(--md-theme-border-color);
      line-height: 170%;
      color: var(--text-icon-text-1);
      text-align: left;

      p {
        font-size: 14px;
      }
    }

    td {
      line-height: 160%;
      color: var(--text-icon-text-3, #485568);

      p {
        font-size: 12px;
      }
    }
  }


  .hljs {}

  .hljs-comment,
  .hljs-quote {
    color: #5c6370;
    font-style: italic;
  }

  .hljs-doctag,
  .hljs-formula,
  .hljs-keyword {
    color: #c678dd;
  }

  .hljs-deletion,
  .hljs-name,
  .hljs-section,
  .hljs-selector-tag,
  .hljs-subst {
    color: #e06c75;
  }

  .hljs-literal {
    color: #56b6c2;
  }

  .hljs-addition,
  .hljs-attribute,
  .hljs-meta .hljs-string,
  .hljs-regexp,
  .hljs-string {
    color: #98c379;
  }

  .hljs-attr,
  .hljs-number,
  .hljs-selector-attr,
  .hljs-selector-class,
  .hljs-selector-pseudo,
  .hljs-template-variable,
  .hljs-type,
  .hljs-variable {
    color: #d19a66;
  }

  .hljs-bullet,
  .hljs-link,
  .hljs-meta,
  .hljs-selector-id,
  .hljs-symbol,
  .hljs-title {
    color: #61aeee;
  }

  .hljs-built_in,
  .hljs-class .hljs-title,
  .hljs-title.class_ {
    color: #e6c07b;
  }

  .hljs-emphasis {
    font-style: italic;
  }

  .hljs-strong {
    font-weight: 700;
  }

  .hljs-link {
    text-decoration: underline;
  }
}