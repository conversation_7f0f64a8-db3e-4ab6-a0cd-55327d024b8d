@import url("./variables.css");
@use "./variables";

#skywork-sidebar-root {
  font-size: 14px;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family:
    Outfit,
    Helvetica Neue,
    Helvetica,
    PingFang SC,
    Hiragino Sans GB,
    Microsoft YaHei,
    Arial,
    sans-serif;
  color: var(--text-icon-text-2);
  background-color: var(--fill-fill-5);
  z-index: 2147483647;
  text-align: left;
  font-weight: 400;
  line-height: normal;
  white-space: normal;
  box-sizing: border-box;
  word-break: normal;
}

/* 设置滚动条整体的宽度 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

/* 设置滚动条滑块的颜色 */
::-webkit-scrollbar-thumb {
  background: var(--fill-fill-1);
  border-radius: 2px;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

img {
  max-width: none;
}
