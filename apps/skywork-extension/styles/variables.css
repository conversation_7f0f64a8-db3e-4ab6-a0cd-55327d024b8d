@import url("@tg-fe/design-token/doccie/theme/default/index.css");
@import url("@tg-fe/design-token/doccie/theme/dark/index.css");

.skywork-root-vars {
  --skywork-sidebar-width: 450px;
  --el-color-primary: var(--black);
  --el-bg-color-overlay: var(--fill-fill-5);
  --el-dialog-bg-color: var(--fill-fill-5);
  --el-mask-color: var(--fill-fill-5);
  --el-bg-color: var(--fill-fill-5);

  .el-button--text {
    --el-color-primary-light-3: var(--text-icon-text-2);
    --el-color-primary-dark-2: var(--text-icon-text-2);
  }

  .el-button--primary {
    --el-color-primary-light-3: var(--text-icon-text-2);
    --el-color-primary-light-5: var(--fill-fill-1);
    --el-color-primary-dark-2: var(--black);
    --el-button-active-bg-color: var(--el-color-primary-dark-2);
    --el-button-active-border-color: var(--el-color-primary-dark-2);
    --el-button-text-color: var(--text-icon-text-6);
    --el-button-hover-text-color: var(--text-icon-text-6);
  }

  .el-button--danger {
    --el-color-danger: var(--red-red500);
    --el-button-text-color: var(--white);
    --el-border-radius-base: 12px;
    --el-button-hover-bg-color: var(--red-red600);
    --el-button-hover-border-color: var(--red-red600);
    --el-button-disabled-bg-color: var(--red-red200);
    --el-button-disabled-border-color: var(--red-red200);
  }

  .el-button--info {
    --el-button-bg-color: var(--fill-fill-3-hover);
    --el-color-info: var(--fill-fill-3-hover);
    --el-button-text-color: var(--text-icon-text-2);

    --el-button-hover-text-color: var(--text-icon-text-2);
    --el-border-radius-base: 12px;
    --el-button-hover-bg-color: var(--fill-fill-2);
    --el-button-hover-border-color: var(--fill-fill-2);
    --el-button-disabled-bg-color: var(--fill-fill-3-hover);
    --el-button-disabled-border-color: var(--fill-fill-3-hover);
    --el-button-disabled-text-color: var(--text-icon-text-5);

    --el-button-active-bg-color: var(--fill-fill-2);
    --el-button-active-border-color: var(--fill-fill-2);
  }

  .el-checkbox__input.is-checked .el-checkbox__inner {
    --el-checkbox-checked-bg-color: var(--text-icon-text-1);
    --el-checkbox-checked-input-border-color: var(--text-icon-text-1);
  }

  .el-checkbox__input.is-checked .el-checkbox__inner:after {
    --el-checkbox-checked-icon-color: var(--surface-surface-4);
  }

  --el-overlay-color-lighter: var(--background-overlay-1);

  .el-switch.is-checked .el-switch__core {
    background-color: var(--black);
    border-color: var(--el-switch-border-color, var(--black));
  }

  .el-switch__core {
    --el-switch-off-color: var(--text-icon-text-4);
  }

  .el-popper {
    --el-text-color-primary: var(--background-overlay-2-tooltip);
    --el-bg-color: #fff;
    font-size: 14px;
    border-radius: 12px;
  }
}
