{"name": "skywork-extension", "description": "manifest.json description", "private": true, "version": "0.0.2", "type": "module", "scripts": {"dev": "wxt", "dev:firefox": "wxt -b firefox", "build": "wxt build", "build:firefox": "wxt build -b firefox", "build:staging": "wxt build --mode staging", "build:firefox:staging": "wxt build -b firefox --mode staging", "zip": "wxt zip", "zip:firefox": "wxt zip -b firefox", "compile": "vue-tsc --noEmit", "postinstall": "wxt prepare"}, "dependencies": {"@tg-fe/shared": "workspace:*", "@tg-fe/track-help": "workspace:*", "@tg-fe/i18n": "workspace:*", "@tg-fe/ui": "workspace:*", "@vueuse/core": "^12.4.0", "element-plus": "catalog:", "js-cookie": "3.0.5", "js-md5": "0.8.3", "lodash-es": "4.17.21", "pinia": "catalog:", "vue": "catalog:"}, "devDependencies": {"@tg-fe/design-token": "workspace:*", "@types/chrome": "0.0.280", "@types/js-cookie": "3.0.6", "@types/lodash-es": "4.17.12", "@vitejs/plugin-vue-jsx": "3.1.0", "@wxt-dev/module-vue": "1.0.1", "autoprefixer": "10.4.20", "postcss": "8.5.1", "sass-embedded": "1.83.4", "tailwindcss": "3.4.17", "typescript": "5.6.3", "unplugin-auto-import": "19.0.0", "unplugin-element-plus": "0.9.0", "unplugin-vue-components": "28.0.0", "vite-svg-loader": "5.1.0", "vue-tsc": "2.1.10", "wxt": "0.19.13"}}