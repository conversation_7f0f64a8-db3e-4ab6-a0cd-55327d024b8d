import DocMessage from "@/apps/sidebar/components/DocMessage";
import { $t } from "@tg-fe/i18n";
import { useNetworkWindow } from "@tg-fe/shared";

const useNetworkStatus = () => {
  useNetworkWindow({
    onOffline() {
      DocMessage.error(
        $t("main.The network connection is disconnected, please check the network connection and try again")
      );
    },
    onOnline() {
      DocMessage.success($t("main.The network has been reconnected"));
    },
  });
};
export { useNetworkStatus };
