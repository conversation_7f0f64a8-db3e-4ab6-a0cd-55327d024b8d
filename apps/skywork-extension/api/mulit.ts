import { apiUrls } from "@/api/apiUrls";
import { http } from "@/shared/request";
import { MsgPayloadData } from "@/types";

// 项目文件列表&解析进度
export interface apiUploadProgressParams {
  project_id?: string;
  file_ids: string[];
}
export const apiUploadProgress = async (data: apiUploadProgressParams) => {
  return http.post<{
    export_url: string;
    list: FileResult[];
  }>(apiUrls.mulit.uploadProgress, {
    data,
  });
};

export interface apiUploadAnalysisSimplifyRes {
  file_name: string;
  file_id: string;
  file_size: number;
  file_type: string;
  knowledge_file_id: number;
  is_success: boolean;
  reason: string;
}

/** 解析 */
export const apiUploadAnalysisSimplify = async (data: Record<string, any>) => {
  return http.post<{ analysis_result_list: apiUploadAnalysisSimplifyRes[] }>(apiUrls.mulit.uploadAnalysisSimplify, {
    data,
  });
};

/** 获取解析的数据 */
export const apiUploadGetTask = async (data: Record<string, any>, msgOptions?: Partial<MsgPayloadData["msgData"]>) => {
  return http.post<any>(
    apiUrls.mulit.uploadGetTask,
    {
      data,
    },
    msgOptions
  );
};

/** 保存到知识库 */
export const apiInformationSourceAddToKnowledge = async (data: { file_ids: string[] }) => {
  return http.post<any>(apiUrls.mulit.informationSourceAddToKnowledge, {
    data,
  });
};
