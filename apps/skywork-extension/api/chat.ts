import { apiUrls } from "@/api/apiUrls";
import { http } from "@/shared/request";
import { AgentHistory, AgentId, AgentSource } from "@tg-fe/shared";

interface apiChatHistoryParams {
  project_id?: number;
  source?: AgentSource;
  offset?: number;
  session_id?: string;
}
/** 获取chat 历史记录 */
export const apiChatHistory = (params: apiChatHistoryParams) => {
  return http.post<AgentHistory>(apiUrls.creation.chatHistory, {
    data: params,
  });
};

/** 获取sessionId */
export const apiChatSessionIdGet = (params: { agent_id: AgentId; source: AgentSource; file_url?: string }) => {
  return http.post<{
    session_id: string;
    file_id: string;
    has_knowledge: boolean;
  }>(apiUrls.creation.chatSessionIdGet, {
    data: params,
  });
};

/** 停止sse */
export const apiChatSseStop = (params: { session_id: string; question_id: string }) => {
  return http.post<{
    is_stop: boolean;
  }>(apiUrls.creation.chatSseStop, {
    data: params,
  });
};
