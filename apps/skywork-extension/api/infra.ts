import { apiUrls } from "@/api/apiUrls";
import { http } from "@/shared/request";

export enum BusinessFileType {
  user_head_picture = "user_head_picture",
}
export interface apiOverseasInfraFileAddParams {
  business_file_type: BusinessFileType;
  file_name: string;
  file_type: string;
  file_size: number;
  expire_time?: number;
  is_get_cdn_url?: boolean;
}
// 普通单文件上传前，获取oss_url、流水号(file_no)
export const apiOverseasInfraFileAdd = async (data: apiOverseasInfraFileAddParams) => {
  return http.post<{ file_expire_url: string; file_no: string; expire_time: number }>(apiUrls.infra.fileAdd, {
    data,
  });
};

export interface ResFileInfo {
  file_no: string;
  file_expire_url: string;
  file_name: string;
  file_type: string;
  file_size: number;
}
// 根据file_no[]获取文件信息
export const apiOverseasInfraFileGet = async (data: {
  file_no_list: string[];
  expire_time?: number;
  is_get_cdn_url?: boolean; // 是否获取cdn链接
}) => {
  return http.post<{
    file_no_list: string[];
    file_record_map: Record<string, ResFileInfo>;
  }>(apiUrls.infra.fileGet, {
    data,
  });
};
