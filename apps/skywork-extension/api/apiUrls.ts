export const apiUrls = {
  usercenter: {
    userLogout: "/usercenter/user/logout",
    userGet: "/usercenter/user/get",
    userUpdate: "/usercenter/user/update",
    userSettingUpdate: "/usercenter/user/setting/update",
    feedbackReport: "/usercenter/feedback/report",
    feedbackDistribute: "/usercenter/feedback/distribute",
  },
  creation: {
    chatSse: "/creation/chat/sse",
    chatSseStop: "/creation/chat/sse/stop",
    chatHistory: "/creation/chat/history",
    chatSessionIdGet: "/creation/chat/session_id/get",
    resourceKnowledgeCreate: "/creation/resource/knowledge/create",
  },
  infra: {
    fileAdd: "infra/file/add",
    fileGet: "infra/file/get",
  },
  mulit: {
    uploadAnalysis: "/mulit/upload/analysis",
    uploadAnalysisSimplify: "/mulit/upload/analysis_simplify",
    uploadProgress: "/mulit/upload/progress",
    uploadGetTask: "/mulit/upload/get_task",
    informationSourceAddToKnowledge: "/mulit/information_source/add_to_knowledge",
  },
};
