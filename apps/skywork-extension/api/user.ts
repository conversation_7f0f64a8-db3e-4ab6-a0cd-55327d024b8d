import { apiUrls } from "@/api/apiUrls";
import { http } from "@/shared/request";
import { UserInfo, UserSetting } from "@/types/user";

/**
 * 用户用户信息
 */
export const apiUserGet = async () => {
  const res = await http.get<{
    suid: string;
    user_info: UserInfo;
    user_setting: UserSetting;
  }>(apiUrls.usercenter.userGet);
  return res;
};

/**
 * 登出
 */
export const apiUserLogout = async () => {
  return http.get(apiUrls.usercenter.userLogout);
};

/**
 * 更新用户信息
 */
export const apiUserUpdate = async (params: Partial<UserSetting & UserInfo>) => {
  return await http.post(apiUrls.usercenter.userUpdate, {
    data: params,
  });
};

/**
 * 更新用户设置
 */
export const apiUserSettingUpdate = async (params: Partial<UserSetting & UserInfo>) => {
  return await http.post(apiUrls.usercenter.userSettingUpdate, {
    data: params,
  });
};

/**
 * 反馈报告 获取反馈
 */
export enum FeedbackTemplateType {
  Contactus = "contactus",
  Investigate = "investigate",
}
export interface FeedbackDistribute {
  template_id: string;
  questions: {
    question_id: string;
    title: string;
  }[];
}
export const apiFeedbackDistribute = async (type: FeedbackTemplateType) => {
  return await http.get<FeedbackDistribute>(apiUrls.usercenter.feedbackDistribute, {
    params: {
      template_type: type,
    },
  });
};

/** 反馈上报 */
export function apiFeedbackReport(data: Record<string, any>) {
  return http.post(apiUrls.usercenter.feedbackReport, {
    data,
  });
}
