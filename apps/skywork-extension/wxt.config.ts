import VueJsx from "@vitejs/plugin-vue-jsx";
import path from "path";
import AutoImport from "unplugin-auto-import/vite";
import ElementPlus from "unplugin-element-plus/vite";
import { ElementPlusResolver } from "unplugin-vue-components/resolvers";
import Components from "unplugin-vue-components/vite";
import ViteSvgLoader from "vite-svg-loader";
import { defineConfig } from "wxt";
import toUtf8 from "./plugins/toUtf8";

// See https://wxt.dev/api/config.html
export default defineConfig({
  extensionApi: "chrome",
  modules: ["@wxt-dev/module-vue"],
  runner: {
    startUrls: ["https://zh.wikipedia.org/"],
  },
  manifest: {
    host_permissions: ["https://*/*", "http://*/*"],
    name: "__MSG_appName__",
    description: "__MSG_appDesc__",
    default_locale: "en",
    permissions: ["storage", "cookies", "tabs", "scripting", "activeTab"],
    externally_connectable: {
      matches: ["*://*.skywork.ai/*", "*://localhost/*"],
    },
    web_accessible_resources: [
      {
        resources: ["app.js"],
        matches: ["https://*/*", "http://*/*"],
      },
      {
        resources: ["fonts/*"],
        matches: ["https://*/*", "http://*/*"],
      },
    ],
    action: {
      default_title: "Toggle Skywork AI Sidebar",
      browser_style: true,
    },
    // side_panel: {
    //   default_path: "sidepanel.html",
    // },
    content_scripts: [
      {
        matches: ["https://*/*", "http://*/*"],
        js: ["content-scripts/content.js"],
      },
      {
        matches: ["*://*.skywork.ai/*", "*://localhost/*"],
        js: ["inject-script.js"],
      },
    ],
  },
  vite: ({ mode }) => {
    const isProd = mode === "production";
    return {
      esbuild: {
        drop: isProd ? ["console"] : [],
      },
      resolve: {
        alias: {
          "@": path.resolve(__dirname, "./"),
        },
      },
      plugins: [
        ViteSvgLoader({
          defaultImport: "url",
        }),
        AutoImport({
          imports: ["vue", "vue-router", "pinia"],
          dts: "./auto-gen/auto-imports.d.ts",
          eslintrc: {
            enabled: true,
            filepath: "./auto-gen/eslintrc-auto-import.json",
          },
          resolvers: [ElementPlusResolver()],
        }),
        ElementPlus({}),
        VueJsx(),
        Components({
          extensions: ["vue", "tsx"],
          dts: "./auto-gen/components.d.ts",
          resolvers: [ElementPlusResolver()],
        }),
        toUtf8(),
      ] as any[],
    };
  },
});
