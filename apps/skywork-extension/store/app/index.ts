import { currentTabClientListener } from "@/shared/listeners";
import { STORE_KEYS } from "@/store/constants";
import { uniqueId } from "lodash-es";
import { defineStore } from "pinia";
import useTheme from "./useTheme";

const useAppStore = defineStore(STORE_KEYS.app, () => {
  const appId = ref(uniqueId());
  const publicUrl = ref("");
  const instanceApp = ref({
    onCloseApp: () => {},
  });
  const tab = ref<chrome.tabs.Tab>();

  // 主题配置
  const theme = useTheme();

  const updatePublicUrl = (url: string) => {
    publicUrl.value = url;
  };

  const destroy = currentTabClientListener(async (_tab) => {
    tab.value = _tab;
  });

  const updateCloseAppFn = (fn: () => void) => {
    instanceApp.value.onCloseApp = fn;
  };

  onUnmounted(destroy);

  return {
    appId,
    publicUrl,
    tab,
    instanceApp,
    updatePublicUrl,
    updateCloseAppFn,
    ...theme,
  };
});
export default useAppStore;
