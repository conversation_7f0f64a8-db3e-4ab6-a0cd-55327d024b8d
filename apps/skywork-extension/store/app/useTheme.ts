import { LOCAL_KEYS } from "@/constants/cacheKey";
import { useUserStore } from "@/store/user";
import { ThemeMode, ThemeType } from "@/types";
import { checkIsDarkTheme } from "@/utils";
import { storage } from "wxt/storage";

const getDefaultTheme = () => {
  return checkIsDarkTheme() ? ThemeMode.Dark : ThemeMode.Light;
};

const useTheme = () => {
  const { userSetting } = storeToRefs(useUserStore());
  const _theme = ref(getDefaultTheme());
  const _cacheTheme = ref<ThemeMode>(getDefaultTheme());

  // 获取监听系统主题
  storage.watch<ThemeMode>(LOCAL_KEYS.systemTheme, (value) => {
    value && (_theme.value = value);
  });
  storage.getItem<ThemeMode>(LOCAL_KEYS.systemTheme).then((value) => {
    value && (_theme.value = value);
  });

  // 实际主题
  const theme = computed(() => {
    if (userSetting.value?.plugin_theme === ThemeType.Light) {
      return ThemeMode.Light;
    }
    if (userSetting.value?.plugin_theme === ThemeType.Dark) {
      return ThemeMode.Dark;
    }
    if (userSetting.value?.plugin_theme === ThemeType.System) {
      return _theme.value;
    }
    return _cacheTheme.value;
  });

  const isDarkMode = computed(() => theme.value === ThemeMode.Dark);

  // 缓存主题 & 获取缓存主题
  storage
    .getItem<{
      theme: ThemeMode;
      type: ThemeType;
    }>(LOCAL_KEYS.theme)
    .then((value) => {
      if (!value) return;
      if (value.type === ThemeType.System) return;

      _cacheTheme.value = value.theme || getDefaultTheme();
    });

  watch(
    [() => theme.value, () => userSetting.value?.plugin_theme],
    ([val, themeType]) => {
      if (!themeType) return;
      if ([ThemeType.Light, ThemeType.Dark].includes(themeType)) {
        storage.setItem(LOCAL_KEYS.theme, {
          theme: val,
          type: themeType,
        });
        return;
      }
      if (ThemeType.System === themeType) {
        storage.removeItem(LOCAL_KEYS.theme);
      }
    },
    {
      immediate: true,
    }
  );

  return {
    theme,
    isDarkMode,
  };
};
export default useTheme;
