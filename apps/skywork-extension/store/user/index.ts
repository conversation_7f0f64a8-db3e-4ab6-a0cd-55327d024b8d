import { apiResourceKnowledgeCreate } from "@/api/knowledge";
import { apiUserGet, apiUserSettingUpdate, apiUserUpdate } from "@/api/user";
import { hasAuthRequest } from "@/shared/request";
import { sendMsg } from "@/shared/send";
import { STORE_KEYS } from "@/store/constants";
import { MsgAction } from "@/types";
import { UserInfo, UserSetting } from "@/types/user";
import { getCookieLanguage } from "@/utils";
import i18n from "@tg-fe/i18n";
import { debounce } from "lodash-es";
import { defineStore } from "pinia";
import useToken from "./useToken";

export const useUserStore = defineStore(STORE_KEYS.user, () => {
  const { token } = useToken();
  const userInfo = ref<UserInfo>();
  const userSetting = ref<UserSetting>();
  const hasAuth = ref(true);

  const getUserInfo = async () => {
    try {
      const data = await apiUserGet();
      userInfo.value = data.user_info;
      userSetting.value = data.user_setting;
      hasAuth.value = true;
      sendMsg({
        action: MsgAction.UpdateTrackBaseData,
        payload: {
          data: {
            uid: data.suid,
          },
        },
      });
      i18n.global.locale.value = (await getCookieLanguage()) || data.user_setting.language;
    } catch (e) {
      hasAuth.value = hasAuthRequest(e);
    }
  };

  const getUserInfoDebounce = debounce(getUserInfo, 100);
  watch(token, (_token) => {
    if (_token === undefined) return;
    if (_token === "") {
      return;
    }
    getUserInfoDebounce();
    apiResourceKnowledgeCreate();
  });

  const updateUserInfo = async (params: Partial<UserInfo>) => {
    await apiUserUpdate(params);
    getUserInfo();
  };
  const updateUserSetting = async (params: Partial<UserSetting>) => {
    userSetting.value = {
      ...(userSetting.value as UserSetting),
      ...params,
    };
    await apiUserSettingUpdate(params);
  };
  return {
    token,
    hasAuth,
    userInfo,
    userSetting,
    updateUserInfo,
    updateUserSetting,
  };
});
