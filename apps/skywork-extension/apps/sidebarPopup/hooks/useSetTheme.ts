import { ThemeMode } from "@/types";
import useTheme from "./useTheme";

const useSetTheme = () => {
  const { theme } = useTheme();

  watch(
    theme,
    (theme) => {
      document.querySelector("#skywork-sidebar-root")?.classList.remove(ThemeMode.Light, ThemeMode.Dark);
      document.querySelector("#skywork-sidebar-root")?.classList.add(theme);
    },
    {
      immediate: true,
    }
  );
};
export default useSetTheme;
