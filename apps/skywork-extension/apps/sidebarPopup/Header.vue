<script lang="ts" setup>
import SvgIcon from "@/apps/sidebarPopup/components/SvgIcon/SvgIcon.vue";
import logo from "@/assets/images/logo-text.png";
import logoDark from "@/assets/images/logo-text-dark.png";
import { ThemeMode } from "@/types";
import useTheme from "@/apps/sidebarPopup/hooks/useTheme";

const { theme } = useTheme();

const onClose = () => {
  window.close();
};
</script>
<template>
  <div class="flex items-center justify-between" id="skywork-sidebar-root">
    <img class="h-[24px] cursor-pointer" :src="theme === ThemeMode.Dark ? logoDark : logo" alt="logo" />
    <SvgIcon class="h-5 w-5 cursor-pointer fill-[--text-icon-text-2]" name="ic_close2" @click="onClose" />
  </div>
</template>
<style scoped lang="scss"></style>
