<script lang="ts" setup>
import SvgIcon from "@/apps/sidebarPopup/components/SvgIcon/SvgIcon.vue";
import Header from "./Header.vue";
import { getCurrentTab } from "@/shared/tabs";
import useSetTheme from "./hooks/useSetTheme";

const onReload = async () => {
  const tab = await getCurrentTab();
  tab.id && browser.tabs.reload(tab.id);
  window.close();
};
useSetTheme();
</script>
<template>
  <div class="bg-fill-fill-5 skywork-root-vars w-[404px] p-3" id="skywork-sidebar-root">
    <Header />
    <div class="text-text-icon-text-1 mt-4 text-[16px] leading-[24px]">Thank you for using skywork!</div>
    <div class="text-text-icon-text-3 mt-2 text-[14px] leading-[21px]">
      You need to refresh this page to make Skyworkwork.
    </div>
    <div class="mt-4 flex justify-end">
      <button
        class="text-text-icon-text-6 flex h-10 items-center justify-center rounded-xl bg-black px-4"
        @click="onReload"
      >
        <SvgIcon class="fill-text-icon-text-6 h-[18px] w-[18px]" name="ic_refresh" />
        <span class="ml-2 text-[16px]">Refresh This Page</span>
      </button>
    </div>
  </div>
</template>
<style scoped lang="scss"></style>
