<template>
  <SvgIconBase v-bind="props" :theme="theme" />
</template>

<script setup lang="ts">
import useAppStore from "@/store/app";
import { SvgIcon as SvgIconBase } from "@tg-fe/ui";
const { theme } = storeToRefs(useAppStore());
const props = defineProps({
  name: {
    type: String,
    required: true,
  },
  type: {
    type: String,
    required: false,
    default: "svg",
  },
});
</script>

<style scoped lang="scss"></style>
