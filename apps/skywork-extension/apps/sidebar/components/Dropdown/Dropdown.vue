<template>
  <el-dropdown
    v-bind="props"
    :popper-class="'skywork-dropdown-container' + (props.popperClass ? ' ' + props.popperClass : '')"
  >
    <slot />
    <template #dropdown>
      <slot name="dropdown" />
    </template>
  </el-dropdown>
</template>

<script lang="tsx" setup>
import { ElDropdown } from "element-plus";
// import useContainerDom from "@/apps/sidebar/hooks/useContainerDom";
import { DropdownProps } from "./types";
const props = defineProps<DropdownProps>();
// const container = useContainerDom();
</script>

<style lang="scss">
// .skywork-dropdown-container {
//   .el-scrollbar {
//     overflow: initial;
//     .el-scrollbar__wrap {
//       overflow: initial;
//     }
//   }
// }
</style>
