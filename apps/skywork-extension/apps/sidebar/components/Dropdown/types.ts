import { ElDropdown } from "element-plus";

// export type IDropdownProps = typeof dropdownProps;
export type IDropdownProps = InstanceType<typeof ElDropdown>["$props"];

export type DropdownProps = {
  // [K in keyof IDropdownProps]: IDropdownProps[K];
  popperClass?: IDropdownProps["popperClass"];
  trigger?: IDropdownProps["trigger"];
  placement?: IDropdownProps["placement"];
  teleported?: IDropdownProps["teleported"];
  tabindex?: IDropdownProps["tabindex"];
};
