import { getContainerElement } from "@/apps/sidebar/utils/dom";
import { DocMessage as _DocMessage, type MessageOptions } from "@tg-fe/ui";
import "./styles.scss";

const error = _DocMessage.error;
const success = _DocMessage.success;
const warning = _DocMessage.warning;

function DocMessage(...options: Parameters<typeof _DocMessage>) {
  let _options = options[0] as MessageOptions;
  if (typeof _options === "string") {
    _options = {
      message: _options,
    };
  }
  return _DocMessage({
    ..._options,
    customClass: "skywork-sidebar-doc-message",
    appendTo: getContainerElement(),
  });
}
DocMessage.error = (...options: Parameters<typeof _DocMessage.error>) => {
  let _options = options[0] as MessageOptions;
  if (typeof _options === "string") {
    _options = {
      message: _options,
    };
  }
  return error({
    ..._options,
    customClass: "skywork-sidebar-doc-message",
    appendTo: getContainerElement(),
  });
};

DocMessage.success = (...options: Parameters<typeof _DocMessage.success>) => {
  let _options = options[0] as MessageOptions;
  if (typeof _options === "string") {
    _options = {
      message: _options,
    };
  }
  return success({
    ..._options,
    customClass: "skywork-sidebar-doc-message",
    appendTo: getContainerElement(),
  });
};

DocMessage.warning = (...options: Parameters<typeof _DocMessage.warning>) => {
  let _options = options[0] as MessageOptions;
  if (typeof _options === "string") {
    _options = {
      message: _options,
    };
  }
  return warning({
    ..._options,
    customClass: "skywork-sidebar-doc-message",
    appendTo: getContainerElement(),
  });
};

export default DocMessage;
