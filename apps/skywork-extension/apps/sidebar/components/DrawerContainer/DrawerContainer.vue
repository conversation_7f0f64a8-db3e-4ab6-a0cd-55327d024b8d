<script lang="ts" setup>
import SvgIcon from "@/apps/sidebar/components/SvgIcon/SvgIcon.vue";
import Drawer from "@/apps/sidebar/components/Drawer/Drawer.vue";
type Props = {
  title: string;
};
defineProps<Props>();
const drawer = ref(false);
</script>
<template>
  <Drawer v-model="drawer" :with-header="false" size="100%" direction="rtl" destroy-on-close>
    <div class="flex h-full w-full flex-col">
      <div class="text-text-icon-text-2 mb-[30px] flex cursor-pointer items-center" @click="drawer = false">
        <SvgIcon class="h-5 w-5 fill-[--text-icon-text-2]" name="ic_drop down menu_left" />
        <span class="ml-3 text-[18px]">{{ title }}</span>
      </div>
      <div class="flex-1">
        <slot :onClose="() => (drawer = false)" />
      </div>
    </div>
  </Drawer>
  <slot name="trigger" :onOpen="() => (drawer = true)" />
</template>
<style lang="scss"></style>
