<template>
  <el-drawer
    v-bind="props"
    :append-to="container"
    :modal-class="'skywork-drawer-container page-overlay' + (props.modalClass ? ' ' + props.modalClass : '')"
  >
    <slot />
  </el-drawer>
</template>

<script lang="tsx" setup>
import type { DrawerProps } from "element-plus";
import useContainerDom from "@/apps/sidebar/hooks/useContainerDom";

const props = defineProps<Partial<DrawerProps>>();
const container = useContainerDom();
</script>

<style lang="scss">
.skywork-drawer-container {
  position: absolute !important;
  right: 0 !important;
  bottom: 0 !important;
  top: 0 !important;
  width: var(--skywork-sidebar-width);
  .el-drawer {
    box-shadow: none;
  }
}
</style>
