<script lang="ts" setup>
import { ElInput } from "element-plus";
import SvgIcon from "@/apps/sidebar/components/SvgIcon/SvgIcon.vue";
import useChatStore from "@/apps/sidebar/store/chat";
import { AgentStatus } from "@tg-fe/shared";
import { ChatSendButtonLoading } from "@tg-fe/ui";
import DocMessage from "@/apps/sidebar/components/DocMessage";
import Tooltip from "@/apps/sidebar/components/Tooltip/Tooltip.vue";
import { apiChatSseStop } from "@/api/chat";
import { $t } from "@tg-fe/i18n";

const chatStore = useChatStore();
const { agents, agentsStatus, sessionId, agentId, projectId } = storeToRefs(chatStore);
type Props = {
  onSend: (query: string) => void;
};
const props = defineProps<Props>();
const query = ref("");

const searchingUniId = computed(() => {
  const questionStatus = agentsStatus.value?.[projectId.value]?.[agentId.value]?.[sessionId.value];
  if (!questionStatus) {
    return;
  }
  return Object.keys(questionStatus).find((uniId) =>
    [AgentStatus.Send, AgentStatus.Loading, AgentStatus.Start].includes(questionStatus[uniId])
  );
});
const questionId = computed(
  () =>
    agents.value?.[projectId.value]?.[agentId.value]?.sessions?.[sessionId.value].questions.find(
      (v) => v.uni_id === searchingUniId.value
    )?.question_id
);
const isSearching = computed(() => !!searchingUniId.value);

const onEnter = (event: Event | KeyboardEvent) => {
  const e = event as KeyboardEvent;
  if (e.code == "Enter") {
    if (e.shiftKey) {
      return;
    }
    _onSend();
    e.preventDefault();
  }
};
const _onSend = () => {
  if (!query.value.trim()) return;
  if (isSearching.value) {
    DocMessage.warning($t("extension.Answering, please wait."));
    return;
  }
  props.onSend(query.value);
  nextTick(() => {
    query.value = "";
  });
};

let cancelling = false;
const onCancel = async () => {
  if (!questionId.value || !searchingUniId.value || cancelling) return;
  cancelling = true;
  try {
    await apiChatSseStop({
      session_id: sessionId.value,
      question_id: questionId.value,
    });
    chatStore.cancelMessage({
      uniId: searchingUniId.value,
    });
  } catch (e) {
    DocMessage.error($t("extension.Failed to cancel the question, please try again later."));
  } finally {
    cancelling = false;
  }
};
</script>
<template>
  <div>
    <div
      class="bg-fill-fill-5 border-line-line-2 text-text-icon-text-3 relative mt-2.5 rounded-xl border py-[--ph] pl-3 pr-2 text-[14px] leading-[0px] [--h:32px] [--ph:8px]"
      :class="{
        'pb-[calc(var(--h)+var(--ph))]': query,
      }"
    >
      <el-input
        class="input"
        v-model="query"
        :class="{
          hasVal: query,
        }"
        type="textarea"
        :autosize="{ minRows: 1, maxRows: 4 }"
        :placeholder="`${$t('extension.Make inquiries about the current page')}`"
        @keydown.stop="onEnter"
        @keyup.stop=""
      />
      <div
        class="absolute right-[--ph] flex h-[--h] w-[--h] items-center justify-center rounded-[10px]"
        :class="{
          'bg-doc-blue-blue200 top-[--ph]': !query,
          'bg-doc-blue bottom-[--ph]  cursor-pointer': query,
          '!bg-fill-fill-3-hover': isSearching,
        }"
      >
        <Tooltip v-if="isSearching" :content="$t('extension.Stop')" popper-class="whitespace-nowrap" placement="top">
          <ChatSendButtonLoading @click="onCancel" />
        </Tooltip>
        <SvgIcon class="fill-fill-fill-5 h-4 w-4" v-else name="ic_send" @click="_onSend" />
      </div>
    </div>
    <div class="text-text-icon-text-5 mt-4 text-[12px] leading-[18px]">
      {{ $t("extension.Skywork may provide inaccurate responses; please verify the information.") }}
    </div>
  </div>
</template>
<style scoped lang="scss">
.input {
  :deep(textarea) {
    box-shadow: none;
    outline: none;
    border: none;
    resize: none;
    background-color: transparent;
    padding: 0;
    line-height: var(--h);
    @apply text-text-icon-text-3;
  }

  &.hasVal {
    :deep(textarea) {
      line-height: 24px;
    }
  }
}
</style>
