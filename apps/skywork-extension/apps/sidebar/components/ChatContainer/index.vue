<script lang="ts" setup>
import useChatStore from "@/apps/sidebar/store/chat";
import { ChatBox, ChatBoxProps, ChatBoxRef } from "@tg-fe/ui";
import { AgentAction } from "@tg-fe/shared";
import Input from "./Input.vue";
import DocMessage from "@/apps/sidebar/components/DocMessage";
import useAppStore from "@/store/app";
import { $t } from "@tg-fe/i18n";

const { isDarkMode } = storeToRefs(useAppStore());
const { sendMessage, loadMore } = useChatStore();
const { questions, agents, agentsStatus } = storeToRefs(useChatStore());
const chatBoxRef = ref<ChatBoxRef>();

const chatQuestions = computed(() => {
  return questions.value?.map((question) => {
    return {
      ...question,
      status: agentsStatus.value[question.project_id]?.[question.agent_id]?.[question.session_id]?.[question.uni_id],
    };
  });
});

const onSend = (query: string) => {
  sendMessage({
    query,
    action: AgentAction.Chat,
  });
};

const onRegenerate: ChatBoxProps["onRegenerate"] = (data) => {
  console.log("===onRegenerate", data);
};
const onScrollToTop = async () => {
  await loadMore();
};

// watch(
//   () => agents.value,
//   (newVal) => {
//     console.log("===agents", JSON.parse(JSON.stringify(newVal)));
//   },
//   {
//     deep: true,
//     immediate: true,
//   }
// );
// watch(
//   () => agentsStatus.value,
//   (newVal) => {
//     console.log("===agentsStatus", JSON.parse(JSON.stringify(newVal)));
//   },
//   {
//     deep: true,
//     immediate: true,
//   }
// );
// watch(
//   () => questions.value,
//   (newVal) => {
//     console.log("===questions", JSON.parse(JSON.stringify(newVal)));
//   },
//   {
//     deep: true,
//     immediate: true,
//   }
// );
</script>
<template>
  <div class="bg-fill-fill-4 flex flex-col rounded-lg py-3">
    <ChatBox
      class="h-[400px] min-h-[395px] flex-1 px-3 [word-break:break-all]"
      :dark="isDarkMode"
      ref="chatBoxRef"
      :questions="chatQuestions"
      :onScrollToTop="onScrollToTop"
      :onRegenerate="onRegenerate"
      :texts="{
        summaryText: $t('projectDetail.chat.summary'),
        mindMapText: $t('projectDetail.chat.mindmap'),
      }"
      :mindMap="{
        onInitFail() {
          DocMessage.error($t('extension.Unable to generate the mind map'));
        },
        onStartDownload() {
          DocMessage(`${$t('extension.Saving')}...`);
        },
      }"
    />
    <Input class="px-3" @send="onSend" />
  </div>
</template>
<style scoped lang="scss"></style>
