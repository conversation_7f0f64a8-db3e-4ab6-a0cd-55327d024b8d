import { getContainerElement } from "@/apps/sidebar/utils/dom";
import { ElMessageBox, ElMessageBoxOptions, ElMessageBoxShortcutMethod } from "element-plus";
import "./styles.scss";

const MessageBox = {
  confirm: ((message, title, options) => {
    return ElMessageBox.confirm(message, title, {
      confirmButtonText: "OK",
      appendTo: getContainerElement(),
      modalClass: "skywork-message-box-container",
      ...options,
    } as any as ElMessageBoxOptions);
  }) as ElMessageBoxShortcutMethod,
};

export default MessageBox;
