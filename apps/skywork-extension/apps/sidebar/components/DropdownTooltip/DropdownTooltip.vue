<template>
  <el-tooltip
    v-bind="props"
    ref="tooltipRef"
    :tabindex="-1"
    teleported
    persistent
    :append-to="container"
    transition="skywork-dropdown-tooltip-zoom-in-top"
  >
    <slot />
    <template #content>
      <slot name="content" :onClose="() => tooltipRef?.onClose()" />
    </template>
  </el-tooltip>
</template>

<script lang="tsx" setup>
import type { ElTooltipProps } from "element-plus";
import { ElTooltip } from "element-plus";

import useContainerDom from "@/apps/sidebar/hooks/useContainerDom";

const props = defineProps<Partial<ElTooltipProps>>();
const container = useContainerDom();
const tooltipRef = ref<InstanceType<typeof ElTooltip>>();
</script>

<style lang="scss">
.skywork-dropdown-tooltip-zoom-in-top-enter-active,
.skywork-dropdown-tooltip-zoom-in-top-leave-active {
  transition: transform 0.15s ease-out;
  transform-origin: center top;
  transition: getCssVar("transition-md-fade");
  &[data-popper-placement^="top"] {
    transform-origin: center bottom;
  }
}
.skywork-dropdown-tooltip-zoom-in-top-enter-from,
.skywork-dropdown-tooltip-zoom-in-top-leave-to {
  transform: scaleY(0);
}
</style>
