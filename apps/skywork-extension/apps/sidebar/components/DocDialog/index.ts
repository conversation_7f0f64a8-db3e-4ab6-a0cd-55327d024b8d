import { getContainerElement } from "@/apps/sidebar/utils/dom";
import { zIndex } from "@/constants/app";
import { $t } from "@tg-fe/i18n";
import { VueRenderer } from "@tg-fe/shared";
import { DocDialog } from "@tg-fe/ui";
import "./styles.scss";

type Props = InstanceType<typeof DocDialog>["$props"];
type Ref = InstanceType<typeof DocDialog>;
export const openDialog = (
  props: Partial<Props> & {
    onConfirm?: (next: () => void) => void;
    onCancel?: () => void;
  }
) => {
  const close = () => {
    renderer?.destroy();
    if (renderer?.element) {
      if (document.body.contains(renderer.element)) {
        document.body.removeChild(renderer.element);
      }
      renderer = null;
    }
  };
  let renderer: VueRenderer<Props, Ref> | null = new VueRenderer(DocDialog, {
    props: {
      visible: true,
      closeHandle: close,
      showClose: true,
      modalClass: "skywork-sidebar-doc-dialog",
      footerBtnConfig: [
        {
          text: $t("extension.Cancel"),
          props: {
            class: "w-[120px] h-10",
            link: true,
          },
          click: close,
        },
        {
          type: "primary",
          text: $t("extension.Confirm"),
          props: {
            class: "rounded-[12px] w-[120px] h-10",
          },
          click: () => props.onConfirm?.(close),
        },
      ],
      ...props,
      dialogConfig: {
        appendTo: getContainerElement(),
        zIndex: zIndex,
        ...props.dialogConfig,
      },
    },
  });
  renderer.element && document.body.appendChild(renderer.element);
};
