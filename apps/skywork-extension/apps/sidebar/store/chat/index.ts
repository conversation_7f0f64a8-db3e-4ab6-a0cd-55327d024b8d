import { apiUrls } from "@/api/apiUrls";
import { apiChatHistory, apiChatSessionIdGet } from "@/api/chat";
import { track } from "@/apps/sidebar/utils/track";
import { sendSseRequest } from "@/shared/request";
import useAppStore from "@/store/app";
import { STORE_KEYS } from "@/store/constants";
import { useUserStore } from "@/store/user";
import { AgentAction, AgentCard, AgentCardType, AgentId, AgentParams, AgentSource, useChat } from "@tg-fe/shared";
import { defineStore } from "pinia";
import useAnalysisUrl from "./useAnalysisUrl";

const useChatStore = defineStore(STORE_KEYS.chat, () => {
  const { userInfo } = storeToRefs(useUserStore());
  const { tab, appId } = storeToRefs(useAppStore());

  const agentId = ref(AgentId.Plugin);
  const projectId = ref("plugin");
  const sessionId = ref("");
  const loadingChatHistory = ref(true);

  const { currentSite, analysisUrl, analysisField, updateSiteStatus, resetSiteUrlData } = useAnalysisUrl();
  const questions = computed(
    () => agents.value[projectId.value]?.[agentId.value].sessions?.[sessionId.value]?.questions
  );

  const objects = computed(() => [
    {
      type: "website_url" as const,
      id: currentSite.value?.analysisData?.file_id as string,
      url: tab.value?.url as string,
      name: tab.value?.url as string,
    },
  ]);

  const getChatParams = () => ({
    agentId: agentId.value,
    projectId: projectId.value,
    sessionId: sessionId.value,
  });

  const {
    agentsTaskStatus,
    agents,
    agentsStatus,
    sendMessage,
    getAgentSessionHistory,
    hasAgentSessionHistory,
    load,
    loadMore,
    cancelMessage,
  } = useChat({
    sendMessage: async (params, requestOptions) => {
      const _params = {
        agent_id: agentId.value,
        session_id: sessionId.value,
        source: AgentSource.Plugin,
        objects: objects.value,
        ...params,
      };
      const res = await sendSseRequest<AgentCard>(
        {
          ...requestOptions,
          url: apiUrls.creation.chatSse,
          onmessage: (ev) => {
            requestOptions?.onmessage?.(ev);
            if (ev.data.card_type === AgentCardType.First) {
              sessionId.value = ev.data.session_id!;
              track.extension_chat_send_click({
                question_id: ev.data.question_id,
              });
            }
          },
          data: _params,
        },
        {
          appId: appId.value,
        }
      );
      return {
        ...res,
        data: {
          agent_id: agentId.value,
          project_id: projectId.value,
          session_id: sessionId.value,
        },
        params: _params as AgentParams,
      };
    },
    async load(params) {
      if (hasAgentSessionHistory(getChatParams())) {
        return;
      }
      const res = await apiChatHistory({
        session_id: sessionId.value,
        source: AgentSource.Plugin,
        ...params,
      });
      return {
        agent_id: agentId.value,
        project_id: projectId.value,
        fetchData: res,
      };
    },
    async loadMore(params) {
      const data = getAgentSessionHistory(getChatParams());
      if (data && !data.has_more) return;
      const res = await apiChatHistory({
        session_id: sessionId.value,
        source: AgentSource.Plugin,
        offset: data?.offset,
        ...params,
      });
      return {
        agent_id: agentId.value,
        project_id: projectId.value,
        fetchData: res,
      };
    },
  });

  /** 发送摘要跟脑图 */
  const sendSummaryAndMindMap = async (url: string) => {
    if (url !== tab.value?.url) return;
    sendMessage(
      {
        action: AgentAction.Summary,
        objects: objects.value,
      },
      {
        noAddUserCard: true,
        noAddBotLoadingCard: true,
        onFirstMessage: ({ session_id }) => {
          sessionId.value = session_id;
          sendMessage(
            {
              action: AgentAction.MindMap,
              objects: objects.value,
            },
            {
              noAddUserCard: true,
              noAddBotLoadingCard: true,
            }
          );
        },
      }
    );
  };

  // 获取sessionId
  const getChatSessionIdGet = async () => {
    const res = await apiChatSessionIdGet({
      agent_id: agentId.value,
      source: AgentSource.Plugin,
      file_url: tab.value?.url as string,
    });
    sessionId.value = res?.session_id;
    return res;
  };

  const analysisFieldOrUrl = async (url: string, file_id?: string) => {
    if (file_id) {
      // 解析文件
      await analysisField(file_id, url, () => analysisUrl(url));
    } else {
      await analysisUrl(url);
    }
  };

  const loadInitHistory = async () => {
    await load();
    const data = getAgentSessionHistory(getChatParams());
    return !!data?.questions.length;
  };

  // 加载基础数据
  const loadData = async (url?: string) => {
    if (!url) return;
    const load = async (fns: Promise<any>[]) => {
      try {
        const [_, hasChatData] = await Promise.all(fns);
        updateSiteStatus(url, {
          status: "success",
        });
        if (!hasChatData) {
          sendSummaryAndMindMap(url);
        }
      } catch (e) {
        updateSiteStatus(url, {
          status: "fail",
        });
      } finally {
        loadingChatHistory.value = false;
      }
    };
    loadingChatHistory.value = true;
    // 获取用户聊天基础信息
    getChatSessionIdGet()
      .then(async (res) => {
        // 存在知识库
        if (res.has_knowledge) {
          updateSiteStatus(url, {
            isFinish: res.has_knowledge,
          });
        }
        // 解析url & 获取历史记录
        load([analysisFieldOrUrl(url, res.file_id), loadInitHistory()]);
      })
      .catch(async () => {
        sessionId.value = "";
        load([analysisFieldOrUrl(url)]);
      });
  };

  const onReloadData = () => {
    resetSiteUrlData(tab.value?.url);
    loadData(tab.value?.url);
  };

  watch(
    () => userInfo.value?.email,
    (email) => {
      if (email) {
        loadData(tab.value?.url);
      }
    },
    {
      immediate: true,
    }
  );

  watch(
    tab,
    (tab) => {
      if (!userInfo.value?.email) return;
      loadData(tab?.url);
    },
    {
      immediate: true,
    }
  );

  return {
    agentsTaskStatus,
    agentId,
    projectId,
    sessionId,
    agents,
    agentsStatus,
    loadingChatHistory,
    questions,
    currentSite,
    getChatSessionIdGet,
    sendMessage,
    cancelMessage,
    loadMore,
    updateSiteStatus,
    onReloadData,
  };
});
export default useChatStore;
