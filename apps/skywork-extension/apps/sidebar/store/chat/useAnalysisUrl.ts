import {
  apiUploadAnalysisSimplify,
  apiUploadAnalysisSimplifyRes,
  apiUploadGetTask,
  apiUploadProgressParams,
} from "@/api/mulit";
import { track } from "@/apps/sidebar/utils/track";
import useAppStore from "@/store/app";

const sleep = (time = 3000) => new Promise((resolve) => setTimeout(resolve, time));

interface Site {
  data: apiUploadAnalysisSimplifyRes;
  analysisData?: FileResult;
  isFinish?: boolean;
  status?: "loading" | "success" | "fail";
}
interface analysisOptions {
  failCallback: () => Promise<void>;
  url: string;
}

const useAnalysisUrl = () => {
  let unMounted = false;
  const { tab, appId } = storeToRefs(useAppStore());
  const siteUrl = computed(() => tab.value?.url);
  const sites = ref({} as Record<string, Site | undefined>);
  const failRetryUrls = ref({} as Record<string, number>);
  const currentSite = computed(() => (siteUrl.value ? sites.value[siteUrl.value] : undefined));

  /** 更新状态 */
  const updateSiteStatus = (url: string, data: Partial<Site>) => {
    sites.value = {
      ...sites.value,
      [url]: {
        ...(sites.value[url] as Site),
        ...data,
      },
    };
  };

  const analysisProgress = async (params: apiUploadProgressParams, options: analysisOptions) => {
    const res = await apiUploadGetTask(params, {
      appId: appId.value,
    });
    const info = res.list[0];
    if (unMounted) {
      throw new Error("App unMounted");
    }
    if (siteUrl.value !== options.url) {
      throw new Error("Url Changed");
    }
    /* 
      任务状态 1:上传中 2:上传失败 3:上传成功解析中 4:解析成功 5:解析失败 6:重试中
    */
    if (!info || [3].includes(info.status)) {
      await sleep();
      return await analysisProgress(params, options);
    }
    if (info.status === 4) {
      return info;
    }
    const key = options.url;
    if (!failRetryUrls.value[key] || failRetryUrls.value[key] >= 0) {
      throw new Error("Parsing failed, please try again later");
    }
    failRetryUrls.value = {
      ...failRetryUrls.value,
      [key]: (failRetryUrls.value[key] ?? 0) + 1,
    };
    await options.failCallback();
  };

  const analysisUrl = async (url?: string) => {
    if (!url) return;
    let startTime = Date.now();
    let data: apiUploadAnalysisSimplifyRes | undefined;
    const createCommonParams = () => ({
      file_id: data?.file_id || "",
      file_name: data?.file_name || "",
      file_size: data?.file_size || 0,
      file_type: data?.file_type || "",
      upload_from: "extension",
      file_from: "website",
      upload_time: parseFloat(((Date.now() - startTime) / 1000).toFixed(2)),
    });
    try {
      const res = await apiUploadAnalysisSimplify({
        request_type: 2,
        business_source: 1,
        page_source: 0,
        file_list: [
          {
            file_name: url,
            file_type: "website_url",
            outside_url: url,
            file_size: 1,
          },
        ],
      });
      data = res.analysis_result_list[0];
      sites.value = {
        ...sites.value,
        [url]: {
          data,
        },
      };
      track.extension_uploading_state({
        ...createCommonParams(),
        state: 1,
      });
    } catch (e) {
      track.extension_uploading_state({
        ...createCommonParams(),
        state: 0,
      });
    }
    if (data) {
      // 轮询解析
      await analysisField(data.file_id, url, async () => {
        await analysisUrl(url);
      });
    }
  };

  const analysisField = async (file_id: string, url: string, failCallback: () => Promise<void>) => {
    // 轮询解析
    const analysisData = await analysisProgress(
      {
        file_ids: [file_id],
      },
      {
        failCallback,
        url,
      }
    );
    if (analysisData) {
      sites.value = {
        ...sites.value,
        [url]: {
          ...(sites.value[url] as Site),
          analysisData,
        },
      };
    }
  };

  const resetSiteUrlData = (url?: string) => {
    if (!url) return;
    sites.value = {
      ...sites.value,
      [url]: undefined,
    };
  };

  onUnmounted(() => {
    unMounted = true;
  });

  return {
    sites,
    currentSite,
    analysisUrl,
    analysisField,
    updateSiteStatus,
    resetSiteUrlData,
  };
};
export default useAnalysisUrl;
