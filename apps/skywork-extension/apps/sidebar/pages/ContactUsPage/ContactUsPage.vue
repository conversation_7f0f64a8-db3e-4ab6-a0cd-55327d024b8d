<script lang="ts" setup>
import { track } from "@/apps/sidebar/utils/track";
import { UploadUserFile } from "element-plus";
import { useUserStore } from "@/store/user";
import { apiFeedbackDistribute, FeedbackTemplateType, FeedbackDistribute, apiFeedbackReport } from "@/api/user";
import DocMessage from "@/apps/sidebar/components/DocMessage";
import { APP_FEEDBACK_EMAIL } from "@/constants/app";
import LoadingWrapper from "@/apps/sidebar/components/LoadingWrapper/LoadingWrapper.vue";
import Upload from "./Upload.vue";
import { $t } from "@tg-fe/i18n";

const props = defineProps<{
  onClose?: () => void;
}>();
const LIMIT = 3;
const { userInfo } = useUserStore();
const loading = ref(false);
const question = ref("");
const fileList = ref([] as UploadUserFile[]);

const feedbackDistribute = ref<FeedbackDistribute>();
const textQuestion = computed(() => feedbackDistribute.value?.questions[0]);
const imageQuestion = computed(() => feedbackDistribute.value?.questions[1]);

const getFeedbackDistribute = async () => {
  loading.value = true;
  try {
    const res = await apiFeedbackDistribute(FeedbackTemplateType.Contactus);
    feedbackDistribute.value = res;
  } finally {
    loading.value = false;
  }
};
getFeedbackDistribute();
const onConfirm = async () => {
  const answer = {
    source: "plugin",
    template_id: feedbackDistribute.value?.template_id,
    answers: [
      {
        question_id: textQuestion.value?.question_id,
        answer_data: [
          {
            answer: question.value,
          },
        ],
      },
      {
        question_id: imageQuestion.value?.question_id,
        answer_data: fileList.value.map((item: Record<string, any>) => ({
          answer: item.file_no,
        })),
      },
    ],
  };
  loading.value = true;
  try {
    await apiFeedbackReport(answer);
    setTimeout(() => {
      props.onClose?.();
      DocMessage.success($t("extension.Feedback submitted"));
    }, 500);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  track.extension_contact_us_show();
});
</script>
<template>
  <LoadingWrapper :loading="loading">
    <div class="flex h-full flex-col">
      <div class="flex-1">
        <el-input
          class="input"
          v-if="textQuestion"
          v-model="question"
          :rows="4"
          type="textarea"
          :placeholder="$t('contactUs.describeDetail')"
        />
        <div class="mt-5" v-if="imageQuestion">
          <p class="text-text-icon-text-2 text-[14px] leading-[21px]">{{ $t("contactUs.uploadImage") }}</p>
          <Upload class="mt-2" v-model="fileList" :limit="LIMIT" />
        </div>
      </div>
      <div class="mt-[30px] flex items-center justify-between">
        <p class="text-text-icon-text-5 text-[12px]">
          {{ $t("extension.You can also contact us by email: ") }}{{ APP_FEEDBACK_EMAIL }}
        </p>
        <button
          class="text-text-icon-text-6 ml-2 flex h-10 w-[120px] items-center justify-center rounded-xl text-[16px]"
          :class="{
            'bg-black': question,
            'bg-fill-fill-1': !question,
          }"
          @click="onConfirm"
        >
          {{ $t("extension.Confirm") }}
        </button>
      </div>
    </div>
  </LoadingWrapper>
</template>
<style scoped lang="scss">
.input {
  :deep(textarea) {
    box-shadow: none;
    outline: none;
    resize: none;
    background-color: transparent;
    padding: 0;
    @apply bg-fill-fill-5 border-line-line-3 text-text-icon-text-3 rounded-lg border border-solid px-3 py-2 pb-5 text-[15px];
  }
}
</style>
