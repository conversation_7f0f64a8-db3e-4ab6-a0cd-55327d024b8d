<script lang="ts" setup>
import { UploadRawFile, UploadFile, UploadRequestOptions, UploadUserFile } from "element-plus";
import SvgIcon from "@/apps/sidebar/components/SvgIcon/SvgIcon.vue";
import DocMessage from "@/apps/sidebar/components/DocMessage";
import { uploadImageToOss } from "@/apps/sidebar/utils/upload";
import { BusinessFileType } from "@/api/infra";
import { $t } from "@tg-fe/i18n";
type Props = {
  modelValue: UploadUserFile[];
  limit?: number;
};

const emits = defineEmits<{
  (e: "update:modelValue", value: UploadUserFile[]): void;
}>();

const props = defineProps<Props>();
const fileList = ref([] as UploadUserFile[]);

const onExceed = () => {
  DocMessage.warning($t("extension.Upload up to {limit} pictures", { limit: props.limit }));
};

const setFileState = (uid: number, type: string, value: string) => {
  const file = fileList.value.map((f) => {
    if (f.uid === uid) {
      const data = {
        ...f,
      } as Record<string, any>;
      data[type] = value;
      return data;
    }
    return f;
  });
  fileList.value = file as UploadUserFile[];
};
const getFileInfo = (file: UploadRawFile) => {
  const [name, type] = file.name.split(".");
  return {
    file_name: name,
    file_size: file.size || 0,
    file_type: type,
  };
};

const handleRemove = (file: UploadFile, indexToRemove: number) => {
  console.log(file, indexToRemove);
  fileList.value = fileList.value.filter((_, index) => index !== indexToRemove);
};

watch(fileList, (value) => {
  emits("update:modelValue", value);
});

const onHttpRequest = async (option: UploadRequestOptions) => {
  const fileUid = option.file.uid;
  setFileState(fileUid, "status", "uploading");
  const res = await uploadImageToOss(option.file, {
    ...getFileInfo(option.file),
    business_file_type: BusinessFileType.user_head_picture,
    expire_time: -1,
  });
  const imageUrl = res.file_expire_url;
  setFileState(fileUid, "status", "done");
  setFileState(fileUid, "url", imageUrl);
  setFileState(fileUid, "file_no", res.file_no);
};
</script>
<template>
  <el-upload
    class="upload"
    v-model:file-list="fileList"
    list-type="picture-card"
    multiple
    accept=".jpg,.jpeg,.png,.webp"
    :limit="props.limit"
    :on-exceed="onExceed"
    :class="{ 'max-size': fileList.length >= 3 }"
    :http-request="onHttpRequest"
  >
    <SvgIcon class="fill-text-icon-text-4 h-5 w-5" name="ic_add" />
    <template #file="{ file, index }">
      <div>
        <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
        <div class="upload-loading" v-if="file.status === 'uploading'">
          <SvgIcon class="loading-btn-animation inline-block h-[32px] w-[32px]" name="ic_loading"></SvgIcon>
        </div>
        <span class="upload-del" @click="handleRemove(file, index)">
          <SvgIcon class="h-[8px] w-[8px] fill-white" name="ic_close" />
        </span>
      </div>
    </template>
  </el-upload>
</template>
<style scoped lang="scss">
.upload {
  &.max-size {
    :deep(.el-upload--picture-card) {
      display: none;
    }
  }
  :deep(.el-upload--picture-card) {
    border-radius: var(---S);
    border: 1px solid var(--line-line-3);
    background: var(--white);
    width: 80px;
    height: 80px;
    border-radius: 8px;
  }
  :deep(.el-upload-list__item) {
    width: 80px;
    height: 80px;
    border-radius: 8px;
    position: relative;
  }
  .el-upload-list__item-thumbnail {
    width: 80px;
    height: 80px;
    -o-object-fit: cover;
    object-fit: cover;
    border-radius: 8px; // cursor: pointer;
  }
}
.upload-del {
  width: 16px;
  height: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  position: absolute;
  top: 2px;
  right: 2px;
  background: var(--background-overlay-1);
  border-radius: 50%;
}

.upload-loading {
  position: absolute;
  top: 0;
  left: 0;
  background: var(--else-black-40, rgba(0, 8, 24, 0.4));
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-btn-animation {
  animation: rotateAnimation 1s linear infinite; /* 持续时间2秒，线性速度，无限次播放 */
}

/* 定义旋转动画 */
@keyframes rotateAnimation {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
