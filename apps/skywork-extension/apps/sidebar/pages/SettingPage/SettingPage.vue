<script lang="ts" setup>
import { track } from "@/apps/sidebar/utils/track";
import { useUserStore } from "@/store/user";
import useAppStore from "@/store/app";
import { ThemeMode, ThemeType } from "@/types";
import lightSystemImg from "@/assets/images/light-theme-system.svg";
import lightLightImg from "@/assets/images/light-theme-light.svg";
import lightDarkImg from "@/assets/images/light-theme-dark.svg";
import darkSystemImg from "@/assets/images/dark-theme-system.svg";
import darkLightImg from "@/assets/images/dark-theme-light.svg";
import darkDarkImg from "@/assets/images/dark-theme-dark.svg";
import { $t } from "@tg-fe/i18n";

const userStore = useUserStore();
const { userSetting } = storeToRefs(userStore);
const { isDarkMode } = storeToRefs(useAppStore());

const list = computed(() => {
  return [
    {
      title: $t("extension.Follow system settings"),
      value: ThemeType.System,
      image: isDarkMode.value ? darkSystemImg : lightSystemImg,
    },
    {
      title: $t("extension.Light mode"),
      value: ThemeType.Light,
      image: isDarkMode.value ? darkLightImg : lightLightImg,
    },
    { title: $t("extension.Dark mode"), value: ThemeType.Dark, image: isDarkMode.value ? darkDarkImg : lightDarkImg },
  ];
});

const onClick = (item: { value: number }) => {
  const appearances = {
    [ThemeType.System]: "system",
    [ThemeType.Light]: "light",
    [ThemeType.Dark]: "dark",
  } as Record<number, string>;
  track.extension_appearance_setting_click({
    appearance: appearances[item.value],
  });
  userStore.updateUserSetting({
    plugin_theme: item.value,
  });
};

onMounted(() => {
  track.extension_appearance_show();
});
</script>
<template>
  <div>
    <h3 class="text-text-icon-text-1 text-[16px] leading-[24px]">{{ $t("extension.Appearance") }}</h3>
    <div class="mt-5 flex justify-between">
      <div class="cursor-pointer" v-for="item in list" :key="item.value" @click="() => onClick(item)">
        <div
          class="relative rounded-lg border border-[transparent] p-1"
          :class="{
            '!border-black': userSetting?.plugin_theme === item.value,
          }"
        >
          <img class="w-[100px]" :src="item.image" />
        </div>
        <div class="text-text-icon-text-5 mt-1.5 text-[12px]">{{ item.title }}</div>
      </div>
    </div>
  </div>
</template>
<style scoped lang="scss">
.select {
  @apply absolute bottom-2 left-2 h-2.5 w-2.5;
}
</style>
