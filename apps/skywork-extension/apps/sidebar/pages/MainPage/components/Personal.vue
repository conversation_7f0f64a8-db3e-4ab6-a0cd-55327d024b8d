<script lang="ts" setup>
import SvgIcon from "@/apps/sidebar/components/SvgIcon/SvgIcon.vue";
import DrawerContainer from "@/apps/sidebar/components/DrawerContainer/DrawerContainer.vue";
import SettingPage from "@/apps/sidebar/pages/SettingPage/SettingPage.vue";
import ContactUsPage from "@/apps/sidebar/pages/ContactUsPage/ContactUsPage.vue";
import { useUserStore } from "@/store/user";
import { storeToRefs } from "pinia";
import { openDialog } from "@/apps/sidebar/components/DocDialog";
import { apiUserLogout } from "@/api/user";
import { $t } from "@tg-fe/i18n";
const props = defineProps<{
  onClose?: () => void;
}>();
const { userInfo } = storeToRefs(useUserStore());
const onLogout = () => {
  openDialog({
    title: $t("extension.Please confirm if you want to log out."),
    async onConfirm(next) {
      await apiUserLogout();
      next();
    },
  });
};
</script>
<template>
  <div class="py-5 pl-3 pr-6">
    <div class="flex">
      <div class="h-[52px] w-[52px]">
        <img class="h-full w-full rounded-[50%]" :src="userInfo?.avatar_url" />
      </div>
      <div class="ml-3 flex flex-1 flex-col justify-center gap-1">
        <div class="text-text-icon-text-1 text-[18px] leading-[27px]">{{ userInfo?.nick_name }}</div>
        <div class="text-text-icon-text-4 text-[14px] leading-[21px]">{{ userInfo?.email }}</div>
      </div>
    </div>
    <div class="mt-5 flex flex-col gap-y-2">
      <DrawerContainer :title="$t('extension.Settings')">
        <template #trigger="{ onOpen }">
          <div
            class="item"
            @click="
              () => {
                onOpen();
                props.onClose?.();
              }
            "
          >
            <SvgIcon class="icon fill-[--text-icon-text-2]" name="ic_setting" />
            <span>{{ $t("extension.Settings") }}</span>
          </div>
        </template>
        <SettingPage />
      </DrawerContainer>
      <DrawerContainer :title="$t('extension.Contact us')">
        <template #trigger="{ onOpen }">
          <div
            class="item"
            @click="
              () => {
                onOpen();
                props.onClose?.();
              }
            "
          >
            <SvgIcon class="icon fill-[--text-icon-text-2]" name="ic_contact us" />
            <span>{{ $t("extension.Contact us") }}</span>
          </div>
        </template>
        <template #default="{ onClose }">
          <ContactUsPage :onClose="onClose" />
        </template>
      </DrawerContainer>
      <div class="item !text-red-red500" @click="onLogout">
        <SvgIcon class="icon fill-[--red-red500]" name="ic_log out" />
        <span>{{ $t("extension.Log out") }}</span>
      </div>
    </div>
  </div>
</template>
<style scoped lang="scss">
.item {
  @apply text-text-icon-text-2 hover:text-text-icon-text-1 flex h-9 cursor-pointer items-center px-3 py-2 text-[14px] leading-[20px];
  .icon {
    @apply mr-1.5 h-4 w-4;
  }

  &:hover {
    @apply bg-fill-fill-3-hover rounded-[10px];
  }
}
</style>
