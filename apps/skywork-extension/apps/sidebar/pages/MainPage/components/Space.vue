<script lang="ts" setup>
import SvgIcon from "@/apps/sidebar/components/SvgIcon/SvgIcon.vue";
import { track } from "@/apps/sidebar/utils/track";
import siteLinkIcon from "@/assets/images/site-link.svg";
import { APP_SPACE_PAGE } from "@/constants/app";
import useChatStore from "@/apps/sidebar/store/chat";
import { apiInformationSourceAddToKnowledge } from "@/api/mulit";
import DocMessage from "@/apps/sidebar/components/DocMessage";
import useAppStore from "@/store/app";
import { $t } from "@tg-fe/i18n";

const { updateSiteStatus } = useChatStore();
const { tab } = storeToRefs(useAppStore());
const { currentSite } = storeToRefs(useChatStore());
const loading = ref(false);

const onSave = async () => {
  const url = tab.value?.url;
  if (!url || loading.value) return;
  track.extension_save_button_click();
  loading.value = true;
  try {
    await apiInformationSourceAddToKnowledge({
      file_ids: [currentSite.value?.analysisData?.file_id as string],
    });
    DocMessage.success($t("extension.Saved"));
    updateSiteStatus(tab.value?.url || "", {
      isFinish: true,
    });
  } catch (err: any) {
    DocMessage.error(err?.response?.data?.msg);
  } finally {
    loading.value = false;
  }
};
const onOpen = () => {
  const fileId = currentSite.value?.analysisData?.file_id;
  if (fileId) {
    track.extension_view_button_click();
    window.open(`${APP_SPACE_PAGE}`);
  }
};
</script>
<template>
  <div class="bg-fill-fill-4 mt-[30px] rounded-lg p-3">
    <div class="flex items-center">
      <div class="text-text-icon-text-2 flex min-w-0 flex-1 items-center">
        <img class="mr-1.5" :src="siteLinkIcon" />
        <div class="ellipsis">
          {{ currentSite?.analysisData?.analysis_result?.parsed_title || tab?.title }}
        </div>
      </div>
      <div
        class="text-doc-blue ml-4 flex cursor-pointer items-center"
        v-if="currentSite?.analysisData?.file_id && !currentSite?.isFinish"
        @click="onSave"
      >
        <SvgIcon class="h-4 w-4 animate-spin" v-if="loading" name="ic_loading" />
        <SvgIcon class="fill-doc-blue h-4 w-4" v-else name="ic_folder_line" />
        <div class="ml-1 text-[14px]">{{ $t("extension.Save to knowledge base") }}</div>
      </div>
      <div class="text-doc-blue ml-4 flex cursor-pointer items-center" v-if="currentSite?.isFinish" @click="onOpen">
        <SvgIcon class="fill-doc-blue h-4 w-4" name="ic_folder_line" />
        <div class="ml-1 text-[14px]">{{ $t("extension.View in knowledge base") }}</div>
      </div>
    </div>
    <p
      class="text-text-icon-text-5 ellipsis-2 mt-2 text-[12px] leading-[18px]"
      v-if="currentSite?.analysisData?.analysis_result?.summary"
    >
      {{ currentSite?.analysisData?.analysis_result?.summary }}
    </p>
  </div>
</template>
<style scoped lang="scss"></style>
