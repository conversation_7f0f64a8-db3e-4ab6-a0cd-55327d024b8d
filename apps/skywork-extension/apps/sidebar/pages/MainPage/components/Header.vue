<script lang="ts" setup>
import SvgIcon from "@/apps/sidebar/components/SvgIcon/SvgIcon.vue";
import Dropdown from "@/apps/sidebar/components/Dropdown/Dropdown.vue";
import logo from "@/assets/images/logo-text.png";
import logoDark from "@/assets/images/logo-text-dark.png";
import Personal from "./Personal.vue";
import { APP_PAGE } from "@/constants/app";
import { useUserStore } from "@/store/user";
import useAppStore from "@/store/app";
import { ThemeMode } from "@/types";

const { userInfo } = storeToRefs(useUserStore());
const { theme, instanceApp } = storeToRefs(useAppStore());

const goHome = () => {
  window.open(APP_PAGE);
};

const onClose = () => {
  instanceApp.value.onCloseApp();
};
</script>
<template>
  <div class="flex items-center justify-between">
    <img class="h-[24px] cursor-pointer" :src="theme === ThemeMode.Dark ? logoDark : logo" alt="logo" @click="goHome" />
    <div class="text-text-icon-text-2 flex items-center gap-x-5">
      <Dropdown
        v-if="userInfo"
        placement="bottom"
        trigger="click"
        :teleported="false"
        :tabindex="-1"
        popper-class="main-header-action-dropdown"
      >
        <SvgIcon class="h-5 w-5 cursor-pointer fill-[--text-icon-text-2] [outline:none]" name="ic_setting" />
        <template #dropdown>
          <Personal />
        </template>
      </Dropdown>
      <SvgIcon class="h-5 w-5 cursor-pointer fill-[--text-icon-text-2]" name="ic_close2" @click="onClose" />
    </div>
  </div>
</template>
<style lang="scss">
.main-header-action-dropdown {
  border: none !important;
  box-shadow: 0px 3px 12px 0px rgba(0, 8, 24, 0.12) !important;
  background-color: var(--fill-fill-5) !important;
  padding: 0 !important;
  .el-popper__arrow {
    display: none;
  }
}
</style>
