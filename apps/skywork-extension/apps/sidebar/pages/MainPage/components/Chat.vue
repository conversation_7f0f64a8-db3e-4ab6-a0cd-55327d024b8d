<script lang="tsx" setup>
import useChatStore from "@/apps/sidebar/store/chat";
import ChatContainer from "@/apps/sidebar/components/ChatContainer/index.vue";
import loadingStaticImg from "@/assets/images/loading-static.png";
import loadingStaticDarkImg from "@/assets/images/loading-static-dark.png";
import SvgIcon from "@/apps/sidebar/components/SvgIcon/SvgIcon.vue";
import useAppStore from "@/store/app";
import { LoadingContainer, LoadingProgressBar } from "@tg-fe/ui";
import { $t } from "@tg-fe/i18n";

const Loading = defineComponent({
  setup() {
    return () => <LoadingProgressBar text={`${$t("extension.Analysing")}...`} isDark={isDarkMode.value} />;
  },
});

const chatStore = useChatStore();
const { currentSite } = storeToRefs(chatStore);
const { isDarkMode } = storeToRefs(useAppStore());
const isSuccess = computed(() => currentSite.value?.status === "success");
const isFail = computed(() => currentSite.value?.status === "fail");
</script>
<template>
  <LoadingContainer :loading="!isSuccess && !isFail" :component="Loading">
    <div class="mt-4 flex min-h-0 flex-1 flex-col">
      <template v-if="isSuccess">
        <div class="text-text-icon-text-1 text-[16px]">{{ $t("extension.Page Chat") }}</div>
        <ChatContainer class="mt-3 min-h-0 flex-1" />
      </template>
      <template v-if="isFail">
        <div class="flex flex-1 flex-col items-center justify-center">
          <div class="text-center">
            <img
              class="inline-block h-[100px] w-[100px]"
              :src="isDarkMode ? loadingStaticDarkImg : loadingStaticImg"
              alt=""
            />
            <div class="text-text-icon-text-3 mt-3 text-[14px] leading-[150%]">
              {{ $t("extension.Parsing failed.") }}
            </div>
            <div
              class="text-text-icon-text-6 mt-4 flex h-[40px] w-[120px] cursor-pointer items-center justify-center rounded-xl bg-black"
              @click="chatStore.onReloadData"
            >
              <SvgIcon class="fill-text-icon-text-6 h-[18px] w-[18px]" name="ic_refresh" />
              <span class="ml-2 text-[14px]">{{ $t("extension.Retry") }}</span>
            </div>
          </div>
        </div>
      </template>
    </div>
  </LoadingContainer>
</template>
<style scoped lang="scss"></style>
