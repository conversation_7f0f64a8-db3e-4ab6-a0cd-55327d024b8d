<script lang="ts" setup>
import { track } from "@/apps/sidebar/utils/track";
import Header from "./components/Header.vue";
import Space from "./components/Space.vue";
import Chat from "./components/Chat.vue";
onMounted(() => {
  track.extension_home_page_show();
});
</script>
<template>
  <div class="flex h-full w-full flex-col px-4 py-5">
    <Header />
    <Space />
    <Chat />
  </div>
</template>
<style scoped lang="scss"></style>
