<script lang="ts" setup>
import { APP_LOGIN_PAGE } from "@/constants/app";
import logo from "@/assets/images/logo.svg";
import { track } from "@/apps/sidebar/utils/track";
import { $t } from "@tg-fe/i18n";

const onLogin = () => {
  track.extension_login_button_click();
  window.open(APP_LOGIN_PAGE);
};
onMounted(() => {
  track.extension_login_page_show();
});
</script>
<template>
  <div class="flex h-full w-full flex-col items-center justify-center pb-[120px]">
    <img class="w-[80px]" :src="logo" alt="logo" />
    <div class="mt-4 text-[24px] leading-[150%] text-black">{{ $t("extension.Welcome to Skywork") }}</div>
    <button class="text-text-icon-text-6 mt-[30px] h-[46px] w-[180px] rounded-xl bg-black text-[20px]" @click="onLogin">
      {{ $t("extension.Log in") }}
    </button>
  </div>
</template>
<style scoped lang="scss"></style>
