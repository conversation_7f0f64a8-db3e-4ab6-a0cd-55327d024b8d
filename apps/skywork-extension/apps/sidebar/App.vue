<script lang="ts" setup>
import useSetTheme from "@/apps/sidebar/hooks/useSetTheme";
import useDrag from "@/apps/sidebar/hooks/useDrag";
import MainPage from "./pages/MainPage/MainPage.vue";
import { useUserStore } from "@/store/user";
import { useNetworkStatus } from "@/hooks/useNetworkStatus";
import LoginPage from "./pages/LoginPage/LoginPage.vue";
import useAppStore from "@/store/app";
import { AppProps } from "./types";

const props = defineProps<AppProps>();
const { token, hasAuth } = storeToRefs(useUserStore());
const { updatePublicUrl, updateCloseAppFn } = useAppStore();
updatePublicUrl(props.publicUrl);
updateCloseAppFn(props.onCloseApp);
useSetTheme();
useNetworkStatus();
const { dragRef, containerRef } = useDrag();
</script>

<template>
  <div
    class="bg-fill-fill-5 skywork-root-vars fixed right-0 top-0 h-full w-[--skywork-sidebar-width] [box-shadow:-1px_0_1px_#919eab3d]"
    id="skywork-sidebar-root"
    ref="containerRef"
  >
    <!-- 首页 -->
    <MainPage v-if="token && hasAuth" />
    <template v-if="(!token && token !== undefined) || !hasAuth">
      <LoginPage />
    </template>
    <div class="ew-resize absolute bottom-0 top-0 w-2 cursor-ew-resize" ref="dragRef"></div>
  </div>
</template>

<style scoped></style>
