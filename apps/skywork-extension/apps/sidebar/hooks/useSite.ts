import { SESSION_KEYS } from "@/constants/cacheKey";
import { SiteType } from "@/types";
import { storage } from "wxt/storage";

const useSite = () => {
  const siteType = ref<SiteType | null>(null);
  storage.watch<SiteType>(SESSION_KEYS.siteType, (value) => {
    siteType.value = value;
  });
  storage.getItem<SiteType>(SESSION_KEYS.siteType).then((value) => {
    siteType.value = value;
  });

  return {
    siteType,
  };
};
export default useSite;
