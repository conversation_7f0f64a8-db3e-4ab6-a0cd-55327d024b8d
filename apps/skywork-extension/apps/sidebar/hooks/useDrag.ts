import { getContainerElement } from "@/apps/sidebar/utils/dom";

const useDrag = () => {
  const containerRef = ref<HTMLElement | null>(null);
  const dragRef = ref<HTMLElement | null>(null);

  const MIN_WIDTH = 400;
  let isStart = false;
  const onMouseDown = (e: MouseEvent) => {
    isStart = true;
    getContainerElement().classList.add("dragging");
  };
  const onMouseMove = (e: MouseEvent) => {
    if (!isStart) return;
    let w = window.innerWidth - e.clientX;
    w = w < MIN_WIDTH ? MIN_WIDTH : w;
    w = w > window.innerWidth ? window.innerWidth : w;
    containerRef.value?.style.setProperty("--skywork-sidebar-width", `${w}px`);
  };
  const onMouseUp = (e: MouseEvent) => {
    isStart = false;
    getContainerElement().classList.remove("dragging");
  };
  onMounted(() => {
    dragRef.value?.addEventListener("mousedown", onMouseDown);
    window?.addEventListener("mousemove", onMouseMove);
    window?.addEventListener("mouseup", onMouseUp);

    return () => {
      dragRef.value?.removeEventListener("mousedown", onMouseDown);
      window?.removeEventListener("mousemove", onMouseMove);
      window?.removeEventListener("mouseup", onMouseUp);
    };
  });
  return {
    containerRef,
    dragRef,
  };
};
export default useDrag;
