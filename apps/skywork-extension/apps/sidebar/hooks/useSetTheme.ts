import { getContainerElement } from "@/apps/sidebar/utils/dom";
import useAppStore from "@/store/app";
import { ThemeMode } from "@/types";

const useTheme = () => {
  const { theme } = storeToRefs(useAppStore());

  watch(
    theme,
    (value) => {
      nextTick(() => {
        getContainerElement()?.classList.remove(ThemeMode.Light, ThemeMode.Dark);
        getContainerElement()?.classList.add(value);
      });
    },
    {
      immediate: true,
    }
  );

  return {
    theme,
  };
};
export default useTheme;
