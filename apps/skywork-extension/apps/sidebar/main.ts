import createStore from "@/store";
import i18n from "@tg-fe/i18n";
import ElementPlus from "element-plus";
import "element-plus/dist/index.css";
import { createApp as _createApp } from "vue";
import App from "./App.vue";
import { AppProps } from "./types";
// eslint-disable-next-line
import "@/styles/markdown.scss";
import "@/styles/tailwind.css";

import { zIndex } from "@/constants/app";
import "@/styles/index.css";
import "./styles/sidebar.css";

export const createApp = (props: AppProps) => {
  const app = _createApp(App, props as Record<string, any>);

  const store = createStore();
  getLanguage().then((locale) => {
    i18n.global.locale.value = locale;
  });
  app.use(store);
  app.use(i18n);
  app.use(ElementPlus, {
    zIndex: zIndex,
  });
  return { app, store };
};
