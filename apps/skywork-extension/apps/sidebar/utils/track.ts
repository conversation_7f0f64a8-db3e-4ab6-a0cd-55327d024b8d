import useChatStore from "@/apps/sidebar/store/chat";
import { sendMsg } from "@/shared/send";
import { useUserStore } from "@/store/user";
import { MsgAction } from "@/types";

const sendTracker = (data: Record<string, any>) => {
  sendMsg({
    action: MsgAction.Tracker,
    payload: {
      data,
    },
  });
};

const formatTrackData = (data: Record<string, any>) => {
  const { userInfo } = useUserStore();
  const chatStore = useChatStore();
  return {
    ...data,
    event_info: {
      user_id: userInfo?.uid,
      agent_id: chatStore.agentId,
      session_id: chatStore.sessionId,
      ...data.event_info,
    },
  };
};

export const track = {
  /** 插件登录页曝光 */
  extension_login_page_show: () => {
    sendTracker(
      formatTrackData({
        event: "extension_login_page_show",
        event_info: {
          origin: "extension",
          event: "show",
          entity: "login_page",
        },
      })
    );
  },
  /** 插件登录按钮点击 */
  extension_login_button_click: () => {
    sendTracker(
      formatTrackData({
        event: "extension_login_button_click",
        event_info: {
          origin: "extension",
          event: "click",
          entity: "login_button",
        },
      })
    );
  },
  /** 插件首页曝光 */
  extension_home_page_show: () => {
    sendTracker(
      formatTrackData({
        event: "extension_home_page_show",
        event_info: {
          origin: "extension",
          event: "show",
          entity: "home_page",
        },
      })
    );
  },
  /** 对话发送 */
  extension_chat_send_click: (params: Record<string, any>) => {
    sendTracker(
      formatTrackData({
        event: "extension_chat_send_click",
        event_info: {
          origin: "extension",
          event: "click",
          entity: "chat_send",
          ...params,
        },
      })
    );
  },
  /** 保存到知识库点击 */
  extension_save_button_click: () => {
    sendTracker(
      formatTrackData({
        event: "extension_save_button_click",
        event_info: {
          origin: "extension",
          event: "click",
          entity: "save_button",
        },
      })
    );
  },
  /** 到知识库查看点击 */
  extension_view_button_click: () => {
    sendTracker(
      formatTrackData({
        event: "extension_view_button_click",
        event_info: {
          origin: "extension",
          event: "click",
          entity: "view_button",
        },
      })
    );
  },
  /** 外观设置曝光 */
  extension_appearance_show: () => {
    sendTracker(
      formatTrackData({
        event: "extension_appearance_show",
        event_info: {
          origin: "extension",
          event: "show",
          entity: "appearance",
        },
      })
    );
  },
  /** 外观设置 */
  extension_appearance_setting_click: (params: Record<string, any>) => {
    sendTracker(
      formatTrackData({
        event: "extension_appearance_setting_click",
        event_info: {
          origin: "extension",
          event: "click",
          entity: "appearance_setting",
          ...params,
        },
      })
    );
  },
  /** 插件联系我们曝光 */
  extension_contact_us_show: () => {
    sendTracker(
      formatTrackData({
        event: "extension_contact_us_show",
        event_info: {
          origin: "extension",
          event: "show",
          entity: "contact_us",
        },
      })
    );
  },
  /** 插件文件上传 */
  extension_uploading_state: (params: Record<string, any>) => {
    sendTracker(
      formatTrackData({
        event: "extension_uploading_state",
        event_info: {
          origin: "extension",
          event: "state",
          entity: "uploading",
          ...params,
        },
      })
    );
  },
};
