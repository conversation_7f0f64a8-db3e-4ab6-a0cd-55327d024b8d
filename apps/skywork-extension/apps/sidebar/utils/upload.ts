import { apiOverseasInfraFileAdd, apiOverseasInfraFileAddParams, apiOverseasInfraFileGet } from "@/api/infra";
import { isMsgError, sendMsg } from "@/shared/send";
import { getCurrentTabByClient } from "@/shared/tabs";
import useAppStore from "@/store/app";
import { MsgAction, MsgPayloadData } from "@/types";
import { uniqueId } from "lodash-es";

/**
 * 普通上传
 * @param file File对象
 * @param params 上传参数
 * @param params.business_file_type 业务类型
 * @param params.file_name 文件名称，不带后缀
 * @param params.file_type 文件类型
 * @param params.file_size 文件大小，字节为单位
 * @param params.expire_time 过期时间（秒），不传服务端兜底
 * @param params.is_get_cdn_url 是否获取cdn链接
 * @returns
 */
export const uploadImageToOss = async (file: File, params: apiOverseasInfraFileAddParams) => {
  const res = await apiOverseasInfraFileAdd(params);
  const { file_expire_url, file_no } = res;
  await uploadToOss({ oss_url: file_expire_url, file: file });
  // 3. 获取文件信息
  const data = await apiOverseasInfraFileGet({
    file_no_list: [file_no],
    expire_time: params.expire_time,
    is_get_cdn_url: !!params.is_get_cdn_url,
  });
  return data.file_no_list.map((v) => data.file_record_map[v])[0];
};

// 上传文件到oss
const uploadToOss = async (params: { oss_url: string; file: File }) => {
  const { oss_url, file } = params;
  const appStore = useAppStore();
  const res = await sendMsg<MsgPayloadData & { data: Record<string, any> }>({
    action: MsgAction.UploadImageToOss,
    payload: {
      msgData: {
        appId: appStore.appId,
        tabId: (appStore.tab?.id || (await getCurrentTabByClient()).id) as number,
        id: uniqueId(),
      },
      url: oss_url,
      fileUrl: URL.createObjectURL(file),
      base64: await fileToBase64(file),
    },
  });
  if (isMsgError(res)) {
    throw res.error;
  }
  if (res.msgData.appId !== useAppStore().appId) {
    throw new Error("appId not match");
  }
  return res.data;
};
