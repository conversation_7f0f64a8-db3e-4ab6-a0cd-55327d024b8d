## 部署说明

- 测试环境：
  - 页面地址：https://www-test.skywork.ai/share
  - jenkins地址：https://jenkins.singularity-ai.com/job/test-skywork-ssr-us
  - 分支名：test-skywork-ssr
- 线上环境：
  - 页面地址：https://skywork.ai/share
  - jenkins地址：https://jenkins.singularity-ai.com/job/prod-skywork-ssr-us
  - 分支名：prod-skywork-ssr

# Nuxt Minimal Starter

Look at the [Nuxt documentation](https://nuxt.com/docs/getting-started/introduction) to learn more.

## Setup

Make sure to install dependencies:

```bash
# npm
npm install

# pnpm
pnpm install

# yarn
yarn install

# bun
bun install
```

## Development Server

Start the development server on `http://localhost:3000`:

```bash
# npm
npm run dev

# pnpm
pnpm dev

# yarn
yarn dev

# bun
bun run dev
```

## Production

Build the application for production:

```bash
# npm
npm run build

# pnpm
pnpm build

# yarn
yarn build

# bun
bun run build
```

Locally preview production build:

```bash
# npm
npm run preview

# pnpm
pnpm preview

# yarn
yarn preview

# bun
bun run preview
```

Check out the [deployment documentation](https://nuxt.com/docs/getting-started/deployment) for more information.
