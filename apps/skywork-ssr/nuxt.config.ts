// https://nuxt.com/docs/api/configuration/nuxt-config
import { fileURLToPath } from "node:url";

const isProd = process.env.NODE_ENV === "production";

export default defineNuxtConfig({
  compatibilityDate: "2024-11-01",
  devtools: { enabled: true },
  modules: [],
  alias: {
    "@": fileURLToPath(new URL("./", import.meta.url)),
  },
  app: {
    buildAssetsDir: "/share/_nuxt/",
    head: {
      title: "Doccie-网页标题",
      // 每个页面可以通过definePageMeta重写
      meta: [
        {
          name: "viewport",
          content:
            "width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0,user-scalable=no, viewport-fit=cover",
        },
        {
          charset: "utf-8",
        },
        {
          ["http-equiv"]: "x-ua-compatible",
          content: "ie=edge",
        },
        {
          name: "theme-color",
          content: "#fff",
        },
        {
          name: "description",
          content: "Doccie-网页描述",
        },
        {
          name: "keywords",
          content: "Doccie-网页关键词",
        },
        {
          property: "og:type",
          content: "website",
        },
        {
          property: "og:title",
          content: "Doccie-网页og标题",
        },
        {
          property: "og:description",
          content: "Doccie-网页og描述",
        },
        {
          property: "og:url",
          content: "https://www.tiangong.cn",
        },
        {
          property: "og:image",
          content: "https://static.tiangong.cn/template/image/tg_logo.png",
        },
      ],
      link: [
        {
          rel: "icon",
          href: "/logo.ico",
        },
      ],
      style: [],
      script: [
        // TODO: google analysis
        {
          type: "text/javascript",
          async: true,
          src: "https://www.googletagmanager.com/gtag/js?id=G-WEFE7MXDCE",
        },
        {
          type: "text/javascript",
          children: `
            window.dataLayer = window.dataLayer || [];
            function gtag() { dataLayer.push(arguments); }
            gtag('js', new Date());
            gtag('config', 'G-WEFE7MXDCE');
          `,
        },
        // TODO: arms
        {
          type: "text/javascript",
          children: `
            window.__bl = {
              config: {
                  pid:"in9gwarca6@fcbe9bb0e7e5810",
                  appType:"web",
                  imgUrl:"https://arms-retcode.aliyuncs.com/r.png?",
                  sendResource:true,
                  enableLinkTrace:true,
                  behavior:true,
                  useFmp:true,
                  enableSPA:true,
                  environment:"${isProd ? "prod" : "daily"}",
                  page: (
                    location.host +
                    location.pathname.replace(/(\\/([a-z\\-_]+)?\\d{2,20})/, "$1**").replace(/\\/$/, "") +
                    location.search.match(/\\?|channel=[^&]+/g)
                  ).replace(/null|,|\\?$/g, "")
              }
            }
          `,
        },
        {
          type: "text/javascript",
          crossorigin: "anonymous",
          src: "https://retcode.alicdn.com/retcode/bl.js",
        },
      ],
      noscript: [{ children: "JavaScript is required" }],
    },
  },
  devServer: {
    host: "0.0.0.0",
  },
});
