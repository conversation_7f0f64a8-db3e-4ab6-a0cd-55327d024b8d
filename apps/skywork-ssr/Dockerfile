FROM singularity-dockerhub-bj-registry-vpc.cn-beijing.cr.aliyuncs.com/base/node:18 AS build

WORKDIR /app

COPY . .

RUN npm config set registry https://registry.npmmirror.com && \
    npm install pnpm -g && \
    pnpm config set registry https://registry.npmmirror.com && \
    pnpm install && \
    pnpm run build

FROM singularity-dockerhub-bj-registry-vpc.cn-beijing.cr.aliyuncs.com/base/node:18-alpine

USER root

WORKDIR /app

COPY --from=build /app/.output /app/.output
COPY --from=build /app/env /app/env
COPY --from=build /app/package.json /app/package.json
COPY --from=build /app/ecosystem.config.cjs /app/ecosystem.config.cjs

# pnpm 全局包位置
ENV PNPM_HOME=/usr/local/bin

RUN npm config set registry https://registry.npmmirror.com && \
    npm install pnpm -g && \
    pnpm config set registry https://registry.npmmirror.com && \
    pnpm install --production && \
    pnpm install pm2 -g 
# 对外端口统一改成3000，如果端口更换，这边可以更新一下
EXPOSE 3000

CMD ["npm", "run", "pm2_start"]