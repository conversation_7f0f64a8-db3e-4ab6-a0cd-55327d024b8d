<template>
  <div>
    <div v-if="isErrText">{{ isErrText }}</div>
    <div v-else>加载中</div>
  </div>
</template>

<script lang="tsx" setup>
import { ref } from "vue";
import { useRoute } from "vue-router";
import { useRequestURL } from "#app";

const isErrText = ref();

const requestURL = useRequestURL();
const title = ref(""); //标题
const description = ref(""); //描述
const imageUrl = ref(""); //图片链接
const siteUrl = ref(requestURL.href); //网页 URL
const siteName = ref("Skywork"); //网页 名称

const twitterUs = ref(""); //@你的 Twitter 账号/站点官方账号
const twitterCreator = ref(""); //@作者 Twitter 账号
//http://www.local.tiangong.cn:3000/share/ppt/dca5106d-83df-4800-a16e-399731f8477f?v=0.1&from=any&env=develop
const route = useRoute();
const { query, params } = route;
const conversation_id = params.id; //资源id
const conversation_type = params.type; //资源类型
const { env } = query;

let apiUrl = "https://api.skywork.ai";
let pageUrl = "https://skywork.ai";
switch (env) {
  case "develop":
    apiUrl = "https://api-test.skywork.ai";
    pageUrl = "https://dev.skywork.ai";
    break;
  case "test":
    apiUrl = "https://api-test.skywork.ai";
    pageUrl = "https://text.skywork.ai";
    break;
  case "pre":
    apiUrl = "https://api-pre.skywork.ai";
    pageUrl = "https://pre.skywork.ai";
    break;
  default:
    break;
}

// 浏览器跳转
onMounted(() => {
  // location.href = "https://www.baidu.com";
  // location.href = `${pageUrl}/${conversation_type}/${conversation_id}`;
});

useFetch(`${apiUrl}/doccie-creation/share/detail?type=${conversation_type}&&business_id=${conversation_id}`, {
  method: "get",
  key: "tdk-data",
}).then((res: any) => {
  if (res.data.value.code === 0) {
    setHtmlMate(res.data.value.data);
  } else {
    isErrText.value = res.data.value.message;
  }
});

const setHtmlMate = (value: any) => {
  console.log("value====", value);
  const { title: valueTitle, cover_image } = value || {};
  title.value = valueTitle;
  imageUrl.value = cover_image;
};

useHead(
  {
    title: title,
    meta: [
      {
        name: "description",
        content: description,
      },
      /**************faceBook************开始**/
      {
        property: "og:title", //标题
        content: title,
      },
      {
        property: "og:description", //描述
        content: description,
      },
      {
        property: "og:type",
        content: "website",
      },
      {
        property: "og:image", //图片 URL
        content: imageUrl,
      },
      {
        property: "og:url", //网页 URL
        content: siteUrl,
      },
      {
        property: "og:site_name", //站点名称
        content: siteName,
      },

      /**************faceBook************结束**/
      /**************Twitter************开始**/
      {
        property: "twitter:title", //标题
        content: title,
      },
      {
        property: "twitter:description", //描述
        content: description,
      },
      {
        property: "twitter:card" /** 控制卡片样式 */,
        content: "summary_large_image",
      },
      {
        property: "twitter:image", //图片 URL
        content: imageUrl,
      },
      {
        property: "twitter:url", //网页 URL
        content: siteUrl,
      },
      {
        property: "twitter:site", //@你的 Twitter 账号 站点官方账号
        content: twitterUs,
      },
      {
        property: "twitter:creator", //@作者 Twitter 账号
        content: twitterCreator,
      },
      /**************Twitter************结束**/
    ],
  },
  { active: process.server }
);
</script>

<style lang="scss" scoped></style>
