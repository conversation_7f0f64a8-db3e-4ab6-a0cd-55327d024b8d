// ENVIRONMENT 服务端环境变量标识：prod: 线上, test: 测试
const env = process.env.ENVIRONMENT;
if (env) {
  require('dotenv').config({
    path: `./env/.env.${env}`,
  });
  // 环境变量不存在，默认走线上配置
} else {
  process.env.NODE_ENV !== 'local' &&
    require('dotenv').config({
      path: './env/.env.test',
    });
}

module.exports = {
    apps: [
      {
        name: 'seo_app',
        port: '3000',
        exec_mode: 'cluster',
        instances: 4,
        script: './.output/server/index.mjs',
      }
    ]
  }