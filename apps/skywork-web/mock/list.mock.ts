import Mock from "mockjs";
import { createSSEStream, defineMock } from "vite-plugin-mock-dev-server";
import { mockWorklist } from "./const";
export default defineMock([
  {
    url: "/dev-api/list/get",
    delay: 1000,
    body: {
      code: 200,
      message: "OK",
      result: Mock.mock({
        "list|10": [
          {
            "id|+1": 1,
          },
        ],
      }),
    },
  },
  {
    url: "/dev-api/list/error",
    delay: 1000,
    body: {
      code: 1,
      message: "ERROR",
      result: null,
    },
  },
  {
    url: "/dev-api/list/test",
    response(req, res, next) {
      const { query, body, params, headers } = req;
      console.log(query, body, params, headers);
      const { page = 1, pageSize = 8 } = query;

      const _list = mockWorklist.slice((page - 1) * pageSize, page * pageSize).map((item, index) => {
        return { id: (page - 1) * pageSize + index + 1, name: `Item ${(page - 1) * pageSize + index + 1}`, ...item };
      });
      // const _list = Array.from({ length: pageSize }, (_, index) => ({
      //   id: (page - 1) * pageSize + index + 1,
      //   name: `Item ${(page - 1) * pageSize + index + 1}`,
      // }));
      res.statusCode = 200;
      res.setHeader("Content-Type", "application/json");
      res.setHeader("Access-Control-Allow-Origin", "*");
      res.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
      res.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization");
      res.end(
        JSON.stringify({
          code: 200,
          message: "ok",
          result: {
            list: [..._list],
          },
          total: mockWorklist.length,
        })
      );
    },
  },
  {
    url: "/dev-api/list/export/excel",
    delay: 1000,
    response(req, res) {
      // 模拟数据
      const data = Mock.mock({
        "data|50": [
          {
            "id|+1": 1,
            name: "@cname", // 随机生成姓名
            "age|20-60": 1, // 随机生成年龄
            address: "@county(true)", // 随机生成地址
          },
        ],
      }).data;

      // 将数据转为 CSV 格式
      const csvHeader = "ID,Name,Age,Address\n"; // CSV 表头
      const csvRows = data
        .map((item) => {
          return `${item.id},${item.name},${item.age},${item.address}`; // 每一行的内容
        })
        .join("\n");

      const csvData = csvHeader + csvRows;

      // 设置响应头，表明返回的是 CSV 文件
      res.setHeader("Content-Type", "text/csv");
      res.setHeader("Content-Disposition", "attachment; filename=data.csv");
      res.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
      res.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization");
      // 返回 CSV 内容
      res.end(
        JSON.stringify({
          code: 200,
          message: "ok",
          result: {
            // list: [..._list],
            // total: mockWorklist.length,
          },
          mockBlobData: csvData,
        })
      );
    },
  },
  {
    url: "/dev-api/preview-sse",
    response(req, res) {
      const sse = createSSEStream(req, res);
      let count = 0;
      const timer = setInterval(() => {
        sse.write({
          event: "count",
          data: { count: ++count },
        });
        if (count >= 10) {
          sse.end();
          clearInterval(timer);
        }
      }, 1000);
    },
  },
]);
