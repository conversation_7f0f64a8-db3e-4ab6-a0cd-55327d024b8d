(function () {
  "use strict";
  function Yt(r) {
    return r && r.__esModule && Object.prototype.hasOwnProperty.call(r, "default") ? r.default : r;
  }
  var tn, nt;
  function Zt() {
    if (nt) return tn;
    nt = 1;
    function r(n) {
      return (
        n instanceof Map
          ? (n.clear =
              n.delete =
              n.set =
                function () {
                  throw new Error("map is read-only");
                })
          : n instanceof Set &&
            (n.add =
              n.clear =
              n.delete =
                function () {
                  throw new Error("set is read-only");
                }),
        Object.freeze(n),
        Object.getOwnPropertyNames(n).forEach((i) => {
          const m = n[i],
            C = typeof m;
          (C === "object" || C === "function") && !Object.isFrozen(m) && r(m);
        }),
        n
      );
    }
    class e {
      constructor(i) {
        i.data === void 0 && (i.data = {}), (this.data = i.data), (this.isMatchIgnored = !1);
      }
      ignoreMatch() {
        this.isMatchIgnored = !0;
      }
    }
    function t(n) {
      return n
        .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;")
        .replace(/'/g, "&#x27;");
    }
    function o(n, ...i) {
      const m = Object.create(null);
      for (const C in n) m[C] = n[C];
      return (
        i.forEach(function (C) {
          for (const V in C) m[V] = C[V];
        }),
        m
      );
    }
    const d = "</span>",
      c = (n) => !!n.scope,
      b = (n, { prefix: i }) => {
        if (n.startsWith("language:")) return n.replace("language:", "language-");
        if (n.includes(".")) {
          const m = n.split(".");
          return ["".concat(i).concat(m.shift()), ...m.map((C, V) => "".concat(C).concat("_".repeat(V + 1)))].join(" ");
        }
        return "".concat(i).concat(n);
      };
    class _ {
      constructor(i, m) {
        (this.buffer = ""), (this.classPrefix = m.classPrefix), i.walk(this);
      }
      addText(i) {
        this.buffer += t(i);
      }
      openNode(i) {
        if (!c(i)) return;
        const m = b(i.scope, { prefix: this.classPrefix });
        this.span(m);
      }
      closeNode(i) {
        c(i) && (this.buffer += d);
      }
      value() {
        return this.buffer;
      }
      span(i) {
        this.buffer += '<span class="'.concat(i, '">');
      }
    }
    const p = (n = {}) => {
      const i = { children: [] };
      return Object.assign(i, n), i;
    };
    class s {
      constructor() {
        (this.rootNode = p()), (this.stack = [this.rootNode]);
      }
      get top() {
        return this.stack[this.stack.length - 1];
      }
      get root() {
        return this.rootNode;
      }
      add(i) {
        this.top.children.push(i);
      }
      openNode(i) {
        const m = p({ scope: i });
        this.add(m), this.stack.push(m);
      }
      closeNode() {
        if (this.stack.length > 1) return this.stack.pop();
      }
      closeAllNodes() {
        for (; this.closeNode(); );
      }
      toJSON() {
        return JSON.stringify(this.rootNode, null, 4);
      }
      walk(i) {
        return this.constructor._walk(i, this.rootNode);
      }
      static _walk(i, m) {
        return (
          typeof m == "string"
            ? i.addText(m)
            : m.children && (i.openNode(m), m.children.forEach((C) => this._walk(i, C)), i.closeNode(m)),
          i
        );
      }
      static _collapse(i) {
        typeof i != "string" &&
          i.children &&
          (i.children.every((m) => typeof m == "string")
            ? (i.children = [i.children.join("")])
            : i.children.forEach((m) => {
                s._collapse(m);
              }));
      }
    }
    class a extends s {
      constructor(i) {
        super(), (this.options = i);
      }
      addText(i) {
        i !== "" && this.add(i);
      }
      startScope(i) {
        this.openNode(i);
      }
      endScope() {
        this.closeNode();
      }
      __addSublanguage(i, m) {
        const C = i.root;
        m && (C.scope = "language:".concat(m)), this.add(C);
      }
      toHTML() {
        return new _(this, this.options).value();
      }
      finalize() {
        return this.closeAllNodes(), !0;
      }
    }
    function l(n) {
      return n ? (typeof n == "string" ? n : n.source) : null;
    }
    function g(n) {
      return y("(?=", n, ")");
    }
    function f(n) {
      return y("(?:", n, ")*");
    }
    function E(n) {
      return y("(?:", n, ")?");
    }
    function y(...n) {
      return n.map((m) => l(m)).join("");
    }
    function N(n) {
      const i = n[n.length - 1];
      return typeof i == "object" && i.constructor === Object ? (n.splice(n.length - 1, 1), i) : {};
    }
    function T(...n) {
      return "(" + (N(n).capture ? "" : "?:") + n.map((C) => l(C)).join("|") + ")";
    }
    function O(n) {
      return new RegExp(n.toString() + "|").exec("").length - 1;
    }
    function R(n, i) {
      const m = n && n.exec(i);
      return m && m.index === 0;
    }
    const F = /\[(?:[^\\\]]|\\.)*\]|\(\??|\\([1-9][0-9]*)|\\./;
    function x(n, { joinWith: i }) {
      let m = 0;
      return n
        .map((C) => {
          m += 1;
          const V = m;
          let Q = l(C),
            S = "";
          for (; Q.length > 0; ) {
            const v = F.exec(Q);
            if (!v) {
              S += Q;
              break;
            }
            (S += Q.substring(0, v.index)),
              (Q = Q.substring(v.index + v[0].length)),
              v[0][0] === "\\" && v[1] ? (S += "\\" + String(Number(v[1]) + V)) : ((S += v[0]), v[0] === "(" && m++);
          }
          return S;
        })
        .map((C) => "(".concat(C, ")"))
        .join(i);
    }
    const D = /\b\B/,
      B = "[a-zA-Z]\\w*",
      M = "[a-zA-Z_]\\w*",
      U = "\\b\\d+(\\.\\d+)?",
      K = "(-?)(\\b0[xX][a-fA-F0-9]+|(\\b\\d+(\\.\\d*)?|\\.\\d+)([eE][-+]?\\d+)?)",
      G = "\\b(0b[01]+)",
      re =
        "!|!=|!==|%|%=|&|&&|&=|\\*|\\*=|\\+|\\+=|,|-|-=|/=|/|:|;|<<|<<=|<=|<|===|==|=|>>>=|>>=|>=|>>>|>>|>|\\?|\\[|\\{|\\(|\\^|\\^=|\\||\\|=|\\|\\||~",
      ee = (n = {}) => {
        const i = /^#![ ]*\//;
        return (
          n.binary && (n.begin = y(i, /.*\b/, n.binary, /\b.*/)),
          o(
            {
              scope: "meta",
              begin: i,
              end: /$/,
              relevance: 0,
              "on:begin": (m, C) => {
                m.index !== 0 && C.ignoreMatch();
              },
            },
            n
          )
        );
      },
      ne = { begin: "\\\\[\\s\\S]", relevance: 0 },
      W = { scope: "string", begin: "'", end: "'", illegal: "\\n", contains: [ne] },
      Z = { scope: "string", begin: '"', end: '"', illegal: "\\n", contains: [ne] },
      J = {
        begin:
          /\b(a|an|the|are|I'm|isn't|don't|doesn't|won't|but|just|should|pretty|simply|enough|gonna|going|wtf|so|such|will|you|your|they|like|more)\b/,
      },
      se = function (n, i, m = {}) {
        const C = o({ scope: "comment", begin: n, end: i, contains: [] }, m);
        C.contains.push({
          scope: "doctag",
          begin: "[ ]*(?=(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):)",
          end: /(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):/,
          excludeBegin: !0,
          relevance: 0,
        });
        const V = T(
          "I",
          "a",
          "is",
          "so",
          "us",
          "to",
          "at",
          "if",
          "in",
          "it",
          "on",
          /[A-Za-z]+['](d|ve|re|ll|t|s|n)/,
          /[A-Za-z]+[-][a-z]+/,
          /[A-Za-z][a-z]{2,}/
        );
        return C.contains.push({ begin: y(/[ ]+/, "(", V, /[.]?[:]?([.][ ]|[ ])/, "){3}") }), C;
      },
      Me = se("//", "$"),
      ke = se("/\\*", "\\*/"),
      Ne = se("#", "$"),
      Se = { scope: "number", begin: U, relevance: 0 },
      Ce = { scope: "number", begin: K, relevance: 0 },
      pe = { scope: "number", begin: G, relevance: 0 },
      me = {
        scope: "regexp",
        begin: /\/(?=[^/\n]*\/)/,
        end: /\/[gimuy]*/,
        contains: [ne, { begin: /\[/, end: /\]/, relevance: 0, contains: [ne] }],
      },
      ye = { scope: "title", begin: B, relevance: 0 },
      X = { scope: "title", begin: M, relevance: 0 },
      Y = { begin: "\\.\\s*" + M, relevance: 0 };
    var ae = Object.freeze({
      __proto__: null,
      APOS_STRING_MODE: W,
      BACKSLASH_ESCAPE: ne,
      BINARY_NUMBER_MODE: pe,
      BINARY_NUMBER_RE: G,
      COMMENT: se,
      C_BLOCK_COMMENT_MODE: ke,
      C_LINE_COMMENT_MODE: Me,
      C_NUMBER_MODE: Ce,
      C_NUMBER_RE: K,
      END_SAME_AS_BEGIN: function (n) {
        return Object.assign(n, {
          "on:begin": (i, m) => {
            m.data._beginMatch = i[1];
          },
          "on:end": (i, m) => {
            m.data._beginMatch !== i[1] && m.ignoreMatch();
          },
        });
      },
      HASH_COMMENT_MODE: Ne,
      IDENT_RE: B,
      MATCH_NOTHING_RE: D,
      METHOD_GUARD: Y,
      NUMBER_MODE: Se,
      NUMBER_RE: U,
      PHRASAL_WORDS_MODE: J,
      QUOTE_STRING_MODE: Z,
      REGEXP_MODE: me,
      RE_STARTERS_RE: re,
      SHEBANG: ee,
      TITLE_MODE: ye,
      UNDERSCORE_IDENT_RE: M,
      UNDERSCORE_TITLE_MODE: X,
    });
    function de(n, i) {
      n.input[n.index - 1] === "." && i.ignoreMatch();
    }
    function ge(n, i) {
      n.className !== void 0 && ((n.scope = n.className), delete n.className);
    }
    function _e(n, i) {
      i &&
        n.beginKeywords &&
        ((n.begin = "\\b(" + n.beginKeywords.split(" ").join("|") + ")(?!\\.)(?=\\b|\\s)"),
        (n.__beforeBegin = de),
        (n.keywords = n.keywords || n.beginKeywords),
        delete n.beginKeywords,
        n.relevance === void 0 && (n.relevance = 0));
    }
    function $e(n, i) {
      Array.isArray(n.illegal) && (n.illegal = T(...n.illegal));
    }
    function $n(n, i) {
      if (n.match) {
        if (n.begin || n.end) throw new Error("begin & end are not supported with match");
        (n.begin = n.match), delete n.match;
      }
    }
    function Be(n, i) {
      n.relevance === void 0 && (n.relevance = 1);
    }
    const Ke = (n, i) => {
        if (!n.beforeMatch) return;
        if (n.starts) throw new Error("beforeMatch cannot be used with starts");
        const m = Object.assign({}, n);
        Object.keys(n).forEach((C) => {
          delete n[C];
        }),
          (n.keywords = m.keywords),
          (n.begin = y(m.beforeMatch, g(m.begin))),
          (n.starts = { relevance: 0, contains: [Object.assign(m, { endsParent: !0 })] }),
          (n.relevance = 0),
          delete m.beforeMatch;
      },
      Ge = ["of", "and", "for", "in", "not", "or", "if", "then", "parent", "list", "value"],
      Kn = "keyword";
    function qe(n, i, m = Kn) {
      const C = Object.create(null);
      return (
        typeof n == "string"
          ? V(m, n.split(" "))
          : Array.isArray(n)
            ? V(m, n)
            : Object.keys(n).forEach(function (Q) {
                Object.assign(C, qe(n[Q], i, Q));
              }),
        C
      );
      function V(Q, S) {
        i && (S = S.map((v) => v.toLowerCase())),
          S.forEach(function (v) {
            const k = v.split("|");
            C[k[0]] = [Q, Ue(k[0], k[1])];
          });
      }
    }
    function Ue(n, i) {
      return i ? Number(i) : Gn(n) ? 0 : 1;
    }
    function Gn(n) {
      return Ge.includes(n.toLowerCase());
    }
    const He = {},
      ve = (n) => {},
      Ie = (n, ...i) => {},
      ue = (n, i) => {
        He["".concat(n, "/").concat(i)] || (He["".concat(n, "/").concat(i)] = !0);
      },
      Le = new Error();
    function We(n, i, { key: m }) {
      let C = 0;
      const V = n[m],
        Q = {},
        S = {};
      for (let v = 1; v <= i.length; v++) (S[v + C] = V[v]), (Q[v + C] = !0), (C += O(i[v - 1]));
      (n[m] = S), (n[m]._emit = Q), (n[m]._multi = !0);
    }
    function Ye(n) {
      if (Array.isArray(n.begin)) {
        if (n.skip || n.excludeBegin || n.returnBegin)
          throw (ve("skip, excludeBegin, returnBegin not compatible with beginScope: {}"), Le);
        if (typeof n.beginScope != "object" || n.beginScope === null) throw (ve("beginScope must be object"), Le);
        We(n, n.begin, { key: "beginScope" }), (n.begin = x(n.begin, { joinWith: "" }));
      }
    }
    function Pe(n) {
      if (Array.isArray(n.end)) {
        if (n.skip || n.excludeEnd || n.returnEnd)
          throw (ve("skip, excludeEnd, returnEnd not compatible with endScope: {}"), Le);
        if (typeof n.endScope != "object" || n.endScope === null) throw (ve("endScope must be object"), Le);
        We(n, n.end, { key: "endScope" }), (n.end = x(n.end, { joinWith: "" }));
      }
    }
    function qn(n) {
      n.scope && typeof n.scope == "object" && n.scope !== null && ((n.beginScope = n.scope), delete n.scope);
    }
    function Ze(n) {
      qn(n),
        typeof n.beginScope == "string" && (n.beginScope = { _wrap: n.beginScope }),
        typeof n.endScope == "string" && (n.endScope = { _wrap: n.endScope }),
        Ye(n),
        Pe(n);
    }
    function Hn(n) {
      function i(S, v) {
        return new RegExp(l(S), "m" + (n.case_insensitive ? "i" : "") + (n.unicodeRegex ? "u" : "") + (v ? "g" : ""));
      }
      class m {
        constructor() {
          (this.matchIndexes = {}), (this.regexes = []), (this.matchAt = 1), (this.position = 0);
        }
        addRule(v, k) {
          (k.position = this.position++),
            (this.matchIndexes[this.matchAt] = k),
            this.regexes.push([k, v]),
            (this.matchAt += O(v) + 1);
        }
        compile() {
          this.regexes.length === 0 && (this.exec = () => null);
          const v = this.regexes.map((k) => k[1]);
          (this.matcherRe = i(x(v, { joinWith: "|" }), !0)), (this.lastIndex = 0);
        }
        exec(v) {
          this.matcherRe.lastIndex = this.lastIndex;
          const k = this.matcherRe.exec(v);
          if (!k) return null;
          const ie = k.findIndex((ze, Xn) => Xn > 0 && ze !== void 0),
            j = this.matchIndexes[ie];
          return k.splice(0, ie), Object.assign(k, j);
        }
      }
      class C {
        constructor() {
          (this.rules = []), (this.multiRegexes = []), (this.count = 0), (this.lastIndex = 0), (this.regexIndex = 0);
        }
        getMatcher(v) {
          if (this.multiRegexes[v]) return this.multiRegexes[v];
          const k = new m();
          return this.rules.slice(v).forEach(([ie, j]) => k.addRule(ie, j)), k.compile(), (this.multiRegexes[v] = k), k;
        }
        resumingScanAtSamePosition() {
          return this.regexIndex !== 0;
        }
        considerAll() {
          this.regexIndex = 0;
        }
        addRule(v, k) {
          this.rules.push([v, k]), k.type === "begin" && this.count++;
        }
        exec(v) {
          const k = this.getMatcher(this.regexIndex);
          k.lastIndex = this.lastIndex;
          let ie = k.exec(v);
          if (this.resumingScanAtSamePosition() && !(ie && ie.index === this.lastIndex)) {
            const j = this.getMatcher(0);
            (j.lastIndex = this.lastIndex + 1), (ie = j.exec(v));
          }
          return ie && ((this.regexIndex += ie.position + 1), this.regexIndex === this.count && this.considerAll()), ie;
        }
      }
      function V(S) {
        const v = new C();
        return (
          S.contains.forEach((k) => v.addRule(k.begin, { rule: k, type: "begin" })),
          S.terminatorEnd && v.addRule(S.terminatorEnd, { type: "end" }),
          S.illegal && v.addRule(S.illegal, { type: "illegal" }),
          v
        );
      }
      function Q(S, v) {
        const k = S;
        if (S.isCompiled) return k;
        [ge, $n, Ze, Ke].forEach((j) => j(S, v)),
          n.compilerExtensions.forEach((j) => j(S, v)),
          (S.__beforeBegin = null),
          [_e, $e, Be].forEach((j) => j(S, v)),
          (S.isCompiled = !0);
        let ie = null;
        return (
          typeof S.keywords == "object" &&
            S.keywords.$pattern &&
            ((S.keywords = Object.assign({}, S.keywords)), (ie = S.keywords.$pattern), delete S.keywords.$pattern),
          (ie = ie || /\w+/),
          S.keywords && (S.keywords = qe(S.keywords, n.case_insensitive)),
          (k.keywordPatternRe = i(ie, !0)),
          v &&
            (S.begin || (S.begin = /\B|\b/),
            (k.beginRe = i(k.begin)),
            !S.end && !S.endsWithParent && (S.end = /\B|\b/),
            S.end && (k.endRe = i(k.end)),
            (k.terminatorEnd = l(k.end) || ""),
            S.endsWithParent && v.terminatorEnd && (k.terminatorEnd += (S.end ? "|" : "") + v.terminatorEnd)),
          S.illegal && (k.illegalRe = i(S.illegal)),
          S.contains || (S.contains = []),
          (S.contains = [].concat(
            ...S.contains.map(function (j) {
              return Wn(j === "self" ? S : j);
            })
          )),
          S.contains.forEach(function (j) {
            Q(j, k);
          }),
          S.starts && Q(S.starts, v),
          (k.matcher = V(k)),
          k
        );
      }
      if ((n.compilerExtensions || (n.compilerExtensions = []), n.contains && n.contains.includes("self")))
        throw new Error("ERR: contains `self` is not supported at the top-level of a language.  See documentation.");
      return (n.classNameAliases = o(n.classNameAliases || {})), Q(n);
    }
    function Xe(n) {
      return n ? n.endsWithParent || Xe(n.starts) : !1;
    }
    function Wn(n) {
      return (
        n.variants &&
          !n.cachedVariants &&
          (n.cachedVariants = n.variants.map(function (i) {
            return o(n, { variants: null }, i);
          })),
        n.cachedVariants
          ? n.cachedVariants
          : Xe(n)
            ? o(n, { starts: n.starts ? o(n.starts) : null })
            : Object.isFrozen(n)
              ? o(n)
              : n
      );
    }
    var Yn = "11.11.1";
    class Zn extends Error {
      constructor(i, m) {
        super(i), (this.name = "HTMLInjectionError"), (this.html = m);
      }
    }
    const Fe = t,
      Ve = o,
      z = Symbol("nomatch"),
      De = 7,
      we = function (n) {
        const i = Object.create(null),
          m = Object.create(null),
          C = [];
        let V = !0;
        const Q = "Could not find the language '{}', did you forget to load/include a language module?",
          S = { disableAutodetect: !0, name: "Plain text", contains: [] };
        let v = {
          ignoreUnescapedHTML: !1,
          throwUnescapedHTML: !1,
          noHighlightRe: /^(no-?highlight)$/i,
          languageDetectRe: /\blang(?:uage)?-([\w-]+)\b/i,
          classPrefix: "hljs-",
          cssSelector: "pre code",
          languages: null,
          __emitter: a,
        };
        function k(u) {
          return v.noHighlightRe.test(u);
        }
        function ie(u) {
          let A = u.className + " ";
          A += u.parentNode ? u.parentNode.className : "";
          const P = v.languageDetectRe.exec(A);
          if (P) {
            const q = Ae(P[1]);
            return (
              q || (Ie(Q.replace("{}", P[1])), Ie("Falling back to no-highlight mode for this block.", u)),
              q ? P[1] : "no-highlight"
            );
          }
          return A.split(/\s+/).find((q) => k(q) || Ae(q));
        }
        function j(u, A, P) {
          let q = "",
            te = "";
          typeof A == "object"
            ? ((q = u), (P = A.ignoreIllegals), (te = A.language))
            : (ue("10.7.0", "highlight(lang, code, ...args) has been deprecated."),
              ue(
                "10.7.0",
                "Please use highlight(code, options) instead.\nhttps://github.com/highlightjs/highlight.js/issues/2277"
              ),
              (te = u),
              (q = A)),
            P === void 0 && (P = !0);
          const fe = { code: q, language: te };
          Je("before:highlight", fe);
          const Re = fe.result ? fe.result : ze(fe.language, fe.code, P);
          return (Re.code = fe.code), Je("after:highlight", Re), Re;
        }
        function ze(u, A, P, q) {
          const te = Object.create(null);
          function fe(h, w) {
            return h.keywords[w];
          }
          function Re() {
            if (!I.keywords) {
              oe.addText(H);
              return;
            }
            let h = 0;
            I.keywordPatternRe.lastIndex = 0;
            let w = I.keywordPatternRe.exec(H),
              L = "";
            for (; w; ) {
              L += H.substring(h, w.index);
              const $ = he.case_insensitive ? w[0].toLowerCase() : w[0],
                ce = fe(I, $);
              if (ce) {
                const [Te, Vr] = ce;
                if (
                  (oe.addText(L), (L = ""), (te[$] = (te[$] || 0) + 1), te[$] <= De && (nn += Vr), Te.startsWith("_"))
                )
                  L += w[0];
                else {
                  const Qr = he.classNameAliases[Te] || Te;
                  Ee(w[0], Qr);
                }
              } else L += w[0];
              (h = I.keywordPatternRe.lastIndex), (w = I.keywordPatternRe.exec(H));
            }
            (L += H.substring(h)), oe.addText(L);
          }
          function je() {
            if (H === "") return;
            let h = null;
            if (typeof I.subLanguage == "string") {
              if (!i[I.subLanguage]) {
                oe.addText(H);
                return;
              }
              (h = ze(I.subLanguage, H, !0, Wt[I.subLanguage])), (Wt[I.subLanguage] = h._top);
            } else h = Vn(H, I.subLanguage.length ? I.subLanguage : null);
            I.relevance > 0 && (nn += h.relevance), oe.__addSublanguage(h._emitter, h.language);
          }
          function be() {
            I.subLanguage != null ? je() : Re(), (H = "");
          }
          function Ee(h, w) {
            h !== "" && (oe.startScope(w), oe.addText(h), oe.endScope());
          }
          function Kt(h, w) {
            let L = 1;
            const $ = w.length - 1;
            for (; L <= $; ) {
              if (!h._emit[L]) {
                L++;
                continue;
              }
              const ce = he.classNameAliases[h[L]] || h[L],
                Te = w[L];
              ce ? Ee(Te, ce) : ((H = Te), Re(), (H = "")), L++;
            }
          }
          function Gt(h, w) {
            return (
              h.scope && typeof h.scope == "string" && oe.openNode(he.classNameAliases[h.scope] || h.scope),
              h.beginScope &&
                (h.beginScope._wrap
                  ? (Ee(H, he.classNameAliases[h.beginScope._wrap] || h.beginScope._wrap), (H = ""))
                  : h.beginScope._multi && (Kt(h.beginScope, w), (H = ""))),
              (I = Object.create(h, { parent: { value: I } })),
              I
            );
          }
          function qt(h, w, L) {
            let $ = R(h.endRe, L);
            if ($) {
              if (h["on:end"]) {
                const ce = new e(h);
                h["on:end"](w, ce), ce.isMatchIgnored && ($ = !1);
              }
              if ($) {
                for (; h.endsParent && h.parent; ) h = h.parent;
                return h;
              }
            }
            if (h.endsWithParent) return qt(h.parent, w, L);
          }
          function Hr(h) {
            return I.matcher.regexIndex === 0 ? ((H += h[0]), 1) : ((et = !0), 0);
          }
          function Wr(h) {
            const w = h[0],
              L = h.rule,
              $ = new e(L),
              ce = [L.__beforeBegin, L["on:begin"]];
            for (const Te of ce) if (Te && (Te(h, $), $.isMatchIgnored)) return Hr(w);
            return (
              L.skip ? (H += w) : (L.excludeBegin && (H += w), be(), !L.returnBegin && !L.excludeBegin && (H = w)),
              Gt(L, h),
              L.returnBegin ? 0 : w.length
            );
          }
          function Yr(h) {
            const w = h[0],
              L = A.substring(h.index),
              $ = qt(I, h, L);
            if (!$) return z;
            const ce = I;
            I.endScope && I.endScope._wrap
              ? (be(), Ee(w, I.endScope._wrap))
              : I.endScope && I.endScope._multi
                ? (be(), Kt(I.endScope, h))
                : ce.skip
                  ? (H += w)
                  : (ce.returnEnd || ce.excludeEnd || (H += w), be(), ce.excludeEnd && (H = w));
            do I.scope && oe.closeNode(), !I.skip && !I.subLanguage && (nn += I.relevance), (I = I.parent);
            while (I !== $.parent);
            return $.starts && Gt($.starts, h), ce.returnEnd ? 0 : w.length;
          }
          function Zr() {
            const h = [];
            for (let w = I; w !== he; w = w.parent) w.scope && h.unshift(w.scope);
            h.forEach((w) => oe.openNode(w));
          }
          let en = {};
          function Ht(h, w) {
            const L = w && w[0];
            if (((H += h), L == null)) return be(), 0;
            if (en.type === "begin" && w.type === "end" && en.index === w.index && L === "") {
              if (((H += A.slice(w.index, w.index + 1)), !V)) {
                const $ = new Error("0 width match regex (".concat(u, ")"));
                throw (($.languageName = u), ($.badRule = en.rule), $);
              }
              return 1;
            }
            if (((en = w), w.type === "begin")) return Wr(w);
            if (w.type === "illegal" && !P) {
              const $ = new Error('Illegal lexeme "' + L + '" for mode "' + (I.scope || "<unnamed>") + '"');
              throw (($.mode = I), $);
            } else if (w.type === "end") {
              const $ = Yr(w);
              if ($ !== z) return $;
            }
            if (w.type === "illegal" && L === "") return (H += "\n"), 1;
            if (jn > 1e5 && jn > w.index * 3)
              throw new Error("potential infinite loop, way more iterations than matches");
            return (H += L), L.length;
          }
          const he = Ae(u);
          if (!he) throw (ve(Q.replace("{}", u)), new Error('Unknown language: "' + u + '"'));
          const Xr = Hn(he);
          let Jn = "",
            I = q || Xr;
          const Wt = {},
            oe = new v.__emitter(v);
          Zr();
          let H = "",
            nn = 0,
            xe = 0,
            jn = 0,
            et = !1;
          try {
            if (he.__emitTokens) he.__emitTokens(A, oe);
            else {
              for (I.matcher.considerAll(); ; ) {
                jn++, et ? (et = !1) : I.matcher.considerAll(), (I.matcher.lastIndex = xe);
                const h = I.matcher.exec(A);
                if (!h) break;
                const w = A.substring(xe, h.index),
                  L = Ht(w, h);
                xe = h.index + L;
              }
              Ht(A.substring(xe));
            }
            return (
              oe.finalize(),
              (Jn = oe.toHTML()),
              { language: u, value: Jn, relevance: nn, illegal: !1, _emitter: oe, _top: I }
            );
          } catch (h) {
            if (h.message && h.message.includes("Illegal"))
              return {
                language: u,
                value: Fe(A),
                illegal: !0,
                relevance: 0,
                _illegalBy: {
                  message: h.message,
                  index: xe,
                  context: A.slice(xe - 100, xe + 100),
                  mode: h.mode,
                  resultSoFar: Jn,
                },
                _emitter: oe,
              };
            if (V)
              return { language: u, value: Fe(A), illegal: !1, relevance: 0, errorRaised: h, _emitter: oe, _top: I };
            throw h;
          }
        }
        function Xn(u) {
          const A = { value: Fe(u), illegal: !1, relevance: 0, _top: S, _emitter: new v.__emitter(v) };
          return A._emitter.addText(u), A;
        }
        function Vn(u, A) {
          A = A || v.languages || Object.keys(i);
          const P = Xn(u),
            q = A.filter(Ae)
              .filter($t)
              .map((be) => ze(be, u, !1));
          q.unshift(P);
          const te = q.sort((be, Ee) => {
              if (be.relevance !== Ee.relevance) return Ee.relevance - be.relevance;
              if (be.language && Ee.language) {
                if (Ae(be.language).supersetOf === Ee.language) return 1;
                if (Ae(Ee.language).supersetOf === be.language) return -1;
              }
              return 0;
            }),
            [fe, Re] = te,
            je = fe;
          return (je.secondBest = Re), je;
        }
        function Lr(u, A, P) {
          const q = (A && m[A]) || P;
          u.classList.add("hljs"), u.classList.add("language-".concat(q));
        }
        function Qn(u) {
          let A = null;
          const P = ie(u);
          if (k(P) || (Je("before:highlightElement", { el: u, language: P }), u.dataset.highlighted)) return;
          if (u.children.length > 0 && (v.ignoreUnescapedHTML, v.throwUnescapedHTML))
            throw new Zn("One of your code blocks includes unescaped HTML.", u.innerHTML);
          A = u;
          const q = A.textContent,
            te = P ? j(q, { language: P, ignoreIllegals: !0 }) : Vn(q);
          (u.innerHTML = te.value),
            (u.dataset.highlighted = "yes"),
            Lr(u, P, te.language),
            (u.result = { language: te.language, re: te.relevance, relevance: te.relevance }),
            te.secondBest && (u.secondBest = { language: te.secondBest.language, relevance: te.secondBest.relevance }),
            Je("after:highlightElement", { el: u, result: te, text: q });
        }
        function Dr(u) {
          v = Ve(v, u);
        }
        const Br = () => {
          Qe(), ue("10.6.0", "initHighlighting() deprecated.  Use highlightAll() now.");
        };
        function Ur() {
          Qe(), ue("10.6.0", "initHighlightingOnLoad() deprecated.  Use highlightAll() now.");
        }
        let Ft = !1;
        function Qe() {
          function u() {
            Qe();
          }
          if (document.readyState === "loading") {
            Ft || window.addEventListener("DOMContentLoaded", u, !1), (Ft = !0);
            return;
          }
          document.querySelectorAll(v.cssSelector).forEach(Qn);
        }
        function Pr(u, A) {
          let P = null;
          try {
            P = A(n);
          } catch (q) {
            if ((ve("Language definition for '{}' could not be registered.".replace("{}", u)), V)) ve(q);
            else throw q;
            P = S;
          }
          P.name || (P.name = u),
            (i[u] = P),
            (P.rawDefinition = A.bind(null, n)),
            P.aliases && zt(P.aliases, { languageName: u });
        }
        function Fr(u) {
          delete i[u];
          for (const A of Object.keys(m)) m[A] === u && delete m[A];
        }
        function zr() {
          return Object.keys(i);
        }
        function Ae(u) {
          return (u = (u || "").toLowerCase()), i[u] || i[m[u]];
        }
        function zt(u, { languageName: A }) {
          typeof u == "string" && (u = [u]),
            u.forEach((P) => {
              m[P.toLowerCase()] = A;
            });
        }
        function $t(u) {
          const A = Ae(u);
          return A && !A.disableAutodetect;
        }
        function $r(u) {
          u["before:highlightBlock"] &&
            !u["before:highlightElement"] &&
            (u["before:highlightElement"] = (A) => {
              u["before:highlightBlock"](Object.assign({ block: A.el }, A));
            }),
            u["after:highlightBlock"] &&
              !u["after:highlightElement"] &&
              (u["after:highlightElement"] = (A) => {
                u["after:highlightBlock"](Object.assign({ block: A.el }, A));
              });
        }
        function Kr(u) {
          $r(u), C.push(u);
        }
        function Gr(u) {
          const A = C.indexOf(u);
          A !== -1 && C.splice(A, 1);
        }
        function Je(u, A) {
          const P = u;
          C.forEach(function (q) {
            q[P] && q[P](A);
          });
        }
        function qr(u) {
          return (
            ue("10.7.0", "highlightBlock will be removed entirely in v12.0"),
            ue("10.7.0", "Please use highlightElement now."),
            Qn(u)
          );
        }
        Object.assign(n, {
          highlight: j,
          highlightAuto: Vn,
          highlightAll: Qe,
          highlightElement: Qn,
          highlightBlock: qr,
          configure: Dr,
          initHighlighting: Br,
          initHighlightingOnLoad: Ur,
          registerLanguage: Pr,
          unregisterLanguage: Fr,
          listLanguages: zr,
          getLanguage: Ae,
          registerAliases: zt,
          autoDetection: $t,
          inherit: Ve,
          addPlugin: Kr,
          removePlugin: Gr,
        }),
          (n.debugMode = function () {
            V = !1;
          }),
          (n.safeMode = function () {
            V = !0;
          }),
          (n.versionString = Yn),
          (n.regex = { concat: y, lookahead: g, either: T, optional: E, anyNumberOfTimes: f });
        for (const u in ae) typeof ae[u] == "object" && r(ae[u]);
        return Object.assign(n, ae), n;
      },
      Oe = we({});
    return (Oe.newInstance = () => we({})), (tn = Oe), (Oe.HighlightJS = Oe), (Oe.default = Oe), tn;
  }
  var rn, tt;
  function Xt() {
    if (tt) return rn;
    tt = 1;
    function r(e) {
      const t = e.regex,
        o = t.concat(/[\p{L}_]/u, t.optional(/[\p{L}0-9_.-]*:/u), /[\p{L}0-9_.-]*/u),
        d = /[\p{L}0-9._:-]+/u,
        c = { className: "symbol", begin: /&[a-z]+;|&#[0-9]+;|&#x[a-f0-9]+;/ },
        b = { begin: /\s/, contains: [{ className: "keyword", begin: /#?[a-z_][a-z1-9_-]+/, illegal: /\n/ }] },
        _ = e.inherit(b, { begin: /\(/, end: /\)/ }),
        p = e.inherit(e.APOS_STRING_MODE, { className: "string" }),
        s = e.inherit(e.QUOTE_STRING_MODE, { className: "string" }),
        a = {
          endsWithParent: !0,
          illegal: /</,
          relevance: 0,
          contains: [
            { className: "attr", begin: d, relevance: 0 },
            {
              begin: /=\s*/,
              relevance: 0,
              contains: [
                {
                  className: "string",
                  endsParent: !0,
                  variants: [
                    { begin: /"/, end: /"/, contains: [c] },
                    { begin: /'/, end: /'/, contains: [c] },
                    { begin: /[^\s"'=<>`]+/ },
                  ],
                },
              ],
            },
          ],
        };
      return {
        name: "HTML, XML",
        aliases: ["html", "xhtml", "rss", "atom", "xjb", "xsd", "xsl", "plist", "wsf", "svg"],
        case_insensitive: !0,
        unicodeRegex: !0,
        contains: [
          {
            className: "meta",
            begin: /<![a-z]/,
            end: />/,
            relevance: 10,
            contains: [
              b,
              s,
              p,
              _,
              {
                begin: /\[/,
                end: /\]/,
                contains: [{ className: "meta", begin: /<![a-z]/, end: />/, contains: [b, _, s, p] }],
              },
            ],
          },
          e.COMMENT(/<!--/, /-->/, { relevance: 10 }),
          { begin: /<!\[CDATA\[/, end: /\]\]>/, relevance: 10 },
          c,
          {
            className: "meta",
            end: /\?>/,
            variants: [{ begin: /<\?xml/, relevance: 10, contains: [s] }, { begin: /<\?[a-z][a-z0-9]+/ }],
          },
          {
            className: "tag",
            begin: /<style(?=\s|>)/,
            end: />/,
            keywords: { name: "style" },
            contains: [a],
            starts: { end: /<\/style>/, returnEnd: !0, subLanguage: ["css", "xml"] },
          },
          {
            className: "tag",
            begin: /<script(?=\s|>)/,
            end: />/,
            keywords: { name: "script" },
            contains: [a],
            starts: { end: /<\/script>/, returnEnd: !0, subLanguage: ["javascript", "handlebars", "xml"] },
          },
          { className: "tag", begin: /<>|<\/>/ },
          {
            className: "tag",
            begin: t.concat(/</, t.lookahead(t.concat(o, t.either(/\/>/, />/, /\s/)))),
            end: /\/?>/,
            contains: [{ className: "name", begin: o, relevance: 0, starts: a }],
          },
          {
            className: "tag",
            begin: t.concat(/<\//, t.lookahead(t.concat(o, />/))),
            contains: [
              { className: "name", begin: o, relevance: 0 },
              { begin: />/, relevance: 0, endsParent: !0 },
            ],
          },
        ],
      };
    }
    return (rn = r), rn;
  }
  var an, rt;
  function Vt() {
    if (rt) return an;
    rt = 1;
    function r(e) {
      const t = e.regex,
        o = {},
        d = { begin: /\$\{/, end: /\}/, contains: ["self", { begin: /:-/, contains: [o] }] };
      Object.assign(o, {
        className: "variable",
        variants: [{ begin: t.concat(/\$[\w\d#@][\w\d_]*/, "(?![\\w\\d])(?![$])") }, d],
      });
      const c = { className: "subst", begin: /\$\(/, end: /\)/, contains: [e.BACKSLASH_ESCAPE] },
        b = e.inherit(e.COMMENT(), { match: [/(^|\s)/, /#.*$/], scope: { 2: "comment" } }),
        _ = {
          begin: /<<-?\s*(?=\w+)/,
          starts: { contains: [e.END_SAME_AS_BEGIN({ begin: /(\w+)/, end: /(\w+)/, className: "string" })] },
        },
        p = { className: "string", begin: /"/, end: /"/, contains: [e.BACKSLASH_ESCAPE, o, c] };
      c.contains.push(p);
      const s = { match: /\\"/ },
        a = { className: "string", begin: /'/, end: /'/ },
        l = { match: /\\'/ },
        g = {
          begin: /\$?\(\(/,
          end: /\)\)/,
          contains: [{ begin: /\d+#[0-9a-f]+/, className: "number" }, e.NUMBER_MODE, o],
        },
        f = ["fish", "bash", "zsh", "sh", "csh", "ksh", "tcsh", "dash", "scsh"],
        E = e.SHEBANG({ binary: "(".concat(f.join("|"), ")"), relevance: 10 }),
        y = {
          className: "function",
          begin: /\w[\w\d_]*\s*\(\s*\)\s*\{/,
          returnBegin: !0,
          contains: [e.inherit(e.TITLE_MODE, { begin: /\w[\w\d_]*/ })],
          relevance: 0,
        },
        N = [
          "if",
          "then",
          "else",
          "elif",
          "fi",
          "time",
          "for",
          "while",
          "until",
          "in",
          "do",
          "done",
          "case",
          "esac",
          "coproc",
          "function",
          "select",
        ],
        T = ["true", "false"],
        O = { match: /(\/[a-z._-]+)+/ },
        R = [
          "break",
          "cd",
          "continue",
          "eval",
          "exec",
          "exit",
          "export",
          "getopts",
          "hash",
          "pwd",
          "readonly",
          "return",
          "shift",
          "test",
          "times",
          "trap",
          "umask",
          "unset",
        ],
        F = [
          "alias",
          "bind",
          "builtin",
          "caller",
          "command",
          "declare",
          "echo",
          "enable",
          "help",
          "let",
          "local",
          "logout",
          "mapfile",
          "printf",
          "read",
          "readarray",
          "source",
          "sudo",
          "type",
          "typeset",
          "ulimit",
          "unalias",
        ],
        x = [
          "autoload",
          "bg",
          "bindkey",
          "bye",
          "cap",
          "chdir",
          "clone",
          "comparguments",
          "compcall",
          "compctl",
          "compdescribe",
          "compfiles",
          "compgroups",
          "compquote",
          "comptags",
          "comptry",
          "compvalues",
          "dirs",
          "disable",
          "disown",
          "echotc",
          "echoti",
          "emulate",
          "fc",
          "fg",
          "float",
          "functions",
          "getcap",
          "getln",
          "history",
          "integer",
          "jobs",
          "kill",
          "limit",
          "log",
          "noglob",
          "popd",
          "print",
          "pushd",
          "pushln",
          "rehash",
          "sched",
          "setcap",
          "setopt",
          "stat",
          "suspend",
          "ttyctl",
          "unfunction",
          "unhash",
          "unlimit",
          "unsetopt",
          "vared",
          "wait",
          "whence",
          "where",
          "which",
          "zcompile",
          "zformat",
          "zftp",
          "zle",
          "zmodload",
          "zparseopts",
          "zprof",
          "zpty",
          "zregexparse",
          "zsocket",
          "zstyle",
          "ztcp",
        ],
        D = [
          "chcon",
          "chgrp",
          "chown",
          "chmod",
          "cp",
          "dd",
          "df",
          "dir",
          "dircolors",
          "ln",
          "ls",
          "mkdir",
          "mkfifo",
          "mknod",
          "mktemp",
          "mv",
          "realpath",
          "rm",
          "rmdir",
          "shred",
          "sync",
          "touch",
          "truncate",
          "vdir",
          "b2sum",
          "base32",
          "base64",
          "cat",
          "cksum",
          "comm",
          "csplit",
          "cut",
          "expand",
          "fmt",
          "fold",
          "head",
          "join",
          "md5sum",
          "nl",
          "numfmt",
          "od",
          "paste",
          "ptx",
          "pr",
          "sha1sum",
          "sha224sum",
          "sha256sum",
          "sha384sum",
          "sha512sum",
          "shuf",
          "sort",
          "split",
          "sum",
          "tac",
          "tail",
          "tr",
          "tsort",
          "unexpand",
          "uniq",
          "wc",
          "arch",
          "basename",
          "chroot",
          "date",
          "dirname",
          "du",
          "echo",
          "env",
          "expr",
          "factor",
          "groups",
          "hostid",
          "id",
          "link",
          "logname",
          "nice",
          "nohup",
          "nproc",
          "pathchk",
          "pinky",
          "printenv",
          "printf",
          "pwd",
          "readlink",
          "runcon",
          "seq",
          "sleep",
          "stat",
          "stdbuf",
          "stty",
          "tee",
          "test",
          "timeout",
          "tty",
          "uname",
          "unlink",
          "uptime",
          "users",
          "who",
          "whoami",
          "yes",
        ];
      return {
        name: "Bash",
        aliases: ["sh", "zsh"],
        keywords: {
          $pattern: /\b[a-z][a-z0-9._-]+\b/,
          keyword: N,
          literal: T,
          built_in: [...R, ...F, "set", "shopt", ...x, ...D],
        },
        contains: [E, e.SHEBANG(), y, g, b, _, O, p, s, a, l, o],
      };
    }
    return (an = r), an;
  }
  var sn, at;
  function Qt() {
    if (at) return sn;
    at = 1;
    function r(e) {
      const t = e.regex,
        o = e.COMMENT("//", "$", { contains: [{ begin: /\\\n/ }] }),
        d = "decltype\\(auto\\)",
        c = "[a-zA-Z_]\\w*::",
        _ = "(" + d + "|" + t.optional(c) + "[a-zA-Z_]\\w*" + t.optional("<[^<>]+>") + ")",
        p = { className: "type", variants: [{ begin: "\\b[a-z\\d_]*_t\\b" }, { match: /\batomic_[a-z]{3,6}\b/ }] },
        a = {
          className: "string",
          variants: [
            { begin: '(u8?|U|L)?"', end: '"', illegal: "\\n", contains: [e.BACKSLASH_ESCAPE] },
            {
              begin: "(u8?|U|L)?'(" + "\\\\(x[0-9A-Fa-f]{2}|u[0-9A-Fa-f]{4,8}|[0-7]{3}|\\S)" + "|.)",
              end: "'",
              illegal: ".",
            },
            e.END_SAME_AS_BEGIN({ begin: /(?:u8?|U|L)?R"([^()\\ ]{0,16})\(/, end: /\)([^()\\ ]{0,16})"/ }),
          ],
        },
        l = {
          className: "number",
          variants: [
            { match: /\b(0b[01']+)/ },
            { match: /(-?)\b([\d']+(\.[\d']*)?|\.[\d']+)((ll|LL|l|L)(u|U)?|(u|U)(ll|LL|l|L)?|f|F|b|B)/ },
            {
              match:
                /(-?)\b(0[xX][a-fA-F0-9]+(?:'[a-fA-F0-9]+)*(?:\.[a-fA-F0-9]*(?:'[a-fA-F0-9]*)*)?(?:[pP][-+]?[0-9]+)?(l|L)?(u|U)?)/,
            },
            { match: /(-?)\b\d+(?:'\d+)*(?:\.\d*(?:'\d*)*)?(?:[eE][-+]?\d+)?/ },
          ],
          relevance: 0,
        },
        g = {
          className: "meta",
          begin: /#\s*[a-z]+\b/,
          end: /$/,
          keywords: {
            keyword:
              "if else elif endif define undef warning error line pragma _Pragma ifdef ifndef elifdef elifndef include",
          },
          contains: [
            { begin: /\\\n/, relevance: 0 },
            e.inherit(a, { className: "string" }),
            { className: "string", begin: /<.*?>/ },
            o,
            e.C_BLOCK_COMMENT_MODE,
          ],
        },
        f = { className: "title", begin: t.optional(c) + e.IDENT_RE, relevance: 0 },
        E = t.optional(c) + e.IDENT_RE + "\\s*\\(",
        T = {
          keyword: [
            "asm",
            "auto",
            "break",
            "case",
            "continue",
            "default",
            "do",
            "else",
            "enum",
            "extern",
            "for",
            "fortran",
            "goto",
            "if",
            "inline",
            "register",
            "restrict",
            "return",
            "sizeof",
            "typeof",
            "typeof_unqual",
            "struct",
            "switch",
            "typedef",
            "union",
            "volatile",
            "while",
            "_Alignas",
            "_Alignof",
            "_Atomic",
            "_Generic",
            "_Noreturn",
            "_Static_assert",
            "_Thread_local",
            "alignas",
            "alignof",
            "noreturn",
            "static_assert",
            "thread_local",
            "_Pragma",
          ],
          type: [
            "float",
            "double",
            "signed",
            "unsigned",
            "int",
            "short",
            "long",
            "char",
            "void",
            "_Bool",
            "_BitInt",
            "_Complex",
            "_Imaginary",
            "_Decimal32",
            "_Decimal64",
            "_Decimal96",
            "_Decimal128",
            "_Decimal64x",
            "_Decimal128x",
            "_Float16",
            "_Float32",
            "_Float64",
            "_Float128",
            "_Float32x",
            "_Float64x",
            "_Float128x",
            "const",
            "static",
            "constexpr",
            "complex",
            "bool",
            "imaginary",
          ],
          literal: "true false NULL",
          built_in:
            "std string wstring cin cout cerr clog stdin stdout stderr stringstream istringstream ostringstream auto_ptr deque list queue stack vector map set pair bitset multiset multimap unordered_set unordered_map unordered_multiset unordered_multimap priority_queue make_pair array shared_ptr abort terminate abs acos asin atan2 atan calloc ceil cosh cos exit exp fabs floor fmod fprintf fputs free frexp fscanf future isalnum isalpha iscntrl isdigit isgraph islower isprint ispunct isspace isupper isxdigit tolower toupper labs ldexp log10 log malloc realloc memchr memcmp memcpy memset modf pow printf putchar puts scanf sinh sin snprintf sprintf sqrt sscanf strcat strchr strcmp strcpy strcspn strlen strncat strncmp strncpy strpbrk strrchr strspn strstr tanh tan vfprintf vprintf vsprintf endl initializer_list unique_ptr",
        },
        O = [g, p, o, e.C_BLOCK_COMMENT_MODE, l, a],
        R = {
          variants: [
            { begin: /=/, end: /;/ },
            { begin: /\(/, end: /\)/ },
            { beginKeywords: "new throw return else", end: /;/ },
          ],
          keywords: T,
          contains: O.concat([{ begin: /\(/, end: /\)/, keywords: T, contains: O.concat(["self"]), relevance: 0 }]),
          relevance: 0,
        },
        F = {
          begin: "(" + _ + "[\\*&\\s]+)+" + E,
          returnBegin: !0,
          end: /[{;=]/,
          excludeEnd: !0,
          keywords: T,
          illegal: /[^\w\s\*&:<>.]/,
          contains: [
            { begin: d, keywords: T, relevance: 0 },
            { begin: E, returnBegin: !0, contains: [e.inherit(f, { className: "title.function" })], relevance: 0 },
            { relevance: 0, match: /,/ },
            {
              className: "params",
              begin: /\(/,
              end: /\)/,
              keywords: T,
              relevance: 0,
              contains: [
                o,
                e.C_BLOCK_COMMENT_MODE,
                a,
                l,
                p,
                {
                  begin: /\(/,
                  end: /\)/,
                  keywords: T,
                  relevance: 0,
                  contains: ["self", o, e.C_BLOCK_COMMENT_MODE, a, l, p],
                },
              ],
            },
            p,
            o,
            e.C_BLOCK_COMMENT_MODE,
            g,
          ],
        };
      return {
        name: "C",
        aliases: ["h"],
        keywords: T,
        disableAutodetect: !0,
        illegal: "</",
        contains: [].concat(R, F, O, [
          g,
          { begin: e.IDENT_RE + "::", keywords: T },
          {
            className: "class",
            beginKeywords: "enum class struct union",
            end: /[{;:<>=]/,
            contains: [{ beginKeywords: "final class struct" }, e.TITLE_MODE],
          },
        ]),
        exports: { preprocessor: g, strings: a, keywords: T },
      };
    }
    return (sn = r), sn;
  }
  var on, it;
  function Jt() {
    if (it) return on;
    it = 1;
    function r(e) {
      const t = e.regex,
        o = e.COMMENT("//", "$", { contains: [{ begin: /\\\n/ }] }),
        d = "decltype\\(auto\\)",
        c = "[a-zA-Z_]\\w*::",
        _ = "(?!struct)(" + d + "|" + t.optional(c) + "[a-zA-Z_]\\w*" + t.optional("<[^<>]+>") + ")",
        p = { className: "type", begin: "\\b[a-z\\d_]*_t\\b" },
        a = {
          className: "string",
          variants: [
            { begin: '(u8?|U|L)?"', end: '"', illegal: "\\n", contains: [e.BACKSLASH_ESCAPE] },
            {
              begin: "(u8?|U|L)?'(" + "\\\\(x[0-9A-Fa-f]{2}|u[0-9A-Fa-f]{4,8}|[0-7]{3}|\\S)" + "|.)",
              end: "'",
              illegal: ".",
            },
            e.END_SAME_AS_BEGIN({ begin: /(?:u8?|U|L)?R"([^()\\ ]{0,16})\(/, end: /\)([^()\\ ]{0,16})"/ }),
          ],
        },
        l = {
          className: "number",
          variants: [
            {
              begin:
                "[+-]?(?:(?:[0-9](?:'?[0-9])*\\.(?:[0-9](?:'?[0-9])*)?|\\.[0-9](?:'?[0-9])*)(?:[Ee][+-]?[0-9](?:'?[0-9])*)?|[0-9](?:'?[0-9])*[Ee][+-]?[0-9](?:'?[0-9])*|0[Xx](?:[0-9A-Fa-f](?:'?[0-9A-Fa-f])*(?:\\.(?:[0-9A-Fa-f](?:'?[0-9A-Fa-f])*)?)?|\\.[0-9A-Fa-f](?:'?[0-9A-Fa-f])*)[Pp][+-]?[0-9](?:'?[0-9])*)(?:[Ff](?:16|32|64|128)?|(BF|bf)16|[Ll]|)",
            },
            {
              begin:
                "[+-]?\\b(?:0[Bb][01](?:'?[01])*|0[Xx][0-9A-Fa-f](?:'?[0-9A-Fa-f])*|0(?:'?[0-7])*|[1-9](?:'?[0-9])*)(?:[Uu](?:LL?|ll?)|[Uu][Zz]?|(?:LL?|ll?)[Uu]?|[Zz][Uu]|)",
            },
          ],
          relevance: 0,
        },
        g = {
          className: "meta",
          begin: /#\s*[a-z]+\b/,
          end: /$/,
          keywords: {
            keyword: "if else elif endif define undef warning error line pragma _Pragma ifdef ifndef include",
          },
          contains: [
            { begin: /\\\n/, relevance: 0 },
            e.inherit(a, { className: "string" }),
            { className: "string", begin: /<.*?>/ },
            o,
            e.C_BLOCK_COMMENT_MODE,
          ],
        },
        f = { className: "title", begin: t.optional(c) + e.IDENT_RE, relevance: 0 },
        E = t.optional(c) + e.IDENT_RE + "\\s*\\(",
        y = [
          "alignas",
          "alignof",
          "and",
          "and_eq",
          "asm",
          "atomic_cancel",
          "atomic_commit",
          "atomic_noexcept",
          "auto",
          "bitand",
          "bitor",
          "break",
          "case",
          "catch",
          "class",
          "co_await",
          "co_return",
          "co_yield",
          "compl",
          "concept",
          "const_cast|10",
          "consteval",
          "constexpr",
          "constinit",
          "continue",
          "decltype",
          "default",
          "delete",
          "do",
          "dynamic_cast|10",
          "else",
          "enum",
          "explicit",
          "export",
          "extern",
          "false",
          "final",
          "for",
          "friend",
          "goto",
          "if",
          "import",
          "inline",
          "module",
          "mutable",
          "namespace",
          "new",
          "noexcept",
          "not",
          "not_eq",
          "nullptr",
          "operator",
          "or",
          "or_eq",
          "override",
          "private",
          "protected",
          "public",
          "reflexpr",
          "register",
          "reinterpret_cast|10",
          "requires",
          "return",
          "sizeof",
          "static_assert",
          "static_cast|10",
          "struct",
          "switch",
          "synchronized",
          "template",
          "this",
          "thread_local",
          "throw",
          "transaction_safe",
          "transaction_safe_dynamic",
          "true",
          "try",
          "typedef",
          "typeid",
          "typename",
          "union",
          "using",
          "virtual",
          "volatile",
          "while",
          "xor",
          "xor_eq",
        ],
        N = [
          "bool",
          "char",
          "char16_t",
          "char32_t",
          "char8_t",
          "double",
          "float",
          "int",
          "long",
          "short",
          "void",
          "wchar_t",
          "unsigned",
          "signed",
          "const",
          "static",
        ],
        T = [
          "any",
          "auto_ptr",
          "barrier",
          "binary_semaphore",
          "bitset",
          "complex",
          "condition_variable",
          "condition_variable_any",
          "counting_semaphore",
          "deque",
          "false_type",
          "flat_map",
          "flat_set",
          "future",
          "imaginary",
          "initializer_list",
          "istringstream",
          "jthread",
          "latch",
          "lock_guard",
          "multimap",
          "multiset",
          "mutex",
          "optional",
          "ostringstream",
          "packaged_task",
          "pair",
          "promise",
          "priority_queue",
          "queue",
          "recursive_mutex",
          "recursive_timed_mutex",
          "scoped_lock",
          "set",
          "shared_future",
          "shared_lock",
          "shared_mutex",
          "shared_timed_mutex",
          "shared_ptr",
          "stack",
          "string_view",
          "stringstream",
          "timed_mutex",
          "thread",
          "true_type",
          "tuple",
          "unique_lock",
          "unique_ptr",
          "unordered_map",
          "unordered_multimap",
          "unordered_multiset",
          "unordered_set",
          "variant",
          "vector",
          "weak_ptr",
          "wstring",
          "wstring_view",
        ],
        O = [
          "abort",
          "abs",
          "acos",
          "apply",
          "as_const",
          "asin",
          "atan",
          "atan2",
          "calloc",
          "ceil",
          "cerr",
          "cin",
          "clog",
          "cos",
          "cosh",
          "cout",
          "declval",
          "endl",
          "exchange",
          "exit",
          "exp",
          "fabs",
          "floor",
          "fmod",
          "forward",
          "fprintf",
          "fputs",
          "free",
          "frexp",
          "fscanf",
          "future",
          "invoke",
          "isalnum",
          "isalpha",
          "iscntrl",
          "isdigit",
          "isgraph",
          "islower",
          "isprint",
          "ispunct",
          "isspace",
          "isupper",
          "isxdigit",
          "labs",
          "launder",
          "ldexp",
          "log",
          "log10",
          "make_pair",
          "make_shared",
          "make_shared_for_overwrite",
          "make_tuple",
          "make_unique",
          "malloc",
          "memchr",
          "memcmp",
          "memcpy",
          "memset",
          "modf",
          "move",
          "pow",
          "printf",
          "putchar",
          "puts",
          "realloc",
          "scanf",
          "sin",
          "sinh",
          "snprintf",
          "sprintf",
          "sqrt",
          "sscanf",
          "std",
          "stderr",
          "stdin",
          "stdout",
          "strcat",
          "strchr",
          "strcmp",
          "strcpy",
          "strcspn",
          "strlen",
          "strncat",
          "strncmp",
          "strncpy",
          "strpbrk",
          "strrchr",
          "strspn",
          "strstr",
          "swap",
          "tan",
          "tanh",
          "terminate",
          "to_underlying",
          "tolower",
          "toupper",
          "vfprintf",
          "visit",
          "vprintf",
          "vsprintf",
        ],
        x = {
          type: N,
          keyword: y,
          literal: ["NULL", "false", "nullopt", "nullptr", "true"],
          built_in: ["_Pragma"],
          _type_hints: T,
        },
        D = {
          className: "function.dispatch",
          relevance: 0,
          keywords: { _hint: O },
          begin: t.concat(
            /\b/,
            /(?!decltype)/,
            /(?!if)/,
            /(?!for)/,
            /(?!switch)/,
            /(?!while)/,
            e.IDENT_RE,
            t.lookahead(/(<[^<>]+>|)\s*\(/)
          ),
        },
        B = [D, g, p, o, e.C_BLOCK_COMMENT_MODE, l, a],
        M = {
          variants: [
            { begin: /=/, end: /;/ },
            { begin: /\(/, end: /\)/ },
            { beginKeywords: "new throw return else", end: /;/ },
          ],
          keywords: x,
          contains: B.concat([{ begin: /\(/, end: /\)/, keywords: x, contains: B.concat(["self"]), relevance: 0 }]),
          relevance: 0,
        },
        U = {
          className: "function",
          begin: "(" + _ + "[\\*&\\s]+)+" + E,
          returnBegin: !0,
          end: /[{;=]/,
          excludeEnd: !0,
          keywords: x,
          illegal: /[^\w\s\*&:<>.]/,
          contains: [
            { begin: d, keywords: x, relevance: 0 },
            { begin: E, returnBegin: !0, contains: [f], relevance: 0 },
            { begin: /::/, relevance: 0 },
            { begin: /:/, endsWithParent: !0, contains: [a, l] },
            { relevance: 0, match: /,/ },
            {
              className: "params",
              begin: /\(/,
              end: /\)/,
              keywords: x,
              relevance: 0,
              contains: [
                o,
                e.C_BLOCK_COMMENT_MODE,
                a,
                l,
                p,
                {
                  begin: /\(/,
                  end: /\)/,
                  keywords: x,
                  relevance: 0,
                  contains: ["self", o, e.C_BLOCK_COMMENT_MODE, a, l, p],
                },
              ],
            },
            p,
            o,
            e.C_BLOCK_COMMENT_MODE,
            g,
          ],
        };
      return {
        name: "C++",
        aliases: ["cc", "c++", "h++", "hpp", "hh", "hxx", "cxx"],
        keywords: x,
        illegal: "</",
        classNameAliases: { "function.dispatch": "built_in" },
        contains: [].concat(M, U, D, B, [
          g,
          {
            begin:
              "\\b(deque|list|queue|priority_queue|pair|stack|vector|map|set|bitset|multiset|multimap|unordered_map|unordered_set|unordered_multiset|unordered_multimap|array|tuple|optional|variant|function|flat_map|flat_set)\\s*<(?!<)",
            end: ">",
            keywords: x,
            contains: ["self", p],
          },
          { begin: e.IDENT_RE + "::", keywords: x },
          {
            match: [/\b(?:enum(?:\s+(?:class|struct))?|class|struct|union)/, /\s+/, /\w+/],
            className: { 1: "keyword", 3: "title.class" },
          },
        ]),
      };
    }
    return (on = r), on;
  }
  var cn, st;
  function jt() {
    if (st) return cn;
    st = 1;
    function r(e) {
      const t = [
          "bool",
          "byte",
          "char",
          "decimal",
          "delegate",
          "double",
          "dynamic",
          "enum",
          "float",
          "int",
          "long",
          "nint",
          "nuint",
          "object",
          "sbyte",
          "short",
          "string",
          "ulong",
          "uint",
          "ushort",
        ],
        o = [
          "public",
          "private",
          "protected",
          "static",
          "internal",
          "protected",
          "abstract",
          "async",
          "extern",
          "override",
          "unsafe",
          "virtual",
          "new",
          "sealed",
          "partial",
        ],
        d = ["default", "false", "null", "true"],
        c = [
          "abstract",
          "as",
          "base",
          "break",
          "case",
          "catch",
          "class",
          "const",
          "continue",
          "do",
          "else",
          "event",
          "explicit",
          "extern",
          "finally",
          "fixed",
          "for",
          "foreach",
          "goto",
          "if",
          "implicit",
          "in",
          "interface",
          "internal",
          "is",
          "lock",
          "namespace",
          "new",
          "operator",
          "out",
          "override",
          "params",
          "private",
          "protected",
          "public",
          "readonly",
          "record",
          "ref",
          "return",
          "scoped",
          "sealed",
          "sizeof",
          "stackalloc",
          "static",
          "struct",
          "switch",
          "this",
          "throw",
          "try",
          "typeof",
          "unchecked",
          "unsafe",
          "using",
          "virtual",
          "void",
          "volatile",
          "while",
        ],
        b = [
          "add",
          "alias",
          "and",
          "ascending",
          "args",
          "async",
          "await",
          "by",
          "descending",
          "dynamic",
          "equals",
          "file",
          "from",
          "get",
          "global",
          "group",
          "init",
          "into",
          "join",
          "let",
          "nameof",
          "not",
          "notnull",
          "on",
          "or",
          "orderby",
          "partial",
          "record",
          "remove",
          "required",
          "scoped",
          "select",
          "set",
          "unmanaged",
          "value|0",
          "var",
          "when",
          "where",
          "with",
          "yield",
        ],
        _ = { keyword: c.concat(b), built_in: t, literal: d },
        p = e.inherit(e.TITLE_MODE, { begin: "[a-zA-Z](\\.?\\w)*" }),
        s = {
          className: "number",
          variants: [
            { begin: "\\b(0b[01']+)" },
            { begin: "(-?)\\b([\\d']+(\\.[\\d']*)?|\\.[\\d']+)(u|U|l|L|ul|UL|f|F|b|B)" },
            { begin: "(-?)(\\b0[xX][a-fA-F0-9']+|(\\b[\\d']+(\\.[\\d']*)?|\\.[\\d']+)([eE][-+]?[\\d']+)?)" },
          ],
          relevance: 0,
        },
        a = { className: "string", begin: /"""("*)(?!")(.|\n)*?"""\1/, relevance: 1 },
        l = { className: "string", begin: '@"', end: '"', contains: [{ begin: '""' }] },
        g = e.inherit(l, { illegal: /\n/ }),
        f = { className: "subst", begin: /\{/, end: /\}/, keywords: _ },
        E = e.inherit(f, { illegal: /\n/ }),
        y = {
          className: "string",
          begin: /\$"/,
          end: '"',
          illegal: /\n/,
          contains: [{ begin: /\{\{/ }, { begin: /\}\}/ }, e.BACKSLASH_ESCAPE, E],
        },
        N = {
          className: "string",
          begin: /\$@"/,
          end: '"',
          contains: [{ begin: /\{\{/ }, { begin: /\}\}/ }, { begin: '""' }, f],
        },
        T = e.inherit(N, { illegal: /\n/, contains: [{ begin: /\{\{/ }, { begin: /\}\}/ }, { begin: '""' }, E] });
      (f.contains = [N, y, l, e.APOS_STRING_MODE, e.QUOTE_STRING_MODE, s, e.C_BLOCK_COMMENT_MODE]),
        (E.contains = [
          T,
          y,
          g,
          e.APOS_STRING_MODE,
          e.QUOTE_STRING_MODE,
          s,
          e.inherit(e.C_BLOCK_COMMENT_MODE, { illegal: /\n/ }),
        ]);
      const O = { variants: [a, N, y, l, e.APOS_STRING_MODE, e.QUOTE_STRING_MODE] },
        R = { begin: "<", end: ">", contains: [{ beginKeywords: "in out" }, p] },
        F = e.IDENT_RE + "(<" + e.IDENT_RE + "(\\s*,\\s*" + e.IDENT_RE + ")*>)?(\\[\\])?",
        x = { begin: "@" + e.IDENT_RE, relevance: 0 };
      return {
        name: "C#",
        aliases: ["cs", "c#"],
        keywords: _,
        illegal: /::/,
        contains: [
          e.COMMENT("///", "$", {
            returnBegin: !0,
            contains: [
              {
                className: "doctag",
                variants: [{ begin: "///", relevance: 0 }, { begin: "<!--|-->" }, { begin: "</?", end: ">" }],
              },
            ],
          }),
          e.C_LINE_COMMENT_MODE,
          e.C_BLOCK_COMMENT_MODE,
          {
            className: "meta",
            begin: "#",
            end: "$",
            keywords: {
              keyword: "if else elif endif define undef warning error line region endregion pragma checksum",
            },
          },
          O,
          s,
          {
            beginKeywords: "class interface",
            relevance: 0,
            end: /[{;=]/,
            illegal: /[^\s:,]/,
            contains: [{ beginKeywords: "where class" }, p, R, e.C_LINE_COMMENT_MODE, e.C_BLOCK_COMMENT_MODE],
          },
          {
            beginKeywords: "namespace",
            relevance: 0,
            end: /[{;=]/,
            illegal: /[^\s:]/,
            contains: [p, e.C_LINE_COMMENT_MODE, e.C_BLOCK_COMMENT_MODE],
          },
          {
            beginKeywords: "record",
            relevance: 0,
            end: /[{;=]/,
            illegal: /[^\s:]/,
            contains: [p, R, e.C_LINE_COMMENT_MODE, e.C_BLOCK_COMMENT_MODE],
          },
          {
            className: "meta",
            begin: "^\\s*\\[(?=[\\w])",
            excludeBegin: !0,
            end: "\\]",
            excludeEnd: !0,
            contains: [{ className: "string", begin: /"/, end: /"/ }],
          },
          { beginKeywords: "new return throw await else", relevance: 0 },
          {
            className: "function",
            begin: "(" + F + "\\s+)+" + e.IDENT_RE + "\\s*(<[^=]+>\\s*)?\\(",
            returnBegin: !0,
            end: /\s*[{;=]/,
            excludeEnd: !0,
            keywords: _,
            contains: [
              { beginKeywords: o.join(" "), relevance: 0 },
              {
                begin: e.IDENT_RE + "\\s*(<[^=]+>\\s*)?\\(",
                returnBegin: !0,
                contains: [e.TITLE_MODE, R],
                relevance: 0,
              },
              { match: /\(\)/ },
              {
                className: "params",
                begin: /\(/,
                end: /\)/,
                excludeBegin: !0,
                excludeEnd: !0,
                keywords: _,
                relevance: 0,
                contains: [O, s, e.C_BLOCK_COMMENT_MODE],
              },
              e.C_LINE_COMMENT_MODE,
              e.C_BLOCK_COMMENT_MODE,
            ],
          },
          x,
        ],
      };
    }
    return (cn = r), cn;
  }
  var ln, ot;
  function er() {
    if (ot) return ln;
    ot = 1;
    const r = (s) => ({
        IMPORTANT: { scope: "meta", begin: "!important" },
        BLOCK_COMMENT: s.C_BLOCK_COMMENT_MODE,
        HEXCOLOR: { scope: "number", begin: /#(([0-9a-fA-F]{3,4})|(([0-9a-fA-F]{2}){3,4}))\b/ },
        FUNCTION_DISPATCH: { className: "built_in", begin: /[\w-]+(?=\()/ },
        ATTRIBUTE_SELECTOR_MODE: {
          scope: "selector-attr",
          begin: /\[/,
          end: /\]/,
          illegal: "$",
          contains: [s.APOS_STRING_MODE, s.QUOTE_STRING_MODE],
        },
        CSS_NUMBER_MODE: {
          scope: "number",
          begin:
            s.NUMBER_RE +
            "(%|em|ex|ch|rem|vw|vh|vmin|vmax|cm|mm|in|pt|pc|px|deg|grad|rad|turn|s|ms|Hz|kHz|dpi|dpcm|dppx)?",
          relevance: 0,
        },
        CSS_VARIABLE: { className: "attr", begin: /--[A-Za-z_][A-Za-z0-9_-]*/ },
      }),
      e = [
        "a",
        "abbr",
        "address",
        "article",
        "aside",
        "audio",
        "b",
        "blockquote",
        "body",
        "button",
        "canvas",
        "caption",
        "cite",
        "code",
        "dd",
        "del",
        "details",
        "dfn",
        "div",
        "dl",
        "dt",
        "em",
        "fieldset",
        "figcaption",
        "figure",
        "footer",
        "form",
        "h1",
        "h2",
        "h3",
        "h4",
        "h5",
        "h6",
        "header",
        "hgroup",
        "html",
        "i",
        "iframe",
        "img",
        "input",
        "ins",
        "kbd",
        "label",
        "legend",
        "li",
        "main",
        "mark",
        "menu",
        "nav",
        "object",
        "ol",
        "optgroup",
        "option",
        "p",
        "picture",
        "q",
        "quote",
        "samp",
        "section",
        "select",
        "source",
        "span",
        "strong",
        "summary",
        "sup",
        "table",
        "tbody",
        "td",
        "textarea",
        "tfoot",
        "th",
        "thead",
        "time",
        "tr",
        "ul",
        "var",
        "video",
      ],
      t = [
        "defs",
        "g",
        "marker",
        "mask",
        "pattern",
        "svg",
        "switch",
        "symbol",
        "feBlend",
        "feColorMatrix",
        "feComponentTransfer",
        "feComposite",
        "feConvolveMatrix",
        "feDiffuseLighting",
        "feDisplacementMap",
        "feFlood",
        "feGaussianBlur",
        "feImage",
        "feMerge",
        "feMorphology",
        "feOffset",
        "feSpecularLighting",
        "feTile",
        "feTurbulence",
        "linearGradient",
        "radialGradient",
        "stop",
        "circle",
        "ellipse",
        "image",
        "line",
        "path",
        "polygon",
        "polyline",
        "rect",
        "text",
        "use",
        "textPath",
        "tspan",
        "foreignObject",
        "clipPath",
      ],
      o = [...e, ...t],
      d = [
        "any-hover",
        "any-pointer",
        "aspect-ratio",
        "color",
        "color-gamut",
        "color-index",
        "device-aspect-ratio",
        "device-height",
        "device-width",
        "display-mode",
        "forced-colors",
        "grid",
        "height",
        "hover",
        "inverted-colors",
        "monochrome",
        "orientation",
        "overflow-block",
        "overflow-inline",
        "pointer",
        "prefers-color-scheme",
        "prefers-contrast",
        "prefers-reduced-motion",
        "prefers-reduced-transparency",
        "resolution",
        "scan",
        "scripting",
        "update",
        "width",
        "min-width",
        "max-width",
        "min-height",
        "max-height",
      ]
        .sort()
        .reverse(),
      c = [
        "active",
        "any-link",
        "blank",
        "checked",
        "current",
        "default",
        "defined",
        "dir",
        "disabled",
        "drop",
        "empty",
        "enabled",
        "first",
        "first-child",
        "first-of-type",
        "fullscreen",
        "future",
        "focus",
        "focus-visible",
        "focus-within",
        "has",
        "host",
        "host-context",
        "hover",
        "indeterminate",
        "in-range",
        "invalid",
        "is",
        "lang",
        "last-child",
        "last-of-type",
        "left",
        "link",
        "local-link",
        "not",
        "nth-child",
        "nth-col",
        "nth-last-child",
        "nth-last-col",
        "nth-last-of-type",
        "nth-of-type",
        "only-child",
        "only-of-type",
        "optional",
        "out-of-range",
        "past",
        "placeholder-shown",
        "read-only",
        "read-write",
        "required",
        "right",
        "root",
        "scope",
        "target",
        "target-within",
        "user-invalid",
        "valid",
        "visited",
        "where",
      ]
        .sort()
        .reverse(),
      b = [
        "after",
        "backdrop",
        "before",
        "cue",
        "cue-region",
        "first-letter",
        "first-line",
        "grammar-error",
        "marker",
        "part",
        "placeholder",
        "selection",
        "slotted",
        "spelling-error",
      ]
        .sort()
        .reverse(),
      _ = [
        "accent-color",
        "align-content",
        "align-items",
        "align-self",
        "alignment-baseline",
        "all",
        "anchor-name",
        "animation",
        "animation-composition",
        "animation-delay",
        "animation-direction",
        "animation-duration",
        "animation-fill-mode",
        "animation-iteration-count",
        "animation-name",
        "animation-play-state",
        "animation-range",
        "animation-range-end",
        "animation-range-start",
        "animation-timeline",
        "animation-timing-function",
        "appearance",
        "aspect-ratio",
        "backdrop-filter",
        "backface-visibility",
        "background",
        "background-attachment",
        "background-blend-mode",
        "background-clip",
        "background-color",
        "background-image",
        "background-origin",
        "background-position",
        "background-position-x",
        "background-position-y",
        "background-repeat",
        "background-size",
        "baseline-shift",
        "block-size",
        "border",
        "border-block",
        "border-block-color",
        "border-block-end",
        "border-block-end-color",
        "border-block-end-style",
        "border-block-end-width",
        "border-block-start",
        "border-block-start-color",
        "border-block-start-style",
        "border-block-start-width",
        "border-block-style",
        "border-block-width",
        "border-bottom",
        "border-bottom-color",
        "border-bottom-left-radius",
        "border-bottom-right-radius",
        "border-bottom-style",
        "border-bottom-width",
        "border-collapse",
        "border-color",
        "border-end-end-radius",
        "border-end-start-radius",
        "border-image",
        "border-image-outset",
        "border-image-repeat",
        "border-image-slice",
        "border-image-source",
        "border-image-width",
        "border-inline",
        "border-inline-color",
        "border-inline-end",
        "border-inline-end-color",
        "border-inline-end-style",
        "border-inline-end-width",
        "border-inline-start",
        "border-inline-start-color",
        "border-inline-start-style",
        "border-inline-start-width",
        "border-inline-style",
        "border-inline-width",
        "border-left",
        "border-left-color",
        "border-left-style",
        "border-left-width",
        "border-radius",
        "border-right",
        "border-right-color",
        "border-right-style",
        "border-right-width",
        "border-spacing",
        "border-start-end-radius",
        "border-start-start-radius",
        "border-style",
        "border-top",
        "border-top-color",
        "border-top-left-radius",
        "border-top-right-radius",
        "border-top-style",
        "border-top-width",
        "border-width",
        "bottom",
        "box-align",
        "box-decoration-break",
        "box-direction",
        "box-flex",
        "box-flex-group",
        "box-lines",
        "box-ordinal-group",
        "box-orient",
        "box-pack",
        "box-shadow",
        "box-sizing",
        "break-after",
        "break-before",
        "break-inside",
        "caption-side",
        "caret-color",
        "clear",
        "clip",
        "clip-path",
        "clip-rule",
        "color",
        "color-interpolation",
        "color-interpolation-filters",
        "color-profile",
        "color-rendering",
        "color-scheme",
        "column-count",
        "column-fill",
        "column-gap",
        "column-rule",
        "column-rule-color",
        "column-rule-style",
        "column-rule-width",
        "column-span",
        "column-width",
        "columns",
        "contain",
        "contain-intrinsic-block-size",
        "contain-intrinsic-height",
        "contain-intrinsic-inline-size",
        "contain-intrinsic-size",
        "contain-intrinsic-width",
        "container",
        "container-name",
        "container-type",
        "content",
        "content-visibility",
        "counter-increment",
        "counter-reset",
        "counter-set",
        "cue",
        "cue-after",
        "cue-before",
        "cursor",
        "cx",
        "cy",
        "direction",
        "display",
        "dominant-baseline",
        "empty-cells",
        "enable-background",
        "field-sizing",
        "fill",
        "fill-opacity",
        "fill-rule",
        "filter",
        "flex",
        "flex-basis",
        "flex-direction",
        "flex-flow",
        "flex-grow",
        "flex-shrink",
        "flex-wrap",
        "float",
        "flood-color",
        "flood-opacity",
        "flow",
        "font",
        "font-display",
        "font-family",
        "font-feature-settings",
        "font-kerning",
        "font-language-override",
        "font-optical-sizing",
        "font-palette",
        "font-size",
        "font-size-adjust",
        "font-smooth",
        "font-smoothing",
        "font-stretch",
        "font-style",
        "font-synthesis",
        "font-synthesis-position",
        "font-synthesis-small-caps",
        "font-synthesis-style",
        "font-synthesis-weight",
        "font-variant",
        "font-variant-alternates",
        "font-variant-caps",
        "font-variant-east-asian",
        "font-variant-emoji",
        "font-variant-ligatures",
        "font-variant-numeric",
        "font-variant-position",
        "font-variation-settings",
        "font-weight",
        "forced-color-adjust",
        "gap",
        "glyph-orientation-horizontal",
        "glyph-orientation-vertical",
        "grid",
        "grid-area",
        "grid-auto-columns",
        "grid-auto-flow",
        "grid-auto-rows",
        "grid-column",
        "grid-column-end",
        "grid-column-start",
        "grid-gap",
        "grid-row",
        "grid-row-end",
        "grid-row-start",
        "grid-template",
        "grid-template-areas",
        "grid-template-columns",
        "grid-template-rows",
        "hanging-punctuation",
        "height",
        "hyphenate-character",
        "hyphenate-limit-chars",
        "hyphens",
        "icon",
        "image-orientation",
        "image-rendering",
        "image-resolution",
        "ime-mode",
        "initial-letter",
        "initial-letter-align",
        "inline-size",
        "inset",
        "inset-area",
        "inset-block",
        "inset-block-end",
        "inset-block-start",
        "inset-inline",
        "inset-inline-end",
        "inset-inline-start",
        "isolation",
        "justify-content",
        "justify-items",
        "justify-self",
        "kerning",
        "left",
        "letter-spacing",
        "lighting-color",
        "line-break",
        "line-height",
        "line-height-step",
        "list-style",
        "list-style-image",
        "list-style-position",
        "list-style-type",
        "margin",
        "margin-block",
        "margin-block-end",
        "margin-block-start",
        "margin-bottom",
        "margin-inline",
        "margin-inline-end",
        "margin-inline-start",
        "margin-left",
        "margin-right",
        "margin-top",
        "margin-trim",
        "marker",
        "marker-end",
        "marker-mid",
        "marker-start",
        "marks",
        "mask",
        "mask-border",
        "mask-border-mode",
        "mask-border-outset",
        "mask-border-repeat",
        "mask-border-slice",
        "mask-border-source",
        "mask-border-width",
        "mask-clip",
        "mask-composite",
        "mask-image",
        "mask-mode",
        "mask-origin",
        "mask-position",
        "mask-repeat",
        "mask-size",
        "mask-type",
        "masonry-auto-flow",
        "math-depth",
        "math-shift",
        "math-style",
        "max-block-size",
        "max-height",
        "max-inline-size",
        "max-width",
        "min-block-size",
        "min-height",
        "min-inline-size",
        "min-width",
        "mix-blend-mode",
        "nav-down",
        "nav-index",
        "nav-left",
        "nav-right",
        "nav-up",
        "none",
        "normal",
        "object-fit",
        "object-position",
        "offset",
        "offset-anchor",
        "offset-distance",
        "offset-path",
        "offset-position",
        "offset-rotate",
        "opacity",
        "order",
        "orphans",
        "outline",
        "outline-color",
        "outline-offset",
        "outline-style",
        "outline-width",
        "overflow",
        "overflow-anchor",
        "overflow-block",
        "overflow-clip-margin",
        "overflow-inline",
        "overflow-wrap",
        "overflow-x",
        "overflow-y",
        "overlay",
        "overscroll-behavior",
        "overscroll-behavior-block",
        "overscroll-behavior-inline",
        "overscroll-behavior-x",
        "overscroll-behavior-y",
        "padding",
        "padding-block",
        "padding-block-end",
        "padding-block-start",
        "padding-bottom",
        "padding-inline",
        "padding-inline-end",
        "padding-inline-start",
        "padding-left",
        "padding-right",
        "padding-top",
        "page",
        "page-break-after",
        "page-break-before",
        "page-break-inside",
        "paint-order",
        "pause",
        "pause-after",
        "pause-before",
        "perspective",
        "perspective-origin",
        "place-content",
        "place-items",
        "place-self",
        "pointer-events",
        "position",
        "position-anchor",
        "position-visibility",
        "print-color-adjust",
        "quotes",
        "r",
        "resize",
        "rest",
        "rest-after",
        "rest-before",
        "right",
        "rotate",
        "row-gap",
        "ruby-align",
        "ruby-position",
        "scale",
        "scroll-behavior",
        "scroll-margin",
        "scroll-margin-block",
        "scroll-margin-block-end",
        "scroll-margin-block-start",
        "scroll-margin-bottom",
        "scroll-margin-inline",
        "scroll-margin-inline-end",
        "scroll-margin-inline-start",
        "scroll-margin-left",
        "scroll-margin-right",
        "scroll-margin-top",
        "scroll-padding",
        "scroll-padding-block",
        "scroll-padding-block-end",
        "scroll-padding-block-start",
        "scroll-padding-bottom",
        "scroll-padding-inline",
        "scroll-padding-inline-end",
        "scroll-padding-inline-start",
        "scroll-padding-left",
        "scroll-padding-right",
        "scroll-padding-top",
        "scroll-snap-align",
        "scroll-snap-stop",
        "scroll-snap-type",
        "scroll-timeline",
        "scroll-timeline-axis",
        "scroll-timeline-name",
        "scrollbar-color",
        "scrollbar-gutter",
        "scrollbar-width",
        "shape-image-threshold",
        "shape-margin",
        "shape-outside",
        "shape-rendering",
        "speak",
        "speak-as",
        "src",
        "stop-color",
        "stop-opacity",
        "stroke",
        "stroke-dasharray",
        "stroke-dashoffset",
        "stroke-linecap",
        "stroke-linejoin",
        "stroke-miterlimit",
        "stroke-opacity",
        "stroke-width",
        "tab-size",
        "table-layout",
        "text-align",
        "text-align-all",
        "text-align-last",
        "text-anchor",
        "text-combine-upright",
        "text-decoration",
        "text-decoration-color",
        "text-decoration-line",
        "text-decoration-skip",
        "text-decoration-skip-ink",
        "text-decoration-style",
        "text-decoration-thickness",
        "text-emphasis",
        "text-emphasis-color",
        "text-emphasis-position",
        "text-emphasis-style",
        "text-indent",
        "text-justify",
        "text-orientation",
        "text-overflow",
        "text-rendering",
        "text-shadow",
        "text-size-adjust",
        "text-transform",
        "text-underline-offset",
        "text-underline-position",
        "text-wrap",
        "text-wrap-mode",
        "text-wrap-style",
        "timeline-scope",
        "top",
        "touch-action",
        "transform",
        "transform-box",
        "transform-origin",
        "transform-style",
        "transition",
        "transition-behavior",
        "transition-delay",
        "transition-duration",
        "transition-property",
        "transition-timing-function",
        "translate",
        "unicode-bidi",
        "user-modify",
        "user-select",
        "vector-effect",
        "vertical-align",
        "view-timeline",
        "view-timeline-axis",
        "view-timeline-inset",
        "view-timeline-name",
        "view-transition-name",
        "visibility",
        "voice-balance",
        "voice-duration",
        "voice-family",
        "voice-pitch",
        "voice-range",
        "voice-rate",
        "voice-stress",
        "voice-volume",
        "white-space",
        "white-space-collapse",
        "widows",
        "width",
        "will-change",
        "word-break",
        "word-spacing",
        "word-wrap",
        "writing-mode",
        "x",
        "y",
        "z-index",
        "zoom",
      ]
        .sort()
        .reverse();
    function p(s) {
      const a = s.regex,
        l = r(s),
        g = { begin: /-(webkit|moz|ms|o)-(?=[a-z])/ },
        f = "and or not only",
        E = /@-?\w[\w]*(-\w+)*/,
        y = "[a-zA-Z-][a-zA-Z0-9_-]*",
        N = [s.APOS_STRING_MODE, s.QUOTE_STRING_MODE];
      return {
        name: "CSS",
        case_insensitive: !0,
        illegal: /[=|'\$]/,
        keywords: { keyframePosition: "from to" },
        classNameAliases: { keyframePosition: "selector-tag" },
        contains: [
          l.BLOCK_COMMENT,
          g,
          l.CSS_NUMBER_MODE,
          { className: "selector-id", begin: /#[A-Za-z0-9_-]+/, relevance: 0 },
          { className: "selector-class", begin: "\\." + y, relevance: 0 },
          l.ATTRIBUTE_SELECTOR_MODE,
          {
            className: "selector-pseudo",
            variants: [{ begin: ":(" + c.join("|") + ")" }, { begin: ":(:)?(" + b.join("|") + ")" }],
          },
          l.CSS_VARIABLE,
          { className: "attribute", begin: "\\b(" + _.join("|") + ")\\b" },
          {
            begin: /:/,
            end: /[;}{]/,
            contains: [
              l.BLOCK_COMMENT,
              l.HEXCOLOR,
              l.IMPORTANT,
              l.CSS_NUMBER_MODE,
              ...N,
              {
                begin: /(url|data-uri)\(/,
                end: /\)/,
                relevance: 0,
                keywords: { built_in: "url data-uri" },
                contains: [...N, { className: "string", begin: /[^)]/, endsWithParent: !0, excludeEnd: !0 }],
              },
              l.FUNCTION_DISPATCH,
            ],
          },
          {
            begin: a.lookahead(/@/),
            end: "[{;]",
            relevance: 0,
            illegal: /:/,
            contains: [
              { className: "keyword", begin: E },
              {
                begin: /\s/,
                endsWithParent: !0,
                excludeEnd: !0,
                relevance: 0,
                keywords: { $pattern: /[a-z-]+/, keyword: f, attribute: d.join(" ") },
                contains: [{ begin: /[a-z-]+(?=:)/, className: "attribute" }, ...N, l.CSS_NUMBER_MODE],
              },
            ],
          },
          { className: "selector-tag", begin: "\\b(" + o.join("|") + ")\\b" },
        ],
      };
    }
    return (ln = p), ln;
  }
  var dn, ct;
  function nr() {
    if (ct) return dn;
    ct = 1;
    function r(e) {
      const t = e.regex,
        o = { begin: /<\/?[A-Za-z_]/, end: ">", subLanguage: "xml", relevance: 0 },
        d = { begin: "^[-\\*]{3,}", end: "$" },
        c = {
          className: "code",
          variants: [
            { begin: "(`{3,})[^`](.|\\n)*?\\1`*[ ]*" },
            { begin: "(~{3,})[^~](.|\\n)*?\\1~*[ ]*" },
            { begin: "```", end: "```+[ ]*$" },
            { begin: "~~~", end: "~~~+[ ]*$" },
            { begin: "`.+?`" },
            { begin: "(?=^( {4}|\\t))", contains: [{ begin: "^( {4}|\\t)", end: "(\\n)$" }], relevance: 0 },
          ],
        },
        b = { className: "bullet", begin: "^[ 	]*([*+-]|(\\d+\\.))(?=\\s+)", end: "\\s+", excludeEnd: !0 },
        _ = {
          begin: /^\[[^\n]+\]:/,
          returnBegin: !0,
          contains: [
            { className: "symbol", begin: /\[/, end: /\]/, excludeBegin: !0, excludeEnd: !0 },
            { className: "link", begin: /:\s*/, end: /$/, excludeBegin: !0 },
          ],
        },
        p = /[A-Za-z][A-Za-z0-9+.-]*/,
        s = {
          variants: [
            { begin: /\[.+?\]\[.*?\]/, relevance: 0 },
            { begin: /\[.+?\]\(((data|javascript|mailto):|(?:http|ftp)s?:\/\/).*?\)/, relevance: 2 },
            { begin: t.concat(/\[.+?\]\(/, p, /:\/\/.*?\)/), relevance: 2 },
            { begin: /\[.+?\]\([./?&#].*?\)/, relevance: 1 },
            { begin: /\[.*?\]\(.*?\)/, relevance: 0 },
          ],
          returnBegin: !0,
          contains: [
            { match: /\[(?=\])/ },
            { className: "string", relevance: 0, begin: "\\[", end: "\\]", excludeBegin: !0, returnEnd: !0 },
            { className: "link", relevance: 0, begin: "\\]\\(", end: "\\)", excludeBegin: !0, excludeEnd: !0 },
            { className: "symbol", relevance: 0, begin: "\\]\\[", end: "\\]", excludeBegin: !0, excludeEnd: !0 },
          ],
        },
        a = {
          className: "strong",
          contains: [],
          variants: [
            { begin: /_{2}(?!\s)/, end: /_{2}/ },
            { begin: /\*{2}(?!\s)/, end: /\*{2}/ },
          ],
        },
        l = {
          className: "emphasis",
          contains: [],
          variants: [
            { begin: /\*(?![*\s])/, end: /\*/ },
            { begin: /_(?![_\s])/, end: /_/, relevance: 0 },
          ],
        },
        g = e.inherit(a, { contains: [] }),
        f = e.inherit(l, { contains: [] });
      a.contains.push(f), l.contains.push(g);
      let E = [o, s];
      return (
        [a, l, g, f].forEach((O) => {
          O.contains = O.contains.concat(E);
        }),
        (E = E.concat(a, l)),
        {
          name: "Markdown",
          aliases: ["md", "mkdown", "mkd"],
          contains: [
            {
              className: "section",
              variants: [
                { begin: "^#{1,6}", end: "$", contains: E },
                {
                  begin: "(?=^.+?\\n[=-]{2,}$)",
                  contains: [{ begin: "^[=-]*$" }, { begin: "^", end: "\\n", contains: E }],
                },
              ],
            },
            o,
            b,
            a,
            l,
            { className: "quote", begin: "^>\\s+", contains: E, end: "$" },
            c,
            d,
            s,
            _,
            { scope: "literal", match: /&([a-zA-Z0-9]+|#[0-9]{1,7}|#[Xx][0-9a-fA-F]{1,6});/ },
          ],
        }
      );
    }
    return (dn = r), dn;
  }
  var un, lt;
  function tr() {
    if (lt) return un;
    lt = 1;
    function r(e) {
      const t = e.regex;
      return {
        name: "Diff",
        aliases: ["patch"],
        contains: [
          {
            className: "meta",
            relevance: 10,
            match: t.either(/^@@ +-\d+,\d+ +\+\d+,\d+ +@@/, /^\*\*\* +\d+,\d+ +\*\*\*\*$/, /^--- +\d+,\d+ +----$/),
          },
          {
            className: "comment",
            variants: [
              { begin: t.either(/Index: /, /^index/, /={3,}/, /^-{3}/, /^\*{3} /, /^\+{3}/, /^diff --git/), end: /$/ },
              { match: /^\*{15}$/ },
            ],
          },
          { className: "addition", begin: /^\+/, end: /$/ },
          { className: "deletion", begin: /^-/, end: /$/ },
          { className: "addition", begin: /^!/, end: /$/ },
        ],
      };
    }
    return (un = r), un;
  }
  var gn, dt;
  function rr() {
    if (dt) return gn;
    dt = 1;
    function r(e) {
      const t = e.regex,
        o = "([a-zA-Z_]\\w*[!?=]?|[-+~]@|<<|>>|=~|===?|<=>|[<>]=?|\\*\\*|[-/+%^&*~`|]|\\[\\]=?)",
        d = t.either(/\b([A-Z]+[a-z0-9]+)+/, /\b([A-Z]+[a-z0-9]+)+[A-Z]+/),
        c = t.concat(d, /(::\w+)*/),
        _ = {
          "variable.constant": ["__FILE__", "__LINE__", "__ENCODING__"],
          "variable.language": ["self", "super"],
          keyword: [
            "alias",
            "and",
            "begin",
            "BEGIN",
            "break",
            "case",
            "class",
            "defined",
            "do",
            "else",
            "elsif",
            "end",
            "END",
            "ensure",
            "for",
            "if",
            "in",
            "module",
            "next",
            "not",
            "or",
            "redo",
            "require",
            "rescue",
            "retry",
            "return",
            "then",
            "undef",
            "unless",
            "until",
            "when",
            "while",
            "yield",
            ...["include", "extend", "prepend", "public", "private", "protected", "raise", "throw"],
          ],
          built_in: [
            "proc",
            "lambda",
            "attr_accessor",
            "attr_reader",
            "attr_writer",
            "define_method",
            "private_constant",
            "module_function",
          ],
          literal: ["true", "false", "nil"],
        },
        p = { className: "doctag", begin: "@[A-Za-z]+" },
        s = { begin: "#<", end: ">" },
        a = [
          e.COMMENT("#", "$", { contains: [p] }),
          e.COMMENT("^=begin", "^=end", { contains: [p], relevance: 10 }),
          e.COMMENT("^__END__", e.MATCH_NOTHING_RE),
        ],
        l = { className: "subst", begin: /#\{/, end: /\}/, keywords: _ },
        g = {
          className: "string",
          contains: [e.BACKSLASH_ESCAPE, l],
          variants: [
            { begin: /'/, end: /'/ },
            { begin: /"/, end: /"/ },
            { begin: /`/, end: /`/ },
            { begin: /%[qQwWx]?\(/, end: /\)/ },
            { begin: /%[qQwWx]?\[/, end: /\]/ },
            { begin: /%[qQwWx]?\{/, end: /\}/ },
            { begin: /%[qQwWx]?</, end: />/ },
            { begin: /%[qQwWx]?\//, end: /\// },
            { begin: /%[qQwWx]?%/, end: /%/ },
            { begin: /%[qQwWx]?-/, end: /-/ },
            { begin: /%[qQwWx]?\|/, end: /\|/ },
            { begin: /\B\?(\\\d{1,3})/ },
            { begin: /\B\?(\\x[A-Fa-f0-9]{1,2})/ },
            { begin: /\B\?(\\u\{?[A-Fa-f0-9]{1,6}\}?)/ },
            { begin: /\B\?(\\M-\\C-|\\M-\\c|\\c\\M-|\\M-|\\C-\\M-)[\x20-\x7e]/ },
            { begin: /\B\?\\(c|C-)[\x20-\x7e]/ },
            { begin: /\B\?\\?\S/ },
            {
              begin: t.concat(/<<[-~]?'?/, t.lookahead(/(\w+)(?=\W)[^\n]*\n(?:[^\n]*\n)*?\s*\1\b/)),
              contains: [e.END_SAME_AS_BEGIN({ begin: /(\w+)/, end: /(\w+)/, contains: [e.BACKSLASH_ESCAPE, l] })],
            },
          ],
        },
        f = "[1-9](_?[0-9])*|0",
        E = "[0-9](_?[0-9])*",
        y = {
          className: "number",
          relevance: 0,
          variants: [
            { begin: "\\b(".concat(f, ")(\\.(").concat(E, "))?([eE][+-]?(").concat(E, ")|r)?i?\\b") },
            { begin: "\\b0[dD][0-9](_?[0-9])*r?i?\\b" },
            { begin: "\\b0[bB][0-1](_?[0-1])*r?i?\\b" },
            { begin: "\\b0[oO][0-7](_?[0-7])*r?i?\\b" },
            { begin: "\\b0[xX][0-9a-fA-F](_?[0-9a-fA-F])*r?i?\\b" },
            { begin: "\\b0(_?[0-7])+r?i?\\b" },
          ],
        },
        N = {
          variants: [
            { match: /\(\)/ },
            { className: "params", begin: /\(/, end: /(?=\))/, excludeBegin: !0, endsParent: !0, keywords: _ },
          ],
        },
        B = [
          g,
          {
            variants: [{ match: [/class\s+/, c, /\s+<\s+/, c] }, { match: [/\b(class|module)\s+/, c] }],
            scope: { 2: "title.class", 4: "title.class.inherited" },
            keywords: _,
          },
          { match: [/(include|extend)\s+/, c], scope: { 2: "title.class" }, keywords: _ },
          { relevance: 0, match: [c, /\.new[. (]/], scope: { 1: "title.class" } },
          { relevance: 0, match: /\b[A-Z][A-Z_0-9]+\b/, className: "variable.constant" },
          { relevance: 0, match: d, scope: "title.class" },
          { match: [/def/, /\s+/, o], scope: { 1: "keyword", 3: "title.function" }, contains: [N] },
          { begin: e.IDENT_RE + "::" },
          { className: "symbol", begin: e.UNDERSCORE_IDENT_RE + "(!|\\?)?:", relevance: 0 },
          { className: "symbol", begin: ":(?!\\s)", contains: [g, { begin: o }], relevance: 0 },
          y,
          { className: "variable", begin: "(\\$\\W)|((\\$|@@?)(\\w+))(?=[^@$?])(?![A-Za-z])(?![@$?'])" },
          {
            className: "params",
            begin: /\|(?!=)/,
            end: /\|/,
            excludeBegin: !0,
            excludeEnd: !0,
            relevance: 0,
            keywords: _,
          },
          {
            begin: "(" + e.RE_STARTERS_RE + "|unless)\\s*",
            keywords: "unless",
            contains: [
              {
                className: "regexp",
                contains: [e.BACKSLASH_ESCAPE, l],
                illegal: /\n/,
                variants: [
                  { begin: "/", end: "/[a-z]*" },
                  { begin: /%r\{/, end: /\}[a-z]*/ },
                  { begin: "%r\\(", end: "\\)[a-z]*" },
                  { begin: "%r!", end: "![a-z]*" },
                  { begin: "%r\\[", end: "\\][a-z]*" },
                ],
              },
            ].concat(s, a),
            relevance: 0,
          },
        ].concat(s, a);
      (l.contains = B), (N.contains = B);
      const G = [
        { begin: /^\s*=>/, starts: { end: "$", contains: B } },
        {
          className: "meta.prompt",
          begin:
            "^(" +
            "[>?]>" +
            "|" +
            "[\\w#]+\\(\\w+\\):\\d+:\\d+[>*]" +
            "|" +
            "(\\w+-)?\\d+\\.\\d+\\.\\d+(p\\d+)?[^\\d][^>]+>" +
            ")(?=[ ])",
          starts: { end: "$", keywords: _, contains: B },
        },
      ];
      return (
        a.unshift(s),
        {
          name: "Ruby",
          aliases: ["rb", "gemspec", "podspec", "thor", "irb"],
          keywords: _,
          illegal: /\/\*/,
          contains: [e.SHEBANG({ binary: "ruby" })].concat(G).concat(a).concat(B),
        }
      );
    }
    return (gn = r), gn;
  }
  var bn, ut;
  function ar() {
    if (ut) return bn;
    ut = 1;
    function r(e) {
      const b = {
        keyword: [
          "break",
          "case",
          "chan",
          "const",
          "continue",
          "default",
          "defer",
          "else",
          "fallthrough",
          "for",
          "func",
          "go",
          "goto",
          "if",
          "import",
          "interface",
          "map",
          "package",
          "range",
          "return",
          "select",
          "struct",
          "switch",
          "type",
          "var",
        ],
        type: [
          "bool",
          "byte",
          "complex64",
          "complex128",
          "error",
          "float32",
          "float64",
          "int8",
          "int16",
          "int32",
          "int64",
          "string",
          "uint8",
          "uint16",
          "uint32",
          "uint64",
          "int",
          "uint",
          "uintptr",
          "rune",
        ],
        literal: ["true", "false", "iota", "nil"],
        built_in: [
          "append",
          "cap",
          "close",
          "complex",
          "copy",
          "imag",
          "len",
          "make",
          "new",
          "panic",
          "print",
          "println",
          "real",
          "recover",
          "delete",
        ],
      };
      return {
        name: "Go",
        aliases: ["golang"],
        keywords: b,
        illegal: "</",
        contains: [
          e.C_LINE_COMMENT_MODE,
          e.C_BLOCK_COMMENT_MODE,
          { className: "string", variants: [e.QUOTE_STRING_MODE, e.APOS_STRING_MODE, { begin: "`", end: "`" }] },
          {
            className: "number",
            variants: [
              { match: /-?\b0[xX]\.[a-fA-F0-9](_?[a-fA-F0-9])*[pP][+-]?\d(_?\d)*i?/, relevance: 0 },
              {
                match: /-?\b0[xX](_?[a-fA-F0-9])+((\.([a-fA-F0-9](_?[a-fA-F0-9])*)?)?[pP][+-]?\d(_?\d)*)?i?/,
                relevance: 0,
              },
              { match: /-?\b0[oO](_?[0-7])*i?/, relevance: 0 },
              { match: /-?\.\d(_?\d)*([eE][+-]?\d(_?\d)*)?i?/, relevance: 0 },
              { match: /-?\b\d(_?\d)*(\.(\d(_?\d)*)?)?([eE][+-]?\d(_?\d)*)?i?/, relevance: 0 },
            ],
          },
          { begin: /:=/ },
          {
            className: "function",
            beginKeywords: "func",
            end: "\\s*(\\{|$)",
            excludeEnd: !0,
            contains: [
              e.TITLE_MODE,
              { className: "params", begin: /\(/, end: /\)/, endsParent: !0, keywords: b, illegal: /["']/ },
            ],
          },
        ],
      };
    }
    return (bn = r), bn;
  }
  var pn, gt;
  function ir() {
    if (gt) return pn;
    gt = 1;
    function r(e) {
      const t = e.regex,
        o = /[_A-Za-z][_0-9A-Za-z]*/;
      return {
        name: "GraphQL",
        aliases: ["gql"],
        case_insensitive: !0,
        disableAutodetect: !1,
        keywords: {
          keyword: [
            "query",
            "mutation",
            "subscription",
            "type",
            "input",
            "schema",
            "directive",
            "interface",
            "union",
            "scalar",
            "fragment",
            "enum",
            "on",
          ],
          literal: ["true", "false", "null"],
        },
        contains: [
          e.HASH_COMMENT_MODE,
          e.QUOTE_STRING_MODE,
          e.NUMBER_MODE,
          { scope: "punctuation", match: /[.]{3}/, relevance: 0 },
          { scope: "punctuation", begin: /[\!\(\)\:\=\[\]\{\|\}]{1}/, relevance: 0 },
          { scope: "variable", begin: /\$/, end: /\W/, excludeEnd: !0, relevance: 0 },
          { scope: "meta", match: /@\w+/, excludeEnd: !0 },
          { scope: "symbol", begin: t.concat(o, t.lookahead(/\s*:/)), relevance: 0 },
        ],
        illegal: [/[;<']/, /BEGIN/],
      };
    }
    return (pn = r), pn;
  }
  var mn, bt;
  function sr() {
    if (bt) return mn;
    bt = 1;
    function r(e) {
      const t = e.regex,
        o = {
          className: "number",
          relevance: 0,
          variants: [{ begin: /([+-]+)?[\d]+_[\d_]+/ }, { begin: e.NUMBER_RE }],
        },
        d = e.COMMENT();
      d.variants = [
        { begin: /;/, end: /$/ },
        { begin: /#/, end: /$/ },
      ];
      const c = { className: "variable", variants: [{ begin: /\$[\w\d"][\w\d_]*/ }, { begin: /\$\{(.*?)\}/ }] },
        b = { className: "literal", begin: /\bon|off|true|false|yes|no\b/ },
        _ = {
          className: "string",
          contains: [e.BACKSLASH_ESCAPE],
          variants: [
            { begin: "'''", end: "'''", relevance: 10 },
            { begin: '"""', end: '"""', relevance: 10 },
            { begin: '"', end: '"' },
            { begin: "'", end: "'" },
          ],
        },
        p = { begin: /\[/, end: /\]/, contains: [d, b, c, _, o, "self"], relevance: 0 },
        s = /[A-Za-z0-9_-]+/,
        a = /"(\\"|[^"])*"/,
        l = /'[^']*'/,
        g = t.either(s, a, l),
        f = t.concat(g, "(\\s*\\.\\s*", g, ")*", t.lookahead(/\s*=\s*[^#\s]/));
      return {
        name: "TOML, also INI",
        aliases: ["toml"],
        case_insensitive: !0,
        illegal: /\S/,
        contains: [
          d,
          { className: "section", begin: /\[+/, end: /\]+/ },
          { begin: f, className: "attr", starts: { end: /$/, contains: [d, p, b, c, _, o] } },
        ],
      };
    }
    return (mn = r), mn;
  }
  var _n, pt;
  function or() {
    if (pt) return _n;
    pt = 1;
    var r = "[0-9](_*[0-9])*",
      e = "\\.(".concat(r, ")"),
      t = "[0-9a-fA-F](_*[0-9a-fA-F])*",
      o = {
        className: "number",
        variants: [
          {
            begin:
              "(\\b(".concat(r, ")((").concat(e, ")|\\.)?|(").concat(e, "))") + "[eE][+-]?(".concat(r, ")[fFdD]?\\b"),
          },
          { begin: "\\b(".concat(r, ")((").concat(e, ")[fFdD]?\\b|\\.([fFdD]\\b)?)") },
          { begin: "(".concat(e, ")[fFdD]?\\b") },
          { begin: "\\b(".concat(r, ")[fFdD]\\b") },
          {
            begin:
              "\\b0[xX]((".concat(t, ")\\.?|(").concat(t, ")?\\.(").concat(t, "))") +
              "[pP][+-]?(".concat(r, ")[fFdD]?\\b"),
          },
          { begin: "\\b(0|[1-9](_*[0-9])*)[lL]?\\b" },
          { begin: "\\b0[xX](".concat(t, ")[lL]?\\b") },
          { begin: "\\b0(_*[0-7])*[lL]?\\b" },
          { begin: "\\b0[bB][01](_*[01])*[lL]?\\b" },
        ],
        relevance: 0,
      };
    function d(b, _, p) {
      return p === -1 ? "" : b.replace(_, (s) => d(b, _, p - 1));
    }
    function c(b) {
      const _ = b.regex,
        p = "[À-ʸa-zA-Z_$][À-ʸa-zA-Z_$0-9]*",
        s = p + d("(?:<" + p + "~~~(?:\\s*,\\s*" + p + "~~~)*>)?", /~~~/g, 2),
        E = {
          keyword: [
            "synchronized",
            "abstract",
            "private",
            "var",
            "static",
            "if",
            "const ",
            "for",
            "while",
            "strictfp",
            "finally",
            "protected",
            "import",
            "native",
            "final",
            "void",
            "enum",
            "else",
            "break",
            "transient",
            "catch",
            "instanceof",
            "volatile",
            "case",
            "assert",
            "package",
            "default",
            "public",
            "try",
            "switch",
            "continue",
            "throws",
            "protected",
            "public",
            "private",
            "module",
            "requires",
            "exports",
            "do",
            "sealed",
            "yield",
            "permits",
            "goto",
            "when",
          ],
          literal: ["false", "true", "null"],
          type: ["char", "boolean", "long", "float", "int", "byte", "short", "double"],
          built_in: ["super", "this"],
        },
        y = { className: "meta", begin: "@" + p, contains: [{ begin: /\(/, end: /\)/, contains: ["self"] }] },
        N = {
          className: "params",
          begin: /\(/,
          end: /\)/,
          keywords: E,
          relevance: 0,
          contains: [b.C_BLOCK_COMMENT_MODE],
          endsParent: !0,
        };
      return {
        name: "Java",
        aliases: ["jsp"],
        keywords: E,
        illegal: /<\/|#/,
        contains: [
          b.COMMENT("/\\*\\*", "\\*/", {
            relevance: 0,
            contains: [
              { begin: /\w+@/, relevance: 0 },
              { className: "doctag", begin: "@[A-Za-z]+" },
            ],
          }),
          { begin: /import java\.[a-z]+\./, keywords: "import", relevance: 2 },
          b.C_LINE_COMMENT_MODE,
          b.C_BLOCK_COMMENT_MODE,
          { begin: /"""/, end: /"""/, className: "string", contains: [b.BACKSLASH_ESCAPE] },
          b.APOS_STRING_MODE,
          b.QUOTE_STRING_MODE,
          {
            match: [/\b(?:class|interface|enum|extends|implements|new)/, /\s+/, p],
            className: { 1: "keyword", 3: "title.class" },
          },
          { match: /non-sealed/, scope: "keyword" },
          {
            begin: [_.concat(/(?!else)/, p), /\s+/, p, /\s+/, /=(?!=)/],
            className: { 1: "type", 3: "variable", 5: "operator" },
          },
          {
            begin: [/record/, /\s+/, p],
            className: { 1: "keyword", 3: "title.class" },
            contains: [N, b.C_LINE_COMMENT_MODE, b.C_BLOCK_COMMENT_MODE],
          },
          { beginKeywords: "new throw return else", relevance: 0 },
          {
            begin: ["(?:" + s + "\\s+)", b.UNDERSCORE_IDENT_RE, /\s*(?=\()/],
            className: { 2: "title.function" },
            keywords: E,
            contains: [
              {
                className: "params",
                begin: /\(/,
                end: /\)/,
                keywords: E,
                relevance: 0,
                contains: [y, b.APOS_STRING_MODE, b.QUOTE_STRING_MODE, o, b.C_BLOCK_COMMENT_MODE],
              },
              b.C_LINE_COMMENT_MODE,
              b.C_BLOCK_COMMENT_MODE,
            ],
          },
          o,
          y,
        ],
      };
    }
    return (_n = c), _n;
  }
  var fn, mt;
  function cr() {
    if (mt) return fn;
    mt = 1;
    const r = "[A-Za-z$_][0-9A-Za-z$_]*",
      e = [
        "as",
        "in",
        "of",
        "if",
        "for",
        "while",
        "finally",
        "var",
        "new",
        "function",
        "do",
        "return",
        "void",
        "else",
        "break",
        "catch",
        "instanceof",
        "with",
        "throw",
        "case",
        "default",
        "try",
        "switch",
        "continue",
        "typeof",
        "delete",
        "let",
        "yield",
        "const",
        "class",
        "debugger",
        "async",
        "await",
        "static",
        "import",
        "from",
        "export",
        "extends",
        "using",
      ],
      t = ["true", "false", "null", "undefined", "NaN", "Infinity"],
      o = [
        "Object",
        "Function",
        "Boolean",
        "Symbol",
        "Math",
        "Date",
        "Number",
        "BigInt",
        "String",
        "RegExp",
        "Array",
        "Float32Array",
        "Float64Array",
        "Int8Array",
        "Uint8Array",
        "Uint8ClampedArray",
        "Int16Array",
        "Int32Array",
        "Uint16Array",
        "Uint32Array",
        "BigInt64Array",
        "BigUint64Array",
        "Set",
        "Map",
        "WeakSet",
        "WeakMap",
        "ArrayBuffer",
        "SharedArrayBuffer",
        "Atomics",
        "DataView",
        "JSON",
        "Promise",
        "Generator",
        "GeneratorFunction",
        "AsyncFunction",
        "Reflect",
        "Proxy",
        "Intl",
        "WebAssembly",
      ],
      d = [
        "Error",
        "EvalError",
        "InternalError",
        "RangeError",
        "ReferenceError",
        "SyntaxError",
        "TypeError",
        "URIError",
      ],
      c = [
        "setInterval",
        "setTimeout",
        "clearInterval",
        "clearTimeout",
        "require",
        "exports",
        "eval",
        "isFinite",
        "isNaN",
        "parseFloat",
        "parseInt",
        "decodeURI",
        "decodeURIComponent",
        "encodeURI",
        "encodeURIComponent",
        "escape",
        "unescape",
      ],
      b = [
        "arguments",
        "this",
        "super",
        "console",
        "window",
        "document",
        "localStorage",
        "sessionStorage",
        "module",
        "global",
      ],
      _ = [].concat(c, o, d);
    function p(s) {
      const a = s.regex,
        l = (X, { after: Y }) => {
          const le = "</" + X[0].slice(1);
          return X.input.indexOf(le, Y) !== -1;
        },
        g = r,
        f = { begin: "<>", end: "</>" },
        E = /<[A-Za-z0-9\\._:-]+\s*\/>/,
        y = {
          begin: /<[A-Za-z0-9\\._:-]+/,
          end: /\/[A-Za-z0-9\\._:-]+>|\/>/,
          isTrulyOpeningTag: (X, Y) => {
            const le = X[0].length + X.index,
              ae = X.input[le];
            if (ae === "<" || ae === ",") {
              Y.ignoreMatch();
              return;
            }
            ae === ">" && (l(X, { after: le }) || Y.ignoreMatch());
            let de;
            const ge = X.input.substring(le);
            if ((de = ge.match(/^\s*=/))) {
              Y.ignoreMatch();
              return;
            }
            if ((de = ge.match(/^\s+extends\s+/)) && de.index === 0) {
              Y.ignoreMatch();
              return;
            }
          },
        },
        N = { $pattern: r, keyword: e, literal: t, built_in: _, "variable.language": b },
        T = "[0-9](_?[0-9])*",
        O = "\\.(".concat(T, ")"),
        R = "0|[1-9](_?[0-9])*|0[0-7]*[89][0-9]*",
        F = {
          className: "number",
          variants: [
            { begin: "(\\b(".concat(R, ")((").concat(O, ")|\\.)?|(").concat(O, "))") + "[eE][+-]?(".concat(T, ")\\b") },
            { begin: "\\b(".concat(R, ")\\b((").concat(O, ")\\b|\\.)?|(").concat(O, ")\\b") },
            { begin: "\\b(0|[1-9](_?[0-9])*)n\\b" },
            { begin: "\\b0[xX][0-9a-fA-F](_?[0-9a-fA-F])*n?\\b" },
            { begin: "\\b0[bB][0-1](_?[0-1])*n?\\b" },
            { begin: "\\b0[oO][0-7](_?[0-7])*n?\\b" },
            { begin: "\\b0[0-7]+n?\\b" },
          ],
          relevance: 0,
        },
        x = { className: "subst", begin: "\\$\\{", end: "\\}", keywords: N, contains: [] },
        D = {
          begin: ".?html`",
          end: "",
          starts: { end: "`", returnEnd: !1, contains: [s.BACKSLASH_ESCAPE, x], subLanguage: "xml" },
        },
        B = {
          begin: ".?css`",
          end: "",
          starts: { end: "`", returnEnd: !1, contains: [s.BACKSLASH_ESCAPE, x], subLanguage: "css" },
        },
        M = {
          begin: ".?gql`",
          end: "",
          starts: { end: "`", returnEnd: !1, contains: [s.BACKSLASH_ESCAPE, x], subLanguage: "graphql" },
        },
        U = { className: "string", begin: "`", end: "`", contains: [s.BACKSLASH_ESCAPE, x] },
        G = {
          className: "comment",
          variants: [
            s.COMMENT(/\/\*\*(?!\/)/, "\\*/", {
              relevance: 0,
              contains: [
                {
                  begin: "(?=@[A-Za-z]+)",
                  relevance: 0,
                  contains: [
                    { className: "doctag", begin: "@[A-Za-z]+" },
                    { className: "type", begin: "\\{", end: "\\}", excludeEnd: !0, excludeBegin: !0, relevance: 0 },
                    { className: "variable", begin: g + "(?=\\s*(-)|$)", endsParent: !0, relevance: 0 },
                    { begin: /(?=[^\n])\s/, relevance: 0 },
                  ],
                },
              ],
            }),
            s.C_BLOCK_COMMENT_MODE,
            s.C_LINE_COMMENT_MODE,
          ],
        },
        re = [s.APOS_STRING_MODE, s.QUOTE_STRING_MODE, D, B, M, U, { match: /\$\d+/ }, F];
      x.contains = re.concat({ begin: /\{/, end: /\}/, keywords: N, contains: ["self"].concat(re) });
      const ee = [].concat(G, x.contains),
        ne = ee.concat([{ begin: /(\s*)\(/, end: /\)/, keywords: N, contains: ["self"].concat(ee) }]),
        W = {
          className: "params",
          begin: /(\s*)\(/,
          end: /\)/,
          excludeBegin: !0,
          excludeEnd: !0,
          keywords: N,
          contains: ne,
        },
        Z = {
          variants: [
            {
              match: [/class/, /\s+/, g, /\s+/, /extends/, /\s+/, a.concat(g, "(", a.concat(/\./, g), ")*")],
              scope: { 1: "keyword", 3: "title.class", 5: "keyword", 7: "title.class.inherited" },
            },
            { match: [/class/, /\s+/, g], scope: { 1: "keyword", 3: "title.class" } },
          ],
        },
        J = {
          relevance: 0,
          match: a.either(
            /\bJSON/,
            /\b[A-Z][a-z]+([A-Z][a-z]*|\d)*/,
            /\b[A-Z]{2,}([A-Z][a-z]+|\d)+([A-Z][a-z]*)*/,
            /\b[A-Z]{2,}[a-z]+([A-Z][a-z]+|\d)*([A-Z][a-z]*)*/
          ),
          className: "title.class",
          keywords: { _: [...o, ...d] },
        },
        se = { label: "use_strict", className: "meta", relevance: 10, begin: /^\s*['"]use (strict|asm)['"]/ },
        Me = {
          variants: [{ match: [/function/, /\s+/, g, /(?=\s*\()/] }, { match: [/function/, /\s*(?=\()/] }],
          className: { 1: "keyword", 3: "title.function" },
          label: "func.def",
          contains: [W],
          illegal: /%/,
        },
        ke = { relevance: 0, match: /\b[A-Z][A-Z_0-9]+\b/, className: "variable.constant" };
      function Ne(X) {
        return a.concat("(?!", X.join("|"), ")");
      }
      const Se = {
          match: a.concat(
            /\b/,
            Ne([...c, "super", "import"].map((X) => "".concat(X, "\\s*\\("))),
            g,
            a.lookahead(/\s*\(/)
          ),
          className: "title.function",
          relevance: 0,
        },
        Ce = {
          begin: a.concat(/\./, a.lookahead(a.concat(g, /(?![0-9A-Za-z$_(])/))),
          end: g,
          excludeBegin: !0,
          keywords: "prototype",
          className: "property",
          relevance: 0,
        },
        pe = {
          match: [/get|set/, /\s+/, g, /(?=\()/],
          className: { 1: "keyword", 3: "title.function" },
          contains: [{ begin: /\(\)/ }, W],
        },
        me = "(\\([^()]*(\\([^()]*(\\([^()]*\\)[^()]*)*\\)[^()]*)*\\)|" + s.UNDERSCORE_IDENT_RE + ")\\s*=>",
        ye = {
          match: [/const|var|let/, /\s+/, g, /\s*/, /=\s*/, /(async\s*)?/, a.lookahead(me)],
          keywords: "async",
          className: { 1: "keyword", 3: "title.function" },
          contains: [W],
        };
      return {
        name: "JavaScript",
        aliases: ["js", "jsx", "mjs", "cjs"],
        keywords: N,
        exports: { PARAMS_CONTAINS: ne, CLASS_REFERENCE: J },
        illegal: /#(?![$_A-z])/,
        contains: [
          s.SHEBANG({ label: "shebang", binary: "node", relevance: 5 }),
          se,
          s.APOS_STRING_MODE,
          s.QUOTE_STRING_MODE,
          D,
          B,
          M,
          U,
          G,
          { match: /\$\d+/ },
          F,
          J,
          { scope: "attr", match: g + a.lookahead(":"), relevance: 0 },
          ye,
          {
            begin: "(" + s.RE_STARTERS_RE + "|\\b(case|return|throw)\\b)\\s*",
            keywords: "return throw case",
            relevance: 0,
            contains: [
              G,
              s.REGEXP_MODE,
              {
                className: "function",
                begin: me,
                returnBegin: !0,
                end: "\\s*=>",
                contains: [
                  {
                    className: "params",
                    variants: [
                      { begin: s.UNDERSCORE_IDENT_RE, relevance: 0 },
                      { className: null, begin: /\(\s*\)/, skip: !0 },
                      { begin: /(\s*)\(/, end: /\)/, excludeBegin: !0, excludeEnd: !0, keywords: N, contains: ne },
                    ],
                  },
                ],
              },
              { begin: /,/, relevance: 0 },
              { match: /\s+/, relevance: 0 },
              {
                variants: [
                  { begin: f.begin, end: f.end },
                  { match: E },
                  { begin: y.begin, "on:begin": y.isTrulyOpeningTag, end: y.end },
                ],
                subLanguage: "xml",
                contains: [{ begin: y.begin, end: y.end, skip: !0, contains: ["self"] }],
              },
            ],
          },
          Me,
          { beginKeywords: "while if switch catch for" },
          {
            begin:
              "\\b(?!function)" +
              s.UNDERSCORE_IDENT_RE +
              "\\([^()]*(\\([^()]*(\\([^()]*\\)[^()]*)*\\)[^()]*)*\\)\\s*\\{",
            returnBegin: !0,
            label: "func.def",
            contains: [W, s.inherit(s.TITLE_MODE, { begin: g, className: "title.function" })],
          },
          { match: /\.\.\./, relevance: 0 },
          Ce,
          { match: "\\$" + g, relevance: 0 },
          { match: [/\bconstructor(?=\s*\()/], className: { 1: "title.function" }, contains: [W] },
          Se,
          ke,
          Z,
          pe,
          { match: /\$[(.]/ },
        ],
      };
    }
    return (fn = p), fn;
  }
  var En, _t;
  function lr() {
    if (_t) return En;
    _t = 1;
    function r(e) {
      const t = { className: "attr", begin: /"(\\.|[^\\"\r\n])*"(?=\s*:)/, relevance: 1.01 },
        o = { match: /[{}[\],:]/, className: "punctuation", relevance: 0 },
        d = ["true", "false", "null"],
        c = { scope: "literal", beginKeywords: d.join(" ") };
      return {
        name: "JSON",
        aliases: ["jsonc"],
        keywords: { literal: d },
        contains: [t, o, e.QUOTE_STRING_MODE, c, e.C_NUMBER_MODE, e.C_LINE_COMMENT_MODE, e.C_BLOCK_COMMENT_MODE],
        illegal: "\\S",
      };
    }
    return (En = r), En;
  }
  var hn, ft;
  function dr() {
    if (ft) return hn;
    ft = 1;
    var r = "[0-9](_*[0-9])*",
      e = "\\.(".concat(r, ")"),
      t = "[0-9a-fA-F](_*[0-9a-fA-F])*",
      o = {
        className: "number",
        variants: [
          {
            begin:
              "(\\b(".concat(r, ")((").concat(e, ")|\\.)?|(").concat(e, "))") + "[eE][+-]?(".concat(r, ")[fFdD]?\\b"),
          },
          { begin: "\\b(".concat(r, ")((").concat(e, ")[fFdD]?\\b|\\.([fFdD]\\b)?)") },
          { begin: "(".concat(e, ")[fFdD]?\\b") },
          { begin: "\\b(".concat(r, ")[fFdD]\\b") },
          {
            begin:
              "\\b0[xX]((".concat(t, ")\\.?|(").concat(t, ")?\\.(").concat(t, "))") +
              "[pP][+-]?(".concat(r, ")[fFdD]?\\b"),
          },
          { begin: "\\b(0|[1-9](_*[0-9])*)[lL]?\\b" },
          { begin: "\\b0[xX](".concat(t, ")[lL]?\\b") },
          { begin: "\\b0(_*[0-7])*[lL]?\\b" },
          { begin: "\\b0[bB][01](_*[01])*[lL]?\\b" },
        ],
        relevance: 0,
      };
    function d(c) {
      const b = {
          keyword:
            "abstract as val var vararg get set class object open private protected public noinline crossinline dynamic final enum if else do while for when throw try catch finally import package is in fun override companion reified inline lateinit init interface annotation data sealed internal infix operator out by constructor super tailrec where const inner suspend typealias external expect actual",
          built_in: "Byte Short Char Int Long Boolean Float Double Void Unit Nothing",
          literal: "true false null",
        },
        _ = {
          className: "keyword",
          begin: /\b(break|continue|return|this)\b/,
          starts: { contains: [{ className: "symbol", begin: /@\w+/ }] },
        },
        p = { className: "symbol", begin: c.UNDERSCORE_IDENT_RE + "@" },
        s = { className: "subst", begin: /\$\{/, end: /\}/, contains: [c.C_NUMBER_MODE] },
        a = { className: "variable", begin: "\\$" + c.UNDERSCORE_IDENT_RE },
        l = {
          className: "string",
          variants: [
            { begin: '"""', end: '"""(?=[^"])', contains: [a, s] },
            { begin: "'", end: "'", illegal: /\n/, contains: [c.BACKSLASH_ESCAPE] },
            { begin: '"', end: '"', illegal: /\n/, contains: [c.BACKSLASH_ESCAPE, a, s] },
          ],
        };
      s.contains.push(l);
      const g = {
          className: "meta",
          begin:
            "@(?:file|property|field|get|set|receiver|param|setparam|delegate)\\s*:(?:\\s*" +
            c.UNDERSCORE_IDENT_RE +
            ")?",
        },
        f = {
          className: "meta",
          begin: "@" + c.UNDERSCORE_IDENT_RE,
          contains: [{ begin: /\(/, end: /\)/, contains: [c.inherit(l, { className: "string" }), "self"] }],
        },
        E = o,
        y = c.COMMENT("/\\*", "\\*/", { contains: [c.C_BLOCK_COMMENT_MODE] }),
        N = {
          variants: [
            { className: "type", begin: c.UNDERSCORE_IDENT_RE },
            { begin: /\(/, end: /\)/, contains: [] },
          ],
        },
        T = N;
      return (
        (T.variants[1].contains = [N]),
        (N.variants[1].contains = [T]),
        {
          name: "Kotlin",
          aliases: ["kt", "kts"],
          keywords: b,
          contains: [
            c.COMMENT("/\\*\\*", "\\*/", { relevance: 0, contains: [{ className: "doctag", begin: "@[A-Za-z]+" }] }),
            c.C_LINE_COMMENT_MODE,
            y,
            _,
            p,
            g,
            f,
            {
              className: "function",
              beginKeywords: "fun",
              end: "[(]|$",
              returnBegin: !0,
              excludeEnd: !0,
              keywords: b,
              relevance: 5,
              contains: [
                {
                  begin: c.UNDERSCORE_IDENT_RE + "\\s*\\(",
                  returnBegin: !0,
                  relevance: 0,
                  contains: [c.UNDERSCORE_TITLE_MODE],
                },
                { className: "type", begin: /</, end: />/, keywords: "reified", relevance: 0 },
                {
                  className: "params",
                  begin: /\(/,
                  end: /\)/,
                  endsParent: !0,
                  keywords: b,
                  relevance: 0,
                  contains: [
                    {
                      begin: /:/,
                      end: /[=,\/]/,
                      endsWithParent: !0,
                      contains: [N, c.C_LINE_COMMENT_MODE, y],
                      relevance: 0,
                    },
                    c.C_LINE_COMMENT_MODE,
                    y,
                    g,
                    f,
                    l,
                    c.C_NUMBER_MODE,
                  ],
                },
                y,
              ],
            },
            {
              begin: [/class|interface|trait/, /\s+/, c.UNDERSCORE_IDENT_RE],
              beginScope: { 3: "title.class" },
              keywords: "class interface trait",
              end: /[:\{(]|$/,
              excludeEnd: !0,
              illegal: "extends implements",
              contains: [
                { beginKeywords: "public protected internal private constructor" },
                c.UNDERSCORE_TITLE_MODE,
                { className: "type", begin: /</, end: />/, excludeBegin: !0, excludeEnd: !0, relevance: 0 },
                { className: "type", begin: /[,:]\s*/, end: /[<\(,){\s]|$/, excludeBegin: !0, returnEnd: !0 },
                g,
                f,
              ],
            },
            l,
            { className: "meta", begin: "^#!/usr/bin/env", end: "$", illegal: "\n" },
            E,
          ],
        }
      );
    }
    return (hn = d), hn;
  }
  var Nn, Et;
  function ur() {
    if (Et) return Nn;
    Et = 1;
    const r = (a) => ({
        IMPORTANT: { scope: "meta", begin: "!important" },
        BLOCK_COMMENT: a.C_BLOCK_COMMENT_MODE,
        HEXCOLOR: { scope: "number", begin: /#(([0-9a-fA-F]{3,4})|(([0-9a-fA-F]{2}){3,4}))\b/ },
        FUNCTION_DISPATCH: { className: "built_in", begin: /[\w-]+(?=\()/ },
        ATTRIBUTE_SELECTOR_MODE: {
          scope: "selector-attr",
          begin: /\[/,
          end: /\]/,
          illegal: "$",
          contains: [a.APOS_STRING_MODE, a.QUOTE_STRING_MODE],
        },
        CSS_NUMBER_MODE: {
          scope: "number",
          begin:
            a.NUMBER_RE +
            "(%|em|ex|ch|rem|vw|vh|vmin|vmax|cm|mm|in|pt|pc|px|deg|grad|rad|turn|s|ms|Hz|kHz|dpi|dpcm|dppx)?",
          relevance: 0,
        },
        CSS_VARIABLE: { className: "attr", begin: /--[A-Za-z_][A-Za-z0-9_-]*/ },
      }),
      e = [
        "a",
        "abbr",
        "address",
        "article",
        "aside",
        "audio",
        "b",
        "blockquote",
        "body",
        "button",
        "canvas",
        "caption",
        "cite",
        "code",
        "dd",
        "del",
        "details",
        "dfn",
        "div",
        "dl",
        "dt",
        "em",
        "fieldset",
        "figcaption",
        "figure",
        "footer",
        "form",
        "h1",
        "h2",
        "h3",
        "h4",
        "h5",
        "h6",
        "header",
        "hgroup",
        "html",
        "i",
        "iframe",
        "img",
        "input",
        "ins",
        "kbd",
        "label",
        "legend",
        "li",
        "main",
        "mark",
        "menu",
        "nav",
        "object",
        "ol",
        "optgroup",
        "option",
        "p",
        "picture",
        "q",
        "quote",
        "samp",
        "section",
        "select",
        "source",
        "span",
        "strong",
        "summary",
        "sup",
        "table",
        "tbody",
        "td",
        "textarea",
        "tfoot",
        "th",
        "thead",
        "time",
        "tr",
        "ul",
        "var",
        "video",
      ],
      t = [
        "defs",
        "g",
        "marker",
        "mask",
        "pattern",
        "svg",
        "switch",
        "symbol",
        "feBlend",
        "feColorMatrix",
        "feComponentTransfer",
        "feComposite",
        "feConvolveMatrix",
        "feDiffuseLighting",
        "feDisplacementMap",
        "feFlood",
        "feGaussianBlur",
        "feImage",
        "feMerge",
        "feMorphology",
        "feOffset",
        "feSpecularLighting",
        "feTile",
        "feTurbulence",
        "linearGradient",
        "radialGradient",
        "stop",
        "circle",
        "ellipse",
        "image",
        "line",
        "path",
        "polygon",
        "polyline",
        "rect",
        "text",
        "use",
        "textPath",
        "tspan",
        "foreignObject",
        "clipPath",
      ],
      o = [...e, ...t],
      d = [
        "any-hover",
        "any-pointer",
        "aspect-ratio",
        "color",
        "color-gamut",
        "color-index",
        "device-aspect-ratio",
        "device-height",
        "device-width",
        "display-mode",
        "forced-colors",
        "grid",
        "height",
        "hover",
        "inverted-colors",
        "monochrome",
        "orientation",
        "overflow-block",
        "overflow-inline",
        "pointer",
        "prefers-color-scheme",
        "prefers-contrast",
        "prefers-reduced-motion",
        "prefers-reduced-transparency",
        "resolution",
        "scan",
        "scripting",
        "update",
        "width",
        "min-width",
        "max-width",
        "min-height",
        "max-height",
      ]
        .sort()
        .reverse(),
      c = [
        "active",
        "any-link",
        "blank",
        "checked",
        "current",
        "default",
        "defined",
        "dir",
        "disabled",
        "drop",
        "empty",
        "enabled",
        "first",
        "first-child",
        "first-of-type",
        "fullscreen",
        "future",
        "focus",
        "focus-visible",
        "focus-within",
        "has",
        "host",
        "host-context",
        "hover",
        "indeterminate",
        "in-range",
        "invalid",
        "is",
        "lang",
        "last-child",
        "last-of-type",
        "left",
        "link",
        "local-link",
        "not",
        "nth-child",
        "nth-col",
        "nth-last-child",
        "nth-last-col",
        "nth-last-of-type",
        "nth-of-type",
        "only-child",
        "only-of-type",
        "optional",
        "out-of-range",
        "past",
        "placeholder-shown",
        "read-only",
        "read-write",
        "required",
        "right",
        "root",
        "scope",
        "target",
        "target-within",
        "user-invalid",
        "valid",
        "visited",
        "where",
      ]
        .sort()
        .reverse(),
      b = [
        "after",
        "backdrop",
        "before",
        "cue",
        "cue-region",
        "first-letter",
        "first-line",
        "grammar-error",
        "marker",
        "part",
        "placeholder",
        "selection",
        "slotted",
        "spelling-error",
      ]
        .sort()
        .reverse(),
      _ = [
        "accent-color",
        "align-content",
        "align-items",
        "align-self",
        "alignment-baseline",
        "all",
        "anchor-name",
        "animation",
        "animation-composition",
        "animation-delay",
        "animation-direction",
        "animation-duration",
        "animation-fill-mode",
        "animation-iteration-count",
        "animation-name",
        "animation-play-state",
        "animation-range",
        "animation-range-end",
        "animation-range-start",
        "animation-timeline",
        "animation-timing-function",
        "appearance",
        "aspect-ratio",
        "backdrop-filter",
        "backface-visibility",
        "background",
        "background-attachment",
        "background-blend-mode",
        "background-clip",
        "background-color",
        "background-image",
        "background-origin",
        "background-position",
        "background-position-x",
        "background-position-y",
        "background-repeat",
        "background-size",
        "baseline-shift",
        "block-size",
        "border",
        "border-block",
        "border-block-color",
        "border-block-end",
        "border-block-end-color",
        "border-block-end-style",
        "border-block-end-width",
        "border-block-start",
        "border-block-start-color",
        "border-block-start-style",
        "border-block-start-width",
        "border-block-style",
        "border-block-width",
        "border-bottom",
        "border-bottom-color",
        "border-bottom-left-radius",
        "border-bottom-right-radius",
        "border-bottom-style",
        "border-bottom-width",
        "border-collapse",
        "border-color",
        "border-end-end-radius",
        "border-end-start-radius",
        "border-image",
        "border-image-outset",
        "border-image-repeat",
        "border-image-slice",
        "border-image-source",
        "border-image-width",
        "border-inline",
        "border-inline-color",
        "border-inline-end",
        "border-inline-end-color",
        "border-inline-end-style",
        "border-inline-end-width",
        "border-inline-start",
        "border-inline-start-color",
        "border-inline-start-style",
        "border-inline-start-width",
        "border-inline-style",
        "border-inline-width",
        "border-left",
        "border-left-color",
        "border-left-style",
        "border-left-width",
        "border-radius",
        "border-right",
        "border-right-color",
        "border-right-style",
        "border-right-width",
        "border-spacing",
        "border-start-end-radius",
        "border-start-start-radius",
        "border-style",
        "border-top",
        "border-top-color",
        "border-top-left-radius",
        "border-top-right-radius",
        "border-top-style",
        "border-top-width",
        "border-width",
        "bottom",
        "box-align",
        "box-decoration-break",
        "box-direction",
        "box-flex",
        "box-flex-group",
        "box-lines",
        "box-ordinal-group",
        "box-orient",
        "box-pack",
        "box-shadow",
        "box-sizing",
        "break-after",
        "break-before",
        "break-inside",
        "caption-side",
        "caret-color",
        "clear",
        "clip",
        "clip-path",
        "clip-rule",
        "color",
        "color-interpolation",
        "color-interpolation-filters",
        "color-profile",
        "color-rendering",
        "color-scheme",
        "column-count",
        "column-fill",
        "column-gap",
        "column-rule",
        "column-rule-color",
        "column-rule-style",
        "column-rule-width",
        "column-span",
        "column-width",
        "columns",
        "contain",
        "contain-intrinsic-block-size",
        "contain-intrinsic-height",
        "contain-intrinsic-inline-size",
        "contain-intrinsic-size",
        "contain-intrinsic-width",
        "container",
        "container-name",
        "container-type",
        "content",
        "content-visibility",
        "counter-increment",
        "counter-reset",
        "counter-set",
        "cue",
        "cue-after",
        "cue-before",
        "cursor",
        "cx",
        "cy",
        "direction",
        "display",
        "dominant-baseline",
        "empty-cells",
        "enable-background",
        "field-sizing",
        "fill",
        "fill-opacity",
        "fill-rule",
        "filter",
        "flex",
        "flex-basis",
        "flex-direction",
        "flex-flow",
        "flex-grow",
        "flex-shrink",
        "flex-wrap",
        "float",
        "flood-color",
        "flood-opacity",
        "flow",
        "font",
        "font-display",
        "font-family",
        "font-feature-settings",
        "font-kerning",
        "font-language-override",
        "font-optical-sizing",
        "font-palette",
        "font-size",
        "font-size-adjust",
        "font-smooth",
        "font-smoothing",
        "font-stretch",
        "font-style",
        "font-synthesis",
        "font-synthesis-position",
        "font-synthesis-small-caps",
        "font-synthesis-style",
        "font-synthesis-weight",
        "font-variant",
        "font-variant-alternates",
        "font-variant-caps",
        "font-variant-east-asian",
        "font-variant-emoji",
        "font-variant-ligatures",
        "font-variant-numeric",
        "font-variant-position",
        "font-variation-settings",
        "font-weight",
        "forced-color-adjust",
        "gap",
        "glyph-orientation-horizontal",
        "glyph-orientation-vertical",
        "grid",
        "grid-area",
        "grid-auto-columns",
        "grid-auto-flow",
        "grid-auto-rows",
        "grid-column",
        "grid-column-end",
        "grid-column-start",
        "grid-gap",
        "grid-row",
        "grid-row-end",
        "grid-row-start",
        "grid-template",
        "grid-template-areas",
        "grid-template-columns",
        "grid-template-rows",
        "hanging-punctuation",
        "height",
        "hyphenate-character",
        "hyphenate-limit-chars",
        "hyphens",
        "icon",
        "image-orientation",
        "image-rendering",
        "image-resolution",
        "ime-mode",
        "initial-letter",
        "initial-letter-align",
        "inline-size",
        "inset",
        "inset-area",
        "inset-block",
        "inset-block-end",
        "inset-block-start",
        "inset-inline",
        "inset-inline-end",
        "inset-inline-start",
        "isolation",
        "justify-content",
        "justify-items",
        "justify-self",
        "kerning",
        "left",
        "letter-spacing",
        "lighting-color",
        "line-break",
        "line-height",
        "line-height-step",
        "list-style",
        "list-style-image",
        "list-style-position",
        "list-style-type",
        "margin",
        "margin-block",
        "margin-block-end",
        "margin-block-start",
        "margin-bottom",
        "margin-inline",
        "margin-inline-end",
        "margin-inline-start",
        "margin-left",
        "margin-right",
        "margin-top",
        "margin-trim",
        "marker",
        "marker-end",
        "marker-mid",
        "marker-start",
        "marks",
        "mask",
        "mask-border",
        "mask-border-mode",
        "mask-border-outset",
        "mask-border-repeat",
        "mask-border-slice",
        "mask-border-source",
        "mask-border-width",
        "mask-clip",
        "mask-composite",
        "mask-image",
        "mask-mode",
        "mask-origin",
        "mask-position",
        "mask-repeat",
        "mask-size",
        "mask-type",
        "masonry-auto-flow",
        "math-depth",
        "math-shift",
        "math-style",
        "max-block-size",
        "max-height",
        "max-inline-size",
        "max-width",
        "min-block-size",
        "min-height",
        "min-inline-size",
        "min-width",
        "mix-blend-mode",
        "nav-down",
        "nav-index",
        "nav-left",
        "nav-right",
        "nav-up",
        "none",
        "normal",
        "object-fit",
        "object-position",
        "offset",
        "offset-anchor",
        "offset-distance",
        "offset-path",
        "offset-position",
        "offset-rotate",
        "opacity",
        "order",
        "orphans",
        "outline",
        "outline-color",
        "outline-offset",
        "outline-style",
        "outline-width",
        "overflow",
        "overflow-anchor",
        "overflow-block",
        "overflow-clip-margin",
        "overflow-inline",
        "overflow-wrap",
        "overflow-x",
        "overflow-y",
        "overlay",
        "overscroll-behavior",
        "overscroll-behavior-block",
        "overscroll-behavior-inline",
        "overscroll-behavior-x",
        "overscroll-behavior-y",
        "padding",
        "padding-block",
        "padding-block-end",
        "padding-block-start",
        "padding-bottom",
        "padding-inline",
        "padding-inline-end",
        "padding-inline-start",
        "padding-left",
        "padding-right",
        "padding-top",
        "page",
        "page-break-after",
        "page-break-before",
        "page-break-inside",
        "paint-order",
        "pause",
        "pause-after",
        "pause-before",
        "perspective",
        "perspective-origin",
        "place-content",
        "place-items",
        "place-self",
        "pointer-events",
        "position",
        "position-anchor",
        "position-visibility",
        "print-color-adjust",
        "quotes",
        "r",
        "resize",
        "rest",
        "rest-after",
        "rest-before",
        "right",
        "rotate",
        "row-gap",
        "ruby-align",
        "ruby-position",
        "scale",
        "scroll-behavior",
        "scroll-margin",
        "scroll-margin-block",
        "scroll-margin-block-end",
        "scroll-margin-block-start",
        "scroll-margin-bottom",
        "scroll-margin-inline",
        "scroll-margin-inline-end",
        "scroll-margin-inline-start",
        "scroll-margin-left",
        "scroll-margin-right",
        "scroll-margin-top",
        "scroll-padding",
        "scroll-padding-block",
        "scroll-padding-block-end",
        "scroll-padding-block-start",
        "scroll-padding-bottom",
        "scroll-padding-inline",
        "scroll-padding-inline-end",
        "scroll-padding-inline-start",
        "scroll-padding-left",
        "scroll-padding-right",
        "scroll-padding-top",
        "scroll-snap-align",
        "scroll-snap-stop",
        "scroll-snap-type",
        "scroll-timeline",
        "scroll-timeline-axis",
        "scroll-timeline-name",
        "scrollbar-color",
        "scrollbar-gutter",
        "scrollbar-width",
        "shape-image-threshold",
        "shape-margin",
        "shape-outside",
        "shape-rendering",
        "speak",
        "speak-as",
        "src",
        "stop-color",
        "stop-opacity",
        "stroke",
        "stroke-dasharray",
        "stroke-dashoffset",
        "stroke-linecap",
        "stroke-linejoin",
        "stroke-miterlimit",
        "stroke-opacity",
        "stroke-width",
        "tab-size",
        "table-layout",
        "text-align",
        "text-align-all",
        "text-align-last",
        "text-anchor",
        "text-combine-upright",
        "text-decoration",
        "text-decoration-color",
        "text-decoration-line",
        "text-decoration-skip",
        "text-decoration-skip-ink",
        "text-decoration-style",
        "text-decoration-thickness",
        "text-emphasis",
        "text-emphasis-color",
        "text-emphasis-position",
        "text-emphasis-style",
        "text-indent",
        "text-justify",
        "text-orientation",
        "text-overflow",
        "text-rendering",
        "text-shadow",
        "text-size-adjust",
        "text-transform",
        "text-underline-offset",
        "text-underline-position",
        "text-wrap",
        "text-wrap-mode",
        "text-wrap-style",
        "timeline-scope",
        "top",
        "touch-action",
        "transform",
        "transform-box",
        "transform-origin",
        "transform-style",
        "transition",
        "transition-behavior",
        "transition-delay",
        "transition-duration",
        "transition-property",
        "transition-timing-function",
        "translate",
        "unicode-bidi",
        "user-modify",
        "user-select",
        "vector-effect",
        "vertical-align",
        "view-timeline",
        "view-timeline-axis",
        "view-timeline-inset",
        "view-timeline-name",
        "view-transition-name",
        "visibility",
        "voice-balance",
        "voice-duration",
        "voice-family",
        "voice-pitch",
        "voice-range",
        "voice-rate",
        "voice-stress",
        "voice-volume",
        "white-space",
        "white-space-collapse",
        "widows",
        "width",
        "will-change",
        "word-break",
        "word-spacing",
        "word-wrap",
        "writing-mode",
        "x",
        "y",
        "z-index",
        "zoom",
      ]
        .sort()
        .reverse(),
      p = c.concat(b).sort().reverse();
    function s(a) {
      const l = r(a),
        g = p,
        f = "and or not only",
        E = "[\\w-]+",
        y = "(" + E + "|@\\{" + E + "\\})",
        N = [],
        T = [],
        O = function (ee) {
          return { className: "string", begin: "~?" + ee + ".*?" + ee };
        },
        R = function (ee, ne, W) {
          return { className: ee, begin: ne, relevance: W };
        },
        F = { $pattern: /[a-z-]+/, keyword: f, attribute: d.join(" ") },
        x = { begin: "\\(", end: "\\)", contains: T, keywords: F, relevance: 0 };
      T.push(
        a.C_LINE_COMMENT_MODE,
        a.C_BLOCK_COMMENT_MODE,
        O("'"),
        O('"'),
        l.CSS_NUMBER_MODE,
        { begin: "(url|data-uri)\\(", starts: { className: "string", end: "[\\)\\n]", excludeEnd: !0 } },
        l.HEXCOLOR,
        x,
        R("variable", "@@?" + E, 10),
        R("variable", "@\\{" + E + "\\}"),
        R("built_in", "~?`[^`]*?`"),
        { className: "attribute", begin: E + "\\s*:", end: ":", returnBegin: !0, excludeEnd: !0 },
        l.IMPORTANT,
        { beginKeywords: "and not" },
        l.FUNCTION_DISPATCH
      );
      const D = T.concat({ begin: /\{/, end: /\}/, contains: N }),
        B = { beginKeywords: "when", endsWithParent: !0, contains: [{ beginKeywords: "and not" }].concat(T) },
        M = {
          begin: y + "\\s*:",
          returnBegin: !0,
          end: /[;}]/,
          relevance: 0,
          contains: [
            { begin: /-(webkit|moz|ms|o)-/ },
            l.CSS_VARIABLE,
            {
              className: "attribute",
              begin: "\\b(" + _.join("|") + ")\\b",
              end: /(?=:)/,
              starts: { endsWithParent: !0, illegal: "[<=$]", relevance: 0, contains: T },
            },
          ],
        },
        U = {
          className: "keyword",
          begin:
            "@(import|media|charset|font-face|(-[a-z]+-)?keyframes|supports|document|namespace|page|viewport|host)\\b",
          starts: { end: "[;{}]", keywords: F, returnEnd: !0, contains: T, relevance: 0 },
        },
        K = {
          className: "variable",
          variants: [{ begin: "@" + E + "\\s*:", relevance: 15 }, { begin: "@" + E }],
          starts: { end: "[;}]", returnEnd: !0, contains: D },
        },
        G = {
          variants: [
            { begin: "[\\.#:&\\[>]", end: "[;{}]" },
            { begin: y, end: /\{/ },
          ],
          returnBegin: !0,
          returnEnd: !0,
          illegal: "[<='$\"]",
          relevance: 0,
          contains: [
            a.C_LINE_COMMENT_MODE,
            a.C_BLOCK_COMMENT_MODE,
            B,
            R("keyword", "all\\b"),
            R("variable", "@\\{" + E + "\\}"),
            { begin: "\\b(" + o.join("|") + ")\\b", className: "selector-tag" },
            l.CSS_NUMBER_MODE,
            R("selector-tag", y, 0),
            R("selector-id", "#" + y),
            R("selector-class", "\\." + y, 0),
            R("selector-tag", "&", 0),
            l.ATTRIBUTE_SELECTOR_MODE,
            { className: "selector-pseudo", begin: ":(" + c.join("|") + ")" },
            { className: "selector-pseudo", begin: ":(:)?(" + b.join("|") + ")" },
            { begin: /\(/, end: /\)/, relevance: 0, contains: D },
            { begin: "!important" },
            l.FUNCTION_DISPATCH,
          ],
        },
        re = { begin: E + ":(:)?" + "(".concat(g.join("|"), ")"), returnBegin: !0, contains: [G] };
      return (
        N.push(a.C_LINE_COMMENT_MODE, a.C_BLOCK_COMMENT_MODE, U, K, re, M, G, B, l.FUNCTION_DISPATCH),
        { name: "Less", case_insensitive: !0, illegal: "[=>'/<($\"]", contains: N }
      );
    }
    return (Nn = s), Nn;
  }
  var yn, ht;
  function gr() {
    if (ht) return yn;
    ht = 1;
    function r(e) {
      const t = "\\[=*\\[",
        o = "\\]=*\\]",
        d = { begin: t, end: o, contains: ["self"] },
        c = [e.COMMENT("--(?!" + t + ")", "$"), e.COMMENT("--" + t, o, { contains: [d], relevance: 10 })];
      return {
        name: "Lua",
        aliases: ["pluto"],
        keywords: {
          $pattern: e.UNDERSCORE_IDENT_RE,
          literal: "true false nil",
          keyword: "and break do else elseif end for goto if in local not or repeat return then until while",
          built_in:
            "_G _ENV _VERSION __index __newindex __mode __call __metatable __tostring __len __gc __add __sub __mul __div __mod __pow __concat __unm __eq __lt __le assert collectgarbage dofile error getfenv getmetatable ipairs load loadfile loadstring module next pairs pcall print rawequal rawget rawset require select setfenv setmetatable tonumber tostring type unpack xpcall arg self coroutine resume yield status wrap create running debug getupvalue debug sethook getmetatable gethook setmetatable setlocal traceback setfenv getinfo setupvalue getlocal getregistry getfenv io lines write close flush open output type read stderr stdin input stdout popen tmpfile math log max acos huge ldexp pi cos tanh pow deg tan cosh sinh random randomseed frexp ceil floor rad abs sqrt modf asin min mod fmod log10 atan2 exp sin atan os exit setlocale date getenv difftime remove time clock tmpname rename execute package preload loadlib loaded loaders cpath config path seeall string sub upper len gfind rep find match char dump gmatch reverse byte format gsub lower table setn insert getn foreachi maxn foreach concat sort remove",
        },
        contains: c.concat([
          {
            className: "function",
            beginKeywords: "function",
            end: "\\)",
            contains: [
              e.inherit(e.TITLE_MODE, { begin: "([_a-zA-Z]\\w*\\.)*([_a-zA-Z]\\w*:)?[_a-zA-Z]\\w*" }),
              { className: "params", begin: "\\(", endsWithParent: !0, contains: c },
            ].concat(c),
          },
          e.C_NUMBER_MODE,
          e.APOS_STRING_MODE,
          e.QUOTE_STRING_MODE,
          { className: "string", begin: t, end: o, contains: [d], relevance: 5 },
        ]),
      };
    }
    return (yn = r), yn;
  }
  var vn, Nt;
  function br() {
    if (Nt) return vn;
    Nt = 1;
    function r(e) {
      const t = {
          className: "variable",
          variants: [
            { begin: "\\$\\(" + e.UNDERSCORE_IDENT_RE + "\\)", contains: [e.BACKSLASH_ESCAPE] },
            { begin: /\$[@%<?\^\+\*]/ },
          ],
        },
        o = { className: "string", begin: /"/, end: /"/, contains: [e.BACKSLASH_ESCAPE, t] },
        d = {
          className: "variable",
          begin: /\$\([\w-]+\s/,
          end: /\)/,
          keywords: {
            built_in:
              "subst patsubst strip findstring filter filter-out sort word wordlist firstword lastword dir notdir suffix basename addsuffix addprefix join wildcard realpath abspath error warning shell origin flavor foreach if or and call eval file value",
          },
          contains: [t, o],
        },
        c = { begin: "^" + e.UNDERSCORE_IDENT_RE + "\\s*(?=[:+?]?=)" },
        b = { className: "meta", begin: /^\.PHONY:/, end: /$/, keywords: { $pattern: /[\.\w]+/, keyword: ".PHONY" } },
        _ = { className: "section", begin: /^[^\s]+:/, end: /$/, contains: [t] };
      return {
        name: "Makefile",
        aliases: ["mk", "mak", "make"],
        keywords: {
          $pattern: /[\w-]+/,
          keyword:
            "define endef undefine ifdef ifndef ifeq ifneq else endif include -include sinclude override export unexport private vpath",
        },
        contains: [e.HASH_COMMENT_MODE, t, o, d, c, b, _],
      };
    }
    return (vn = r), vn;
  }
  var Tn, yt;
  function pr() {
    if (yt) return Tn;
    yt = 1;
    function r(e) {
      const t = e.regex,
        o = [
          "abs",
          "accept",
          "alarm",
          "and",
          "atan2",
          "bind",
          "binmode",
          "bless",
          "break",
          "caller",
          "chdir",
          "chmod",
          "chomp",
          "chop",
          "chown",
          "chr",
          "chroot",
          "class",
          "close",
          "closedir",
          "connect",
          "continue",
          "cos",
          "crypt",
          "dbmclose",
          "dbmopen",
          "defined",
          "delete",
          "die",
          "do",
          "dump",
          "each",
          "else",
          "elsif",
          "endgrent",
          "endhostent",
          "endnetent",
          "endprotoent",
          "endpwent",
          "endservent",
          "eof",
          "eval",
          "exec",
          "exists",
          "exit",
          "exp",
          "fcntl",
          "field",
          "fileno",
          "flock",
          "for",
          "foreach",
          "fork",
          "format",
          "formline",
          "getc",
          "getgrent",
          "getgrgid",
          "getgrnam",
          "gethostbyaddr",
          "gethostbyname",
          "gethostent",
          "getlogin",
          "getnetbyaddr",
          "getnetbyname",
          "getnetent",
          "getpeername",
          "getpgrp",
          "getpriority",
          "getprotobyname",
          "getprotobynumber",
          "getprotoent",
          "getpwent",
          "getpwnam",
          "getpwuid",
          "getservbyname",
          "getservbyport",
          "getservent",
          "getsockname",
          "getsockopt",
          "given",
          "glob",
          "gmtime",
          "goto",
          "grep",
          "gt",
          "hex",
          "if",
          "index",
          "int",
          "ioctl",
          "join",
          "keys",
          "kill",
          "last",
          "lc",
          "lcfirst",
          "length",
          "link",
          "listen",
          "local",
          "localtime",
          "log",
          "lstat",
          "lt",
          "ma",
          "map",
          "method",
          "mkdir",
          "msgctl",
          "msgget",
          "msgrcv",
          "msgsnd",
          "my",
          "ne",
          "next",
          "no",
          "not",
          "oct",
          "open",
          "opendir",
          "or",
          "ord",
          "our",
          "pack",
          "package",
          "pipe",
          "pop",
          "pos",
          "print",
          "printf",
          "prototype",
          "push",
          "q|0",
          "qq",
          "quotemeta",
          "qw",
          "qx",
          "rand",
          "read",
          "readdir",
          "readline",
          "readlink",
          "readpipe",
          "recv",
          "redo",
          "ref",
          "rename",
          "require",
          "reset",
          "return",
          "reverse",
          "rewinddir",
          "rindex",
          "rmdir",
          "say",
          "scalar",
          "seek",
          "seekdir",
          "select",
          "semctl",
          "semget",
          "semop",
          "send",
          "setgrent",
          "sethostent",
          "setnetent",
          "setpgrp",
          "setpriority",
          "setprotoent",
          "setpwent",
          "setservent",
          "setsockopt",
          "shift",
          "shmctl",
          "shmget",
          "shmread",
          "shmwrite",
          "shutdown",
          "sin",
          "sleep",
          "socket",
          "socketpair",
          "sort",
          "splice",
          "split",
          "sprintf",
          "sqrt",
          "srand",
          "stat",
          "state",
          "study",
          "sub",
          "substr",
          "symlink",
          "syscall",
          "sysopen",
          "sysread",
          "sysseek",
          "system",
          "syswrite",
          "tell",
          "telldir",
          "tie",
          "tied",
          "time",
          "times",
          "tr",
          "truncate",
          "uc",
          "ucfirst",
          "umask",
          "undef",
          "unless",
          "unlink",
          "unpack",
          "unshift",
          "untie",
          "until",
          "use",
          "utime",
          "values",
          "vec",
          "wait",
          "waitpid",
          "wantarray",
          "warn",
          "when",
          "while",
          "write",
          "x|0",
          "xor",
          "y|0",
        ],
        d = /[dualxmsipngr]{0,12}/,
        c = { $pattern: /[\w.]+/, keyword: o.join(" ") },
        b = { className: "subst", begin: "[$@]\\{", end: "\\}", keywords: c },
        _ = { begin: /->\{/, end: /\}/ },
        p = { scope: "attr", match: /\s+:\s*\w+(\s*\(.*?\))?/ },
        s = {
          scope: "variable",
          variants: [
            { begin: /\$\d/ },
            { begin: t.concat(/[$%@](?!")(\^\w\b|#\w+(::\w+)*|\{\w+\}|\w+(::\w*)*)/, "(?![A-Za-z])(?![@$%])") },
            { begin: /[$%@](?!")[^\s\w{=]|\$=/, relevance: 0 },
          ],
          contains: [p],
        },
        a = {
          className: "number",
          variants: [
            { match: /0?\.[0-9][0-9_]+\b/ },
            { match: /\bv?(0|[1-9][0-9_]*(\.[0-9_]+)?|[1-9][0-9_]*)\b/ },
            { match: /\b0[0-7][0-7_]*\b/ },
            { match: /\b0x[0-9a-fA-F][0-9a-fA-F_]*\b/ },
            { match: /\b0b[0-1][0-1_]*\b/ },
          ],
          relevance: 0,
        },
        l = [e.BACKSLASH_ESCAPE, b, s],
        g = [/!/, /\//, /\|/, /\?/, /'/, /"/, /#/],
        f = (N, T, O = "\\1") => {
          const R = O === "\\1" ? O : t.concat(O, T);
          return t.concat(t.concat("(?:", N, ")"), T, /(?:\\.|[^\\\/])*?/, R, /(?:\\.|[^\\\/])*?/, O, d);
        },
        E = (N, T, O) => t.concat(t.concat("(?:", N, ")"), T, /(?:\\.|[^\\\/])*?/, O, d),
        y = [
          s,
          e.HASH_COMMENT_MODE,
          e.COMMENT(/^=\w/, /=cut/, { endsWithParent: !0 }),
          _,
          {
            className: "string",
            contains: l,
            variants: [
              { begin: "q[qwxr]?\\s*\\(", end: "\\)", relevance: 5 },
              { begin: "q[qwxr]?\\s*\\[", end: "\\]", relevance: 5 },
              { begin: "q[qwxr]?\\s*\\{", end: "\\}", relevance: 5 },
              { begin: "q[qwxr]?\\s*\\|", end: "\\|", relevance: 5 },
              { begin: "q[qwxr]?\\s*<", end: ">", relevance: 5 },
              { begin: "qw\\s+q", end: "q", relevance: 5 },
              { begin: "'", end: "'", contains: [e.BACKSLASH_ESCAPE] },
              { begin: '"', end: '"' },
              { begin: "`", end: "`", contains: [e.BACKSLASH_ESCAPE] },
              { begin: /\{\w+\}/, relevance: 0 },
              { begin: "-?\\w+\\s*=>", relevance: 0 },
            ],
          },
          a,
          {
            begin: "(\\/\\/|" + e.RE_STARTERS_RE + "|\\b(split|return|print|reverse|grep)\\b)\\s*",
            keywords: "split return print reverse grep",
            relevance: 0,
            contains: [
              e.HASH_COMMENT_MODE,
              {
                className: "regexp",
                variants: [
                  { begin: f("s|tr|y", t.either(...g, { capture: !0 })) },
                  { begin: f("s|tr|y", "\\(", "\\)") },
                  { begin: f("s|tr|y", "\\[", "\\]") },
                  { begin: f("s|tr|y", "\\{", "\\}") },
                ],
                relevance: 2,
              },
              {
                className: "regexp",
                variants: [
                  { begin: /(m|qr)\/\//, relevance: 0 },
                  { begin: E("(?:m|qr)?", /\//, /\//) },
                  { begin: E("m|qr", t.either(...g, { capture: !0 }), /\1/) },
                  { begin: E("m|qr", /\(/, /\)/) },
                  { begin: E("m|qr", /\[/, /\]/) },
                  { begin: E("m|qr", /\{/, /\}/) },
                ],
              },
            ],
          },
          {
            className: "function",
            beginKeywords: "sub method",
            end: "(\\s*\\(.*?\\))?[;{]",
            excludeEnd: !0,
            relevance: 5,
            contains: [e.TITLE_MODE, p],
          },
          {
            className: "class",
            beginKeywords: "class",
            end: "[;{]",
            excludeEnd: !0,
            relevance: 5,
            contains: [e.TITLE_MODE, p, a],
          },
          { begin: "-\\w\\b", relevance: 0 },
          {
            begin: "^__DATA__$",
            end: "^__END__$",
            subLanguage: "mojolicious",
            contains: [{ begin: "^@@.*", end: "$", className: "comment" }],
          },
        ];
      return (b.contains = y), (_.contains = y), { name: "Perl", aliases: ["pl", "pm"], keywords: c, contains: y };
    }
    return (Tn = r), Tn;
  }
  var Sn, vt;
  function mr() {
    if (vt) return Sn;
    vt = 1;
    function r(e) {
      const t = {
          className: "built_in",
          begin: "\\b(AV|CA|CF|CG|CI|CL|CM|CN|CT|MK|MP|MTK|MTL|NS|SCN|SK|UI|WK|XC)\\w+",
        },
        o = /[a-zA-Z@][a-zA-Z0-9_]*/,
        p = {
          "variable.language": ["this", "super"],
          $pattern: o,
          keyword: [
            "while",
            "export",
            "sizeof",
            "typedef",
            "const",
            "struct",
            "for",
            "union",
            "volatile",
            "static",
            "mutable",
            "if",
            "do",
            "return",
            "goto",
            "enum",
            "else",
            "break",
            "extern",
            "asm",
            "case",
            "default",
            "register",
            "explicit",
            "typename",
            "switch",
            "continue",
            "inline",
            "readonly",
            "assign",
            "readwrite",
            "self",
            "@synchronized",
            "id",
            "typeof",
            "nonatomic",
            "IBOutlet",
            "IBAction",
            "strong",
            "weak",
            "copy",
            "in",
            "out",
            "inout",
            "bycopy",
            "byref",
            "oneway",
            "__strong",
            "__weak",
            "__block",
            "__autoreleasing",
            "@private",
            "@protected",
            "@public",
            "@try",
            "@property",
            "@end",
            "@throw",
            "@catch",
            "@finally",
            "@autoreleasepool",
            "@synthesize",
            "@dynamic",
            "@selector",
            "@optional",
            "@required",
            "@encode",
            "@package",
            "@import",
            "@defs",
            "@compatibility_alias",
            "__bridge",
            "__bridge_transfer",
            "__bridge_retained",
            "__bridge_retain",
            "__covariant",
            "__contravariant",
            "__kindof",
            "_Nonnull",
            "_Nullable",
            "_Null_unspecified",
            "__FUNCTION__",
            "__PRETTY_FUNCTION__",
            "__attribute__",
            "getter",
            "setter",
            "retain",
            "unsafe_unretained",
            "nonnull",
            "nullable",
            "null_unspecified",
            "null_resettable",
            "class",
            "instancetype",
            "NS_DESIGNATED_INITIALIZER",
            "NS_UNAVAILABLE",
            "NS_REQUIRES_SUPER",
            "NS_RETURNS_INNER_POINTER",
            "NS_INLINE",
            "NS_AVAILABLE",
            "NS_DEPRECATED",
            "NS_ENUM",
            "NS_OPTIONS",
            "NS_SWIFT_UNAVAILABLE",
            "NS_ASSUME_NONNULL_BEGIN",
            "NS_ASSUME_NONNULL_END",
            "NS_REFINED_FOR_SWIFT",
            "NS_SWIFT_NAME",
            "NS_SWIFT_NOTHROW",
            "NS_DURING",
            "NS_HANDLER",
            "NS_ENDHANDLER",
            "NS_VALUERETURN",
            "NS_VOIDRETURN",
          ],
          literal: ["false", "true", "FALSE", "TRUE", "nil", "YES", "NO", "NULL"],
          built_in: ["dispatch_once_t", "dispatch_queue_t", "dispatch_sync", "dispatch_async", "dispatch_once"],
          type: [
            "int",
            "float",
            "char",
            "unsigned",
            "signed",
            "short",
            "long",
            "double",
            "wchar_t",
            "unichar",
            "void",
            "bool",
            "BOOL",
            "id|0",
            "_Bool",
          ],
        },
        s = { $pattern: o, keyword: ["@interface", "@class", "@protocol", "@implementation"] };
      return {
        name: "Objective-C",
        aliases: ["mm", "objc", "obj-c", "obj-c++", "objective-c++"],
        keywords: p,
        illegal: "</",
        contains: [
          t,
          e.C_LINE_COMMENT_MODE,
          e.C_BLOCK_COMMENT_MODE,
          e.C_NUMBER_MODE,
          e.QUOTE_STRING_MODE,
          e.APOS_STRING_MODE,
          {
            className: "string",
            variants: [{ begin: '@"', end: '"', illegal: "\\n", contains: [e.BACKSLASH_ESCAPE] }],
          },
          {
            className: "meta",
            begin: /#\s*[a-z]+\b/,
            end: /$/,
            keywords: { keyword: "if else elif endif define undef warning error line pragma ifdef ifndef include" },
            contains: [
              { begin: /\\\n/, relevance: 0 },
              e.inherit(e.QUOTE_STRING_MODE, { className: "string" }),
              { className: "string", begin: /<.*?>/, end: /$/, illegal: "\\n" },
              e.C_LINE_COMMENT_MODE,
              e.C_BLOCK_COMMENT_MODE,
            ],
          },
          {
            className: "class",
            begin: "(" + s.keyword.join("|") + ")\\b",
            end: /(\{|$)/,
            excludeEnd: !0,
            keywords: s,
            contains: [e.UNDERSCORE_TITLE_MODE],
          },
          { begin: "\\." + e.UNDERSCORE_IDENT_RE, relevance: 0 },
        ],
      };
    }
    return (Sn = r), Sn;
  }
  var wn, Tt;
  function _r() {
    if (Tt) return wn;
    Tt = 1;
    function r(e) {
      const t = e.regex,
        o = /(?![A-Za-z0-9])(?![$])/,
        d = t.concat(/[a-zA-Z_\x7f-\xff][a-zA-Z0-9_\x7f-\xff]*/, o),
        c = t.concat(/(\\?[A-Z][a-z0-9_\x7f-\xff]+|\\?[A-Z]+(?=[A-Z][a-z0-9_\x7f-\xff])){1,}/, o),
        b = t.concat(/[A-Z]+/, o),
        _ = { scope: "variable", match: "\\$+" + d },
        p = {
          scope: "meta",
          variants: [
            { begin: /<\?php/, relevance: 10 },
            { begin: /<\?=/ },
            { begin: /<\?/, relevance: 0.1 },
            { begin: /\?>/ },
          ],
        },
        s = { scope: "subst", variants: [{ begin: /\$\w+/ }, { begin: /\{\$/, end: /\}/ }] },
        a = e.inherit(e.APOS_STRING_MODE, { illegal: null }),
        l = e.inherit(e.QUOTE_STRING_MODE, { illegal: null, contains: e.QUOTE_STRING_MODE.contains.concat(s) }),
        g = {
          begin: /<<<[ \t]*(?:(\w+)|"(\w+)")\n/,
          end: /[ \t]*(\w+)\b/,
          contains: e.QUOTE_STRING_MODE.contains.concat(s),
          "on:begin": (W, Z) => {
            Z.data._beginMatch = W[1] || W[2];
          },
          "on:end": (W, Z) => {
            Z.data._beginMatch !== W[1] && Z.ignoreMatch();
          },
        },
        f = e.END_SAME_AS_BEGIN({ begin: /<<<[ \t]*'(\w+)'\n/, end: /[ \t]*(\w+)\b/ }),
        E = "[ 	\n]",
        y = { scope: "string", variants: [l, a, g, f] },
        N = {
          scope: "number",
          variants: [
            { begin: "\\b0[bB][01]+(?:_[01]+)*\\b" },
            { begin: "\\b0[oO][0-7]+(?:_[0-7]+)*\\b" },
            { begin: "\\b0[xX][\\da-fA-F]+(?:_[\\da-fA-F]+)*\\b" },
            { begin: "(?:\\b\\d+(?:_\\d+)*(\\.(?:\\d+(?:_\\d+)*))?|\\B\\.\\d+)(?:[eE][+-]?\\d+)?" },
          ],
          relevance: 0,
        },
        T = ["false", "null", "true"],
        O = [
          "__CLASS__",
          "__DIR__",
          "__FILE__",
          "__FUNCTION__",
          "__COMPILER_HALT_OFFSET__",
          "__LINE__",
          "__METHOD__",
          "__NAMESPACE__",
          "__TRAIT__",
          "die",
          "echo",
          "exit",
          "include",
          "include_once",
          "print",
          "require",
          "require_once",
          "array",
          "abstract",
          "and",
          "as",
          "binary",
          "bool",
          "boolean",
          "break",
          "callable",
          "case",
          "catch",
          "class",
          "clone",
          "const",
          "continue",
          "declare",
          "default",
          "do",
          "double",
          "else",
          "elseif",
          "empty",
          "enddeclare",
          "endfor",
          "endforeach",
          "endif",
          "endswitch",
          "endwhile",
          "enum",
          "eval",
          "extends",
          "final",
          "finally",
          "float",
          "for",
          "foreach",
          "from",
          "global",
          "goto",
          "if",
          "implements",
          "instanceof",
          "insteadof",
          "int",
          "integer",
          "interface",
          "isset",
          "iterable",
          "list",
          "match|0",
          "mixed",
          "new",
          "never",
          "object",
          "or",
          "private",
          "protected",
          "public",
          "readonly",
          "real",
          "return",
          "string",
          "switch",
          "throw",
          "trait",
          "try",
          "unset",
          "use",
          "var",
          "void",
          "while",
          "xor",
          "yield",
        ],
        R = [
          "Error|0",
          "AppendIterator",
          "ArgumentCountError",
          "ArithmeticError",
          "ArrayIterator",
          "ArrayObject",
          "AssertionError",
          "BadFunctionCallException",
          "BadMethodCallException",
          "CachingIterator",
          "CallbackFilterIterator",
          "CompileError",
          "Countable",
          "DirectoryIterator",
          "DivisionByZeroError",
          "DomainException",
          "EmptyIterator",
          "ErrorException",
          "Exception",
          "FilesystemIterator",
          "FilterIterator",
          "GlobIterator",
          "InfiniteIterator",
          "InvalidArgumentException",
          "IteratorIterator",
          "LengthException",
          "LimitIterator",
          "LogicException",
          "MultipleIterator",
          "NoRewindIterator",
          "OutOfBoundsException",
          "OutOfRangeException",
          "OuterIterator",
          "OverflowException",
          "ParentIterator",
          "ParseError",
          "RangeException",
          "RecursiveArrayIterator",
          "RecursiveCachingIterator",
          "RecursiveCallbackFilterIterator",
          "RecursiveDirectoryIterator",
          "RecursiveFilterIterator",
          "RecursiveIterator",
          "RecursiveIteratorIterator",
          "RecursiveRegexIterator",
          "RecursiveTreeIterator",
          "RegexIterator",
          "RuntimeException",
          "SeekableIterator",
          "SplDoublyLinkedList",
          "SplFileInfo",
          "SplFileObject",
          "SplFixedArray",
          "SplHeap",
          "SplMaxHeap",
          "SplMinHeap",
          "SplObjectStorage",
          "SplObserver",
          "SplPriorityQueue",
          "SplQueue",
          "SplStack",
          "SplSubject",
          "SplTempFileObject",
          "TypeError",
          "UnderflowException",
          "UnexpectedValueException",
          "UnhandledMatchError",
          "ArrayAccess",
          "BackedEnum",
          "Closure",
          "Fiber",
          "Generator",
          "Iterator",
          "IteratorAggregate",
          "Serializable",
          "Stringable",
          "Throwable",
          "Traversable",
          "UnitEnum",
          "WeakReference",
          "WeakMap",
          "Directory",
          "__PHP_Incomplete_Class",
          "parent",
          "php_user_filter",
          "self",
          "static",
          "stdClass",
        ],
        x = {
          keyword: O,
          literal: ((W) => {
            const Z = [];
            return (
              W.forEach((J) => {
                Z.push(J), J.toLowerCase() === J ? Z.push(J.toUpperCase()) : Z.push(J.toLowerCase());
              }),
              Z
            );
          })(T),
          built_in: R,
        },
        D = (W) => W.map((Z) => Z.replace(/\|\d+$/, "")),
        B = {
          variants: [
            {
              match: [/new/, t.concat(E, "+"), t.concat("(?!", D(R).join("\\b|"), "\\b)"), c],
              scope: { 1: "keyword", 4: "title.class" },
            },
          ],
        },
        M = t.concat(d, "\\b(?!\\()"),
        U = {
          variants: [
            { match: [t.concat(/::/, t.lookahead(/(?!class\b)/)), M], scope: { 2: "variable.constant" } },
            { match: [/::/, /class/], scope: { 2: "variable.language" } },
            {
              match: [c, t.concat(/::/, t.lookahead(/(?!class\b)/)), M],
              scope: { 1: "title.class", 3: "variable.constant" },
            },
            { match: [c, t.concat("::", t.lookahead(/(?!class\b)/))], scope: { 1: "title.class" } },
            { match: [c, /::/, /class/], scope: { 1: "title.class", 3: "variable.language" } },
          ],
        },
        K = { scope: "attr", match: t.concat(d, t.lookahead(":"), t.lookahead(/(?!::)/)) },
        G = { relevance: 0, begin: /\(/, end: /\)/, keywords: x, contains: [K, _, U, e.C_BLOCK_COMMENT_MODE, y, N, B] },
        re = {
          relevance: 0,
          match: [
            /\b/,
            t.concat("(?!fn\\b|function\\b|", D(O).join("\\b|"), "|", D(R).join("\\b|"), "\\b)"),
            d,
            t.concat(E, "*"),
            t.lookahead(/(?=\()/),
          ],
          scope: { 3: "title.function.invoke" },
          contains: [G],
        };
      G.contains.push(re);
      const ee = [K, U, e.C_BLOCK_COMMENT_MODE, y, N, B],
        ne = {
          begin: t.concat(/#\[\s*\\?/, t.either(c, b)),
          beginScope: "meta",
          end: /]/,
          endScope: "meta",
          keywords: { literal: T, keyword: ["new", "array"] },
          contains: [
            { begin: /\[/, end: /]/, keywords: { literal: T, keyword: ["new", "array"] }, contains: ["self", ...ee] },
            ...ee,
            { scope: "meta", variants: [{ match: c }, { match: b }] },
          ],
        };
      return {
        case_insensitive: !1,
        keywords: x,
        contains: [
          ne,
          e.HASH_COMMENT_MODE,
          e.COMMENT("//", "$"),
          e.COMMENT("/\\*", "\\*/", { contains: [{ scope: "doctag", match: "@[A-Za-z]+" }] }),
          {
            match: /__halt_compiler\(\);/,
            keywords: "__halt_compiler",
            starts: {
              scope: "comment",
              end: e.MATCH_NOTHING_RE,
              contains: [{ match: /\?>/, scope: "meta", endsParent: !0 }],
            },
          },
          p,
          { scope: "variable.language", match: /\$this\b/ },
          _,
          re,
          U,
          { match: [/const/, /\s/, d], scope: { 1: "keyword", 3: "variable.constant" } },
          B,
          {
            scope: "function",
            relevance: 0,
            beginKeywords: "fn function",
            end: /[;{]/,
            excludeEnd: !0,
            illegal: "[$%\\[]",
            contains: [
              { beginKeywords: "use" },
              e.UNDERSCORE_TITLE_MODE,
              { begin: "=>", endsParent: !0 },
              {
                scope: "params",
                begin: "\\(",
                end: "\\)",
                excludeBegin: !0,
                excludeEnd: !0,
                keywords: x,
                contains: ["self", ne, _, U, e.C_BLOCK_COMMENT_MODE, y, N],
              },
            ],
          },
          {
            scope: "class",
            variants: [
              { beginKeywords: "enum", illegal: /[($"]/ },
              { beginKeywords: "class interface trait", illegal: /[:($"]/ },
            ],
            relevance: 0,
            end: /\{/,
            excludeEnd: !0,
            contains: [{ beginKeywords: "extends implements" }, e.UNDERSCORE_TITLE_MODE],
          },
          {
            beginKeywords: "namespace",
            relevance: 0,
            end: ";",
            illegal: /[.']/,
            contains: [e.inherit(e.UNDERSCORE_TITLE_MODE, { scope: "title.class" })],
          },
          {
            beginKeywords: "use",
            relevance: 0,
            end: ";",
            contains: [{ match: /\b(as|const|function)\b/, scope: "keyword" }, e.UNDERSCORE_TITLE_MODE],
          },
          y,
          N,
        ],
      };
    }
    return (wn = r), wn;
  }
  var On, St;
  function fr() {
    if (St) return On;
    St = 1;
    function r(e) {
      return {
        name: "PHP template",
        subLanguage: "xml",
        contains: [
          {
            begin: /<\?(php|=)?/,
            end: /\?>/,
            subLanguage: "php",
            contains: [
              { begin: "/\\*", end: "\\*/", skip: !0 },
              { begin: 'b"', end: '"', skip: !0 },
              { begin: "b'", end: "'", skip: !0 },
              e.inherit(e.APOS_STRING_MODE, { illegal: null, className: null, contains: null, skip: !0 }),
              e.inherit(e.QUOTE_STRING_MODE, { illegal: null, className: null, contains: null, skip: !0 }),
            ],
          },
        ],
      };
    }
    return (On = r), On;
  }
  var An, wt;
  function Er() {
    if (wt) return An;
    wt = 1;
    function r(e) {
      return { name: "Plain text", aliases: ["text", "txt"], disableAutodetect: !0 };
    }
    return (An = r), An;
  }
  var Rn, Ot;
  function hr() {
    if (Ot) return Rn;
    Ot = 1;
    function r(e) {
      const t = e.regex,
        o = new RegExp("[\\p{XID_Start}_]\\p{XID_Continue}*", "u"),
        d = [
          "and",
          "as",
          "assert",
          "async",
          "await",
          "break",
          "case",
          "class",
          "continue",
          "def",
          "del",
          "elif",
          "else",
          "except",
          "finally",
          "for",
          "from",
          "global",
          "if",
          "import",
          "in",
          "is",
          "lambda",
          "match",
          "nonlocal|10",
          "not",
          "or",
          "pass",
          "raise",
          "return",
          "try",
          "while",
          "with",
          "yield",
        ],
        p = {
          $pattern: /[A-Za-z]\w+|__\w+__/,
          keyword: d,
          built_in: [
            "__import__",
            "abs",
            "all",
            "any",
            "ascii",
            "bin",
            "bool",
            "breakpoint",
            "bytearray",
            "bytes",
            "callable",
            "chr",
            "classmethod",
            "compile",
            "complex",
            "delattr",
            "dict",
            "dir",
            "divmod",
            "enumerate",
            "eval",
            "exec",
            "filter",
            "float",
            "format",
            "frozenset",
            "getattr",
            "globals",
            "hasattr",
            "hash",
            "help",
            "hex",
            "id",
            "input",
            "int",
            "isinstance",
            "issubclass",
            "iter",
            "len",
            "list",
            "locals",
            "map",
            "max",
            "memoryview",
            "min",
            "next",
            "object",
            "oct",
            "open",
            "ord",
            "pow",
            "print",
            "property",
            "range",
            "repr",
            "reversed",
            "round",
            "set",
            "setattr",
            "slice",
            "sorted",
            "staticmethod",
            "str",
            "sum",
            "super",
            "tuple",
            "type",
            "vars",
            "zip",
          ],
          literal: ["__debug__", "Ellipsis", "False", "None", "NotImplemented", "True"],
          type: [
            "Any",
            "Callable",
            "Coroutine",
            "Dict",
            "List",
            "Literal",
            "Generic",
            "Optional",
            "Sequence",
            "Set",
            "Tuple",
            "Type",
            "Union",
          ],
        },
        s = { className: "meta", begin: /^(>>>|\.\.\.) / },
        a = { className: "subst", begin: /\{/, end: /\}/, keywords: p, illegal: /#/ },
        l = { begin: /\{\{/, relevance: 0 },
        g = {
          className: "string",
          contains: [e.BACKSLASH_ESCAPE],
          variants: [
            {
              begin: /([uU]|[bB]|[rR]|[bB][rR]|[rR][bB])?'''/,
              end: /'''/,
              contains: [e.BACKSLASH_ESCAPE, s],
              relevance: 10,
            },
            {
              begin: /([uU]|[bB]|[rR]|[bB][rR]|[rR][bB])?"""/,
              end: /"""/,
              contains: [e.BACKSLASH_ESCAPE, s],
              relevance: 10,
            },
            { begin: /([fF][rR]|[rR][fF]|[fF])'''/, end: /'''/, contains: [e.BACKSLASH_ESCAPE, s, l, a] },
            { begin: /([fF][rR]|[rR][fF]|[fF])"""/, end: /"""/, contains: [e.BACKSLASH_ESCAPE, s, l, a] },
            { begin: /([uU]|[rR])'/, end: /'/, relevance: 10 },
            { begin: /([uU]|[rR])"/, end: /"/, relevance: 10 },
            { begin: /([bB]|[bB][rR]|[rR][bB])'/, end: /'/ },
            { begin: /([bB]|[bB][rR]|[rR][bB])"/, end: /"/ },
            { begin: /([fF][rR]|[rR][fF]|[fF])'/, end: /'/, contains: [e.BACKSLASH_ESCAPE, l, a] },
            { begin: /([fF][rR]|[rR][fF]|[fF])"/, end: /"/, contains: [e.BACKSLASH_ESCAPE, l, a] },
            e.APOS_STRING_MODE,
            e.QUOTE_STRING_MODE,
          ],
        },
        f = "[0-9](_?[0-9])*",
        E = "(\\b(".concat(f, "))?\\.(").concat(f, ")|\\b(").concat(f, ")\\."),
        y = "\\b|".concat(d.join("|")),
        N = {
          className: "number",
          relevance: 0,
          variants: [
            { begin: "(\\b(".concat(f, ")|(").concat(E, "))[eE][+-]?(").concat(f, ")[jJ]?(?=").concat(y, ")") },
            { begin: "(".concat(E, ")[jJ]?") },
            { begin: "\\b([1-9](_?[0-9])*|0+(_?0)*)[lLjJ]?(?=".concat(y, ")") },
            { begin: "\\b0[bB](_?[01])+[lL]?(?=".concat(y, ")") },
            { begin: "\\b0[oO](_?[0-7])+[lL]?(?=".concat(y, ")") },
            { begin: "\\b0[xX](_?[0-9a-fA-F])+[lL]?(?=".concat(y, ")") },
            { begin: "\\b(".concat(f, ")[jJ](?=").concat(y, ")") },
          ],
        },
        T = {
          className: "comment",
          begin: t.lookahead(/# type:/),
          end: /$/,
          keywords: p,
          contains: [{ begin: /# type:/ }, { begin: /#/, end: /\b\B/, endsWithParent: !0 }],
        },
        O = {
          className: "params",
          variants: [
            { className: "", begin: /\(\s*\)/, skip: !0 },
            {
              begin: /\(/,
              end: /\)/,
              excludeBegin: !0,
              excludeEnd: !0,
              keywords: p,
              contains: ["self", s, N, g, e.HASH_COMMENT_MODE],
            },
          ],
        };
      return (
        (a.contains = [g, N, s]),
        {
          name: "Python",
          aliases: ["py", "gyp", "ipython"],
          unicodeRegex: !0,
          keywords: p,
          illegal: /(<\/|\?)|=>/,
          contains: [
            s,
            N,
            { scope: "variable.language", match: /\bself\b/ },
            { beginKeywords: "if", relevance: 0 },
            { match: /\bor\b/, scope: "keyword" },
            g,
            T,
            e.HASH_COMMENT_MODE,
            { match: [/\bdef/, /\s+/, o], scope: { 1: "keyword", 3: "title.function" }, contains: [O] },
            {
              variants: [
                { match: [/\bclass/, /\s+/, o, /\s*/, /\(\s*/, o, /\s*\)/] },
                { match: [/\bclass/, /\s+/, o] },
              ],
              scope: { 1: "keyword", 3: "title.class", 6: "title.class.inherited" },
            },
            { className: "meta", begin: /^[\t ]*@/, end: /(?=#)|$/, contains: [N, O, g] },
          ],
        }
      );
    }
    return (Rn = r), Rn;
  }
  var Mn, At;
  function Nr() {
    if (At) return Mn;
    At = 1;
    function r(e) {
      return {
        aliases: ["pycon"],
        contains: [
          {
            className: "meta.prompt",
            starts: { end: / |$/, starts: { end: "$", subLanguage: "python" } },
            variants: [{ begin: /^>>>(?=[ ]|$)/ }, { begin: /^\.\.\.(?=[ ]|$)/ }],
          },
        ],
      };
    }
    return (Mn = r), Mn;
  }
  var kn, Rt;
  function yr() {
    if (Rt) return kn;
    Rt = 1;
    function r(e) {
      const t = e.regex,
        o = /(?:(?:[a-zA-Z]|\.[._a-zA-Z])[._a-zA-Z0-9]*)|\.(?!\d)/,
        d = t.either(
          /0[xX][0-9a-fA-F]+\.[0-9a-fA-F]*[pP][+-]?\d+i?/,
          /0[xX][0-9a-fA-F]+(?:[pP][+-]?\d+)?[Li]?/,
          /(?:\d+(?:\.\d*)?|\.\d+)(?:[eE][+-]?\d+)?[Li]?/
        ),
        c = /[=!<>:]=|\|\||&&|:::?|<-|<<-|->>|->|\|>|[-+*\/?!$&|:<=>@^~]|\*\*/,
        b = t.either(/[()]/, /[{}]/, /\[\[/, /[[\]]/, /\\/, /,/);
      return {
        name: "R",
        keywords: {
          $pattern: o,
          keyword: "function if in break next repeat else for while",
          literal: "NULL NA TRUE FALSE Inf NaN NA_integer_|10 NA_real_|10 NA_character_|10 NA_complex_|10",
          built_in:
            "LETTERS letters month.abb month.name pi T F abs acos acosh all any anyNA Arg as.call as.character as.complex as.double as.environment as.integer as.logical as.null.default as.numeric as.raw asin asinh atan atanh attr attributes baseenv browser c call ceiling class Conj cos cosh cospi cummax cummin cumprod cumsum digamma dim dimnames emptyenv exp expression floor forceAndCall gamma gc.time globalenv Im interactive invisible is.array is.atomic is.call is.character is.complex is.double is.environment is.expression is.finite is.function is.infinite is.integer is.language is.list is.logical is.matrix is.na is.name is.nan is.null is.numeric is.object is.pairlist is.raw is.recursive is.single is.symbol lazyLoadDBfetch length lgamma list log max min missing Mod names nargs nzchar oldClass on.exit pos.to.env proc.time prod quote range Re rep retracemem return round seq_along seq_len seq.int sign signif sin sinh sinpi sqrt standardGeneric substitute sum switch tan tanh tanpi tracemem trigamma trunc unclass untracemem UseMethod xtfrm",
        },
        contains: [
          e.COMMENT(/#'/, /$/, {
            contains: [
              {
                scope: "doctag",
                match: /@examples/,
                starts: { end: t.lookahead(t.either(/\n^#'\s*(?=@[a-zA-Z]+)/, /\n^(?!#')/)), endsParent: !0 },
              },
              {
                scope: "doctag",
                begin: "@param",
                end: /$/,
                contains: [
                  { scope: "variable", variants: [{ match: o }, { match: /`(?:\\.|[^`\\])+`/ }], endsParent: !0 },
                ],
              },
              { scope: "doctag", match: /@[a-zA-Z]+/ },
              { scope: "keyword", match: /\\[a-zA-Z]+/ },
            ],
          }),
          e.HASH_COMMENT_MODE,
          {
            scope: "string",
            contains: [e.BACKSLASH_ESCAPE],
            variants: [
              e.END_SAME_AS_BEGIN({ begin: /[rR]"(-*)\(/, end: /\)(-*)"/ }),
              e.END_SAME_AS_BEGIN({ begin: /[rR]"(-*)\{/, end: /\}(-*)"/ }),
              e.END_SAME_AS_BEGIN({ begin: /[rR]"(-*)\[/, end: /\](-*)"/ }),
              e.END_SAME_AS_BEGIN({ begin: /[rR]'(-*)\(/, end: /\)(-*)'/ }),
              e.END_SAME_AS_BEGIN({ begin: /[rR]'(-*)\{/, end: /\}(-*)'/ }),
              e.END_SAME_AS_BEGIN({ begin: /[rR]'(-*)\[/, end: /\](-*)'/ }),
              { begin: '"', end: '"', relevance: 0 },
              { begin: "'", end: "'", relevance: 0 },
            ],
          },
          {
            relevance: 0,
            variants: [
              { scope: { 1: "operator", 2: "number" }, match: [c, d] },
              { scope: { 1: "operator", 2: "number" }, match: [/%[^%]*%/, d] },
              { scope: { 1: "punctuation", 2: "number" }, match: [b, d] },
              { scope: { 2: "number" }, match: [/[^a-zA-Z0-9._]|^/, d] },
            ],
          },
          { scope: { 3: "operator" }, match: [o, /\s+/, /<-/, /\s+/] },
          { scope: "operator", relevance: 0, variants: [{ match: c }, { match: /%[^%]*%/ }] },
          { scope: "punctuation", relevance: 0, match: b },
          { begin: "`", end: "`", contains: [{ begin: /\\./ }] },
        ],
      };
    }
    return (kn = r), kn;
  }
  var Cn, Mt;
  function vr() {
    if (Mt) return Cn;
    Mt = 1;
    function r(e) {
      const t = e.regex,
        o = /(r#)?/,
        d = t.concat(o, e.UNDERSCORE_IDENT_RE),
        c = t.concat(o, e.IDENT_RE),
        b = {
          className: "title.function.invoke",
          relevance: 0,
          begin: t.concat(/\b/, /(?!let|for|while|if|else|match\b)/, c, t.lookahead(/\s*\(/)),
        },
        _ = "([ui](8|16|32|64|128|size)|f(32|64))?",
        p = [
          "abstract",
          "as",
          "async",
          "await",
          "become",
          "box",
          "break",
          "const",
          "continue",
          "crate",
          "do",
          "dyn",
          "else",
          "enum",
          "extern",
          "false",
          "final",
          "fn",
          "for",
          "if",
          "impl",
          "in",
          "let",
          "loop",
          "macro",
          "match",
          "mod",
          "move",
          "mut",
          "override",
          "priv",
          "pub",
          "ref",
          "return",
          "self",
          "Self",
          "static",
          "struct",
          "super",
          "trait",
          "true",
          "try",
          "type",
          "typeof",
          "union",
          "unsafe",
          "unsized",
          "use",
          "virtual",
          "where",
          "while",
          "yield",
        ],
        s = ["true", "false", "Some", "None", "Ok", "Err"],
        a = [
          "drop ",
          "Copy",
          "Send",
          "Sized",
          "Sync",
          "Drop",
          "Fn",
          "FnMut",
          "FnOnce",
          "ToOwned",
          "Clone",
          "Debug",
          "PartialEq",
          "PartialOrd",
          "Eq",
          "Ord",
          "AsRef",
          "AsMut",
          "Into",
          "From",
          "Default",
          "Iterator",
          "Extend",
          "IntoIterator",
          "DoubleEndedIterator",
          "ExactSizeIterator",
          "SliceConcatExt",
          "ToString",
          "assert!",
          "assert_eq!",
          "bitflags!",
          "bytes!",
          "cfg!",
          "col!",
          "concat!",
          "concat_idents!",
          "debug_assert!",
          "debug_assert_eq!",
          "env!",
          "eprintln!",
          "panic!",
          "file!",
          "format!",
          "format_args!",
          "include_bytes!",
          "include_str!",
          "line!",
          "local_data_key!",
          "module_path!",
          "option_env!",
          "print!",
          "println!",
          "select!",
          "stringify!",
          "try!",
          "unimplemented!",
          "unreachable!",
          "vec!",
          "write!",
          "writeln!",
          "macro_rules!",
          "assert_ne!",
          "debug_assert_ne!",
        ],
        l = [
          "i8",
          "i16",
          "i32",
          "i64",
          "i128",
          "isize",
          "u8",
          "u16",
          "u32",
          "u64",
          "u128",
          "usize",
          "f32",
          "f64",
          "str",
          "char",
          "bool",
          "Box",
          "Option",
          "Result",
          "String",
          "Vec",
        ];
      return {
        name: "Rust",
        aliases: ["rs"],
        keywords: { $pattern: e.IDENT_RE + "!?", type: l, keyword: p, literal: s, built_in: a },
        illegal: "</",
        contains: [
          e.C_LINE_COMMENT_MODE,
          e.COMMENT("/\\*", "\\*/", { contains: ["self"] }),
          e.inherit(e.QUOTE_STRING_MODE, { begin: /b?"/, illegal: null }),
          { className: "symbol", begin: /'[a-zA-Z_][a-zA-Z0-9_]*(?!')/ },
          {
            scope: "string",
            variants: [
              { begin: /b?r(#*)"(.|\n)*?"\1(?!#)/ },
              { begin: /b?'/, end: /'/, contains: [{ scope: "char.escape", match: /\\('|\w|x\w{2}|u\w{4}|U\w{8})/ }] },
            ],
          },
          {
            className: "number",
            variants: [
              { begin: "\\b0b([01_]+)" + _ },
              { begin: "\\b0o([0-7_]+)" + _ },
              { begin: "\\b0x([A-Fa-f0-9_]+)" + _ },
              { begin: "\\b(\\d[\\d_]*(\\.[0-9_]+)?([eE][+-]?[0-9_]+)?)" + _ },
            ],
            relevance: 0,
          },
          { begin: [/fn/, /\s+/, d], className: { 1: "keyword", 3: "title.function" } },
          {
            className: "meta",
            begin: "#!?\\[",
            end: "\\]",
            contains: [{ className: "string", begin: /"/, end: /"/, contains: [e.BACKSLASH_ESCAPE] }],
          },
          { begin: [/let/, /\s+/, /(?:mut\s+)?/, d], className: { 1: "keyword", 3: "keyword", 4: "variable" } },
          { begin: [/for/, /\s+/, d, /\s+/, /in/], className: { 1: "keyword", 3: "variable", 5: "keyword" } },
          { begin: [/type/, /\s+/, d], className: { 1: "keyword", 3: "title.class" } },
          { begin: [/(?:trait|enum|struct|union|impl|for)/, /\s+/, d], className: { 1: "keyword", 3: "title.class" } },
          { begin: e.IDENT_RE + "::", keywords: { keyword: "Self", built_in: a, type: l } },
          { className: "punctuation", begin: "->" },
          b,
        ],
      };
    }
    return (Cn = r), Cn;
  }
  var In, kt;
  function Tr() {
    if (kt) return In;
    kt = 1;
    const r = (s) => ({
        IMPORTANT: { scope: "meta", begin: "!important" },
        BLOCK_COMMENT: s.C_BLOCK_COMMENT_MODE,
        HEXCOLOR: { scope: "number", begin: /#(([0-9a-fA-F]{3,4})|(([0-9a-fA-F]{2}){3,4}))\b/ },
        FUNCTION_DISPATCH: { className: "built_in", begin: /[\w-]+(?=\()/ },
        ATTRIBUTE_SELECTOR_MODE: {
          scope: "selector-attr",
          begin: /\[/,
          end: /\]/,
          illegal: "$",
          contains: [s.APOS_STRING_MODE, s.QUOTE_STRING_MODE],
        },
        CSS_NUMBER_MODE: {
          scope: "number",
          begin:
            s.NUMBER_RE +
            "(%|em|ex|ch|rem|vw|vh|vmin|vmax|cm|mm|in|pt|pc|px|deg|grad|rad|turn|s|ms|Hz|kHz|dpi|dpcm|dppx)?",
          relevance: 0,
        },
        CSS_VARIABLE: { className: "attr", begin: /--[A-Za-z_][A-Za-z0-9_-]*/ },
      }),
      e = [
        "a",
        "abbr",
        "address",
        "article",
        "aside",
        "audio",
        "b",
        "blockquote",
        "body",
        "button",
        "canvas",
        "caption",
        "cite",
        "code",
        "dd",
        "del",
        "details",
        "dfn",
        "div",
        "dl",
        "dt",
        "em",
        "fieldset",
        "figcaption",
        "figure",
        "footer",
        "form",
        "h1",
        "h2",
        "h3",
        "h4",
        "h5",
        "h6",
        "header",
        "hgroup",
        "html",
        "i",
        "iframe",
        "img",
        "input",
        "ins",
        "kbd",
        "label",
        "legend",
        "li",
        "main",
        "mark",
        "menu",
        "nav",
        "object",
        "ol",
        "optgroup",
        "option",
        "p",
        "picture",
        "q",
        "quote",
        "samp",
        "section",
        "select",
        "source",
        "span",
        "strong",
        "summary",
        "sup",
        "table",
        "tbody",
        "td",
        "textarea",
        "tfoot",
        "th",
        "thead",
        "time",
        "tr",
        "ul",
        "var",
        "video",
      ],
      t = [
        "defs",
        "g",
        "marker",
        "mask",
        "pattern",
        "svg",
        "switch",
        "symbol",
        "feBlend",
        "feColorMatrix",
        "feComponentTransfer",
        "feComposite",
        "feConvolveMatrix",
        "feDiffuseLighting",
        "feDisplacementMap",
        "feFlood",
        "feGaussianBlur",
        "feImage",
        "feMerge",
        "feMorphology",
        "feOffset",
        "feSpecularLighting",
        "feTile",
        "feTurbulence",
        "linearGradient",
        "radialGradient",
        "stop",
        "circle",
        "ellipse",
        "image",
        "line",
        "path",
        "polygon",
        "polyline",
        "rect",
        "text",
        "use",
        "textPath",
        "tspan",
        "foreignObject",
        "clipPath",
      ],
      o = [...e, ...t],
      d = [
        "any-hover",
        "any-pointer",
        "aspect-ratio",
        "color",
        "color-gamut",
        "color-index",
        "device-aspect-ratio",
        "device-height",
        "device-width",
        "display-mode",
        "forced-colors",
        "grid",
        "height",
        "hover",
        "inverted-colors",
        "monochrome",
        "orientation",
        "overflow-block",
        "overflow-inline",
        "pointer",
        "prefers-color-scheme",
        "prefers-contrast",
        "prefers-reduced-motion",
        "prefers-reduced-transparency",
        "resolution",
        "scan",
        "scripting",
        "update",
        "width",
        "min-width",
        "max-width",
        "min-height",
        "max-height",
      ]
        .sort()
        .reverse(),
      c = [
        "active",
        "any-link",
        "blank",
        "checked",
        "current",
        "default",
        "defined",
        "dir",
        "disabled",
        "drop",
        "empty",
        "enabled",
        "first",
        "first-child",
        "first-of-type",
        "fullscreen",
        "future",
        "focus",
        "focus-visible",
        "focus-within",
        "has",
        "host",
        "host-context",
        "hover",
        "indeterminate",
        "in-range",
        "invalid",
        "is",
        "lang",
        "last-child",
        "last-of-type",
        "left",
        "link",
        "local-link",
        "not",
        "nth-child",
        "nth-col",
        "nth-last-child",
        "nth-last-col",
        "nth-last-of-type",
        "nth-of-type",
        "only-child",
        "only-of-type",
        "optional",
        "out-of-range",
        "past",
        "placeholder-shown",
        "read-only",
        "read-write",
        "required",
        "right",
        "root",
        "scope",
        "target",
        "target-within",
        "user-invalid",
        "valid",
        "visited",
        "where",
      ]
        .sort()
        .reverse(),
      b = [
        "after",
        "backdrop",
        "before",
        "cue",
        "cue-region",
        "first-letter",
        "first-line",
        "grammar-error",
        "marker",
        "part",
        "placeholder",
        "selection",
        "slotted",
        "spelling-error",
      ]
        .sort()
        .reverse(),
      _ = [
        "accent-color",
        "align-content",
        "align-items",
        "align-self",
        "alignment-baseline",
        "all",
        "anchor-name",
        "animation",
        "animation-composition",
        "animation-delay",
        "animation-direction",
        "animation-duration",
        "animation-fill-mode",
        "animation-iteration-count",
        "animation-name",
        "animation-play-state",
        "animation-range",
        "animation-range-end",
        "animation-range-start",
        "animation-timeline",
        "animation-timing-function",
        "appearance",
        "aspect-ratio",
        "backdrop-filter",
        "backface-visibility",
        "background",
        "background-attachment",
        "background-blend-mode",
        "background-clip",
        "background-color",
        "background-image",
        "background-origin",
        "background-position",
        "background-position-x",
        "background-position-y",
        "background-repeat",
        "background-size",
        "baseline-shift",
        "block-size",
        "border",
        "border-block",
        "border-block-color",
        "border-block-end",
        "border-block-end-color",
        "border-block-end-style",
        "border-block-end-width",
        "border-block-start",
        "border-block-start-color",
        "border-block-start-style",
        "border-block-start-width",
        "border-block-style",
        "border-block-width",
        "border-bottom",
        "border-bottom-color",
        "border-bottom-left-radius",
        "border-bottom-right-radius",
        "border-bottom-style",
        "border-bottom-width",
        "border-collapse",
        "border-color",
        "border-end-end-radius",
        "border-end-start-radius",
        "border-image",
        "border-image-outset",
        "border-image-repeat",
        "border-image-slice",
        "border-image-source",
        "border-image-width",
        "border-inline",
        "border-inline-color",
        "border-inline-end",
        "border-inline-end-color",
        "border-inline-end-style",
        "border-inline-end-width",
        "border-inline-start",
        "border-inline-start-color",
        "border-inline-start-style",
        "border-inline-start-width",
        "border-inline-style",
        "border-inline-width",
        "border-left",
        "border-left-color",
        "border-left-style",
        "border-left-width",
        "border-radius",
        "border-right",
        "border-right-color",
        "border-right-style",
        "border-right-width",
        "border-spacing",
        "border-start-end-radius",
        "border-start-start-radius",
        "border-style",
        "border-top",
        "border-top-color",
        "border-top-left-radius",
        "border-top-right-radius",
        "border-top-style",
        "border-top-width",
        "border-width",
        "bottom",
        "box-align",
        "box-decoration-break",
        "box-direction",
        "box-flex",
        "box-flex-group",
        "box-lines",
        "box-ordinal-group",
        "box-orient",
        "box-pack",
        "box-shadow",
        "box-sizing",
        "break-after",
        "break-before",
        "break-inside",
        "caption-side",
        "caret-color",
        "clear",
        "clip",
        "clip-path",
        "clip-rule",
        "color",
        "color-interpolation",
        "color-interpolation-filters",
        "color-profile",
        "color-rendering",
        "color-scheme",
        "column-count",
        "column-fill",
        "column-gap",
        "column-rule",
        "column-rule-color",
        "column-rule-style",
        "column-rule-width",
        "column-span",
        "column-width",
        "columns",
        "contain",
        "contain-intrinsic-block-size",
        "contain-intrinsic-height",
        "contain-intrinsic-inline-size",
        "contain-intrinsic-size",
        "contain-intrinsic-width",
        "container",
        "container-name",
        "container-type",
        "content",
        "content-visibility",
        "counter-increment",
        "counter-reset",
        "counter-set",
        "cue",
        "cue-after",
        "cue-before",
        "cursor",
        "cx",
        "cy",
        "direction",
        "display",
        "dominant-baseline",
        "empty-cells",
        "enable-background",
        "field-sizing",
        "fill",
        "fill-opacity",
        "fill-rule",
        "filter",
        "flex",
        "flex-basis",
        "flex-direction",
        "flex-flow",
        "flex-grow",
        "flex-shrink",
        "flex-wrap",
        "float",
        "flood-color",
        "flood-opacity",
        "flow",
        "font",
        "font-display",
        "font-family",
        "font-feature-settings",
        "font-kerning",
        "font-language-override",
        "font-optical-sizing",
        "font-palette",
        "font-size",
        "font-size-adjust",
        "font-smooth",
        "font-smoothing",
        "font-stretch",
        "font-style",
        "font-synthesis",
        "font-synthesis-position",
        "font-synthesis-small-caps",
        "font-synthesis-style",
        "font-synthesis-weight",
        "font-variant",
        "font-variant-alternates",
        "font-variant-caps",
        "font-variant-east-asian",
        "font-variant-emoji",
        "font-variant-ligatures",
        "font-variant-numeric",
        "font-variant-position",
        "font-variation-settings",
        "font-weight",
        "forced-color-adjust",
        "gap",
        "glyph-orientation-horizontal",
        "glyph-orientation-vertical",
        "grid",
        "grid-area",
        "grid-auto-columns",
        "grid-auto-flow",
        "grid-auto-rows",
        "grid-column",
        "grid-column-end",
        "grid-column-start",
        "grid-gap",
        "grid-row",
        "grid-row-end",
        "grid-row-start",
        "grid-template",
        "grid-template-areas",
        "grid-template-columns",
        "grid-template-rows",
        "hanging-punctuation",
        "height",
        "hyphenate-character",
        "hyphenate-limit-chars",
        "hyphens",
        "icon",
        "image-orientation",
        "image-rendering",
        "image-resolution",
        "ime-mode",
        "initial-letter",
        "initial-letter-align",
        "inline-size",
        "inset",
        "inset-area",
        "inset-block",
        "inset-block-end",
        "inset-block-start",
        "inset-inline",
        "inset-inline-end",
        "inset-inline-start",
        "isolation",
        "justify-content",
        "justify-items",
        "justify-self",
        "kerning",
        "left",
        "letter-spacing",
        "lighting-color",
        "line-break",
        "line-height",
        "line-height-step",
        "list-style",
        "list-style-image",
        "list-style-position",
        "list-style-type",
        "margin",
        "margin-block",
        "margin-block-end",
        "margin-block-start",
        "margin-bottom",
        "margin-inline",
        "margin-inline-end",
        "margin-inline-start",
        "margin-left",
        "margin-right",
        "margin-top",
        "margin-trim",
        "marker",
        "marker-end",
        "marker-mid",
        "marker-start",
        "marks",
        "mask",
        "mask-border",
        "mask-border-mode",
        "mask-border-outset",
        "mask-border-repeat",
        "mask-border-slice",
        "mask-border-source",
        "mask-border-width",
        "mask-clip",
        "mask-composite",
        "mask-image",
        "mask-mode",
        "mask-origin",
        "mask-position",
        "mask-repeat",
        "mask-size",
        "mask-type",
        "masonry-auto-flow",
        "math-depth",
        "math-shift",
        "math-style",
        "max-block-size",
        "max-height",
        "max-inline-size",
        "max-width",
        "min-block-size",
        "min-height",
        "min-inline-size",
        "min-width",
        "mix-blend-mode",
        "nav-down",
        "nav-index",
        "nav-left",
        "nav-right",
        "nav-up",
        "none",
        "normal",
        "object-fit",
        "object-position",
        "offset",
        "offset-anchor",
        "offset-distance",
        "offset-path",
        "offset-position",
        "offset-rotate",
        "opacity",
        "order",
        "orphans",
        "outline",
        "outline-color",
        "outline-offset",
        "outline-style",
        "outline-width",
        "overflow",
        "overflow-anchor",
        "overflow-block",
        "overflow-clip-margin",
        "overflow-inline",
        "overflow-wrap",
        "overflow-x",
        "overflow-y",
        "overlay",
        "overscroll-behavior",
        "overscroll-behavior-block",
        "overscroll-behavior-inline",
        "overscroll-behavior-x",
        "overscroll-behavior-y",
        "padding",
        "padding-block",
        "padding-block-end",
        "padding-block-start",
        "padding-bottom",
        "padding-inline",
        "padding-inline-end",
        "padding-inline-start",
        "padding-left",
        "padding-right",
        "padding-top",
        "page",
        "page-break-after",
        "page-break-before",
        "page-break-inside",
        "paint-order",
        "pause",
        "pause-after",
        "pause-before",
        "perspective",
        "perspective-origin",
        "place-content",
        "place-items",
        "place-self",
        "pointer-events",
        "position",
        "position-anchor",
        "position-visibility",
        "print-color-adjust",
        "quotes",
        "r",
        "resize",
        "rest",
        "rest-after",
        "rest-before",
        "right",
        "rotate",
        "row-gap",
        "ruby-align",
        "ruby-position",
        "scale",
        "scroll-behavior",
        "scroll-margin",
        "scroll-margin-block",
        "scroll-margin-block-end",
        "scroll-margin-block-start",
        "scroll-margin-bottom",
        "scroll-margin-inline",
        "scroll-margin-inline-end",
        "scroll-margin-inline-start",
        "scroll-margin-left",
        "scroll-margin-right",
        "scroll-margin-top",
        "scroll-padding",
        "scroll-padding-block",
        "scroll-padding-block-end",
        "scroll-padding-block-start",
        "scroll-padding-bottom",
        "scroll-padding-inline",
        "scroll-padding-inline-end",
        "scroll-padding-inline-start",
        "scroll-padding-left",
        "scroll-padding-right",
        "scroll-padding-top",
        "scroll-snap-align",
        "scroll-snap-stop",
        "scroll-snap-type",
        "scroll-timeline",
        "scroll-timeline-axis",
        "scroll-timeline-name",
        "scrollbar-color",
        "scrollbar-gutter",
        "scrollbar-width",
        "shape-image-threshold",
        "shape-margin",
        "shape-outside",
        "shape-rendering",
        "speak",
        "speak-as",
        "src",
        "stop-color",
        "stop-opacity",
        "stroke",
        "stroke-dasharray",
        "stroke-dashoffset",
        "stroke-linecap",
        "stroke-linejoin",
        "stroke-miterlimit",
        "stroke-opacity",
        "stroke-width",
        "tab-size",
        "table-layout",
        "text-align",
        "text-align-all",
        "text-align-last",
        "text-anchor",
        "text-combine-upright",
        "text-decoration",
        "text-decoration-color",
        "text-decoration-line",
        "text-decoration-skip",
        "text-decoration-skip-ink",
        "text-decoration-style",
        "text-decoration-thickness",
        "text-emphasis",
        "text-emphasis-color",
        "text-emphasis-position",
        "text-emphasis-style",
        "text-indent",
        "text-justify",
        "text-orientation",
        "text-overflow",
        "text-rendering",
        "text-shadow",
        "text-size-adjust",
        "text-transform",
        "text-underline-offset",
        "text-underline-position",
        "text-wrap",
        "text-wrap-mode",
        "text-wrap-style",
        "timeline-scope",
        "top",
        "touch-action",
        "transform",
        "transform-box",
        "transform-origin",
        "transform-style",
        "transition",
        "transition-behavior",
        "transition-delay",
        "transition-duration",
        "transition-property",
        "transition-timing-function",
        "translate",
        "unicode-bidi",
        "user-modify",
        "user-select",
        "vector-effect",
        "vertical-align",
        "view-timeline",
        "view-timeline-axis",
        "view-timeline-inset",
        "view-timeline-name",
        "view-transition-name",
        "visibility",
        "voice-balance",
        "voice-duration",
        "voice-family",
        "voice-pitch",
        "voice-range",
        "voice-rate",
        "voice-stress",
        "voice-volume",
        "white-space",
        "white-space-collapse",
        "widows",
        "width",
        "will-change",
        "word-break",
        "word-spacing",
        "word-wrap",
        "writing-mode",
        "x",
        "y",
        "z-index",
        "zoom",
      ]
        .sort()
        .reverse();
    function p(s) {
      const a = r(s),
        l = b,
        g = c,
        f = "@[a-z-]+",
        E = "and or not only",
        N = { className: "variable", begin: "(\\$" + "[a-zA-Z-][a-zA-Z0-9_-]*" + ")\\b", relevance: 0 };
      return {
        name: "SCSS",
        case_insensitive: !0,
        illegal: "[=/|']",
        contains: [
          s.C_LINE_COMMENT_MODE,
          s.C_BLOCK_COMMENT_MODE,
          a.CSS_NUMBER_MODE,
          { className: "selector-id", begin: "#[A-Za-z0-9_-]+", relevance: 0 },
          { className: "selector-class", begin: "\\.[A-Za-z0-9_-]+", relevance: 0 },
          a.ATTRIBUTE_SELECTOR_MODE,
          { className: "selector-tag", begin: "\\b(" + o.join("|") + ")\\b", relevance: 0 },
          { className: "selector-pseudo", begin: ":(" + g.join("|") + ")" },
          { className: "selector-pseudo", begin: ":(:)?(" + l.join("|") + ")" },
          N,
          { begin: /\(/, end: /\)/, contains: [a.CSS_NUMBER_MODE] },
          a.CSS_VARIABLE,
          { className: "attribute", begin: "\\b(" + _.join("|") + ")\\b" },
          {
            begin:
              "\\b(whitespace|wait|w-resize|visible|vertical-text|vertical-ideographic|uppercase|upper-roman|upper-alpha|underline|transparent|top|thin|thick|text|text-top|text-bottom|tb-rl|table-header-group|table-footer-group|sw-resize|super|strict|static|square|solid|small-caps|separate|se-resize|scroll|s-resize|rtl|row-resize|ridge|right|repeat|repeat-y|repeat-x|relative|progress|pointer|overline|outside|outset|oblique|nowrap|not-allowed|normal|none|nw-resize|no-repeat|no-drop|newspaper|ne-resize|n-resize|move|middle|medium|ltr|lr-tb|lowercase|lower-roman|lower-alpha|loose|list-item|line|line-through|line-edge|lighter|left|keep-all|justify|italic|inter-word|inter-ideograph|inside|inset|inline|inline-block|inherit|inactive|ideograph-space|ideograph-parenthesis|ideograph-numeric|ideograph-alpha|horizontal|hidden|help|hand|groove|fixed|ellipsis|e-resize|double|dotted|distribute|distribute-space|distribute-letter|distribute-all-lines|disc|disabled|default|decimal|dashed|crosshair|collapse|col-resize|circle|char|center|capitalize|break-word|break-all|bottom|both|bolder|bold|block|bidi-override|below|baseline|auto|always|all-scroll|absolute|table|table-cell)\\b",
          },
          {
            begin: /:/,
            end: /[;}{]/,
            relevance: 0,
            contains: [
              a.BLOCK_COMMENT,
              N,
              a.HEXCOLOR,
              a.CSS_NUMBER_MODE,
              s.QUOTE_STRING_MODE,
              s.APOS_STRING_MODE,
              a.IMPORTANT,
              a.FUNCTION_DISPATCH,
            ],
          },
          { begin: "@(page|font-face)", keywords: { $pattern: f, keyword: "@page @font-face" } },
          {
            begin: "@",
            end: "[{;]",
            returnBegin: !0,
            keywords: { $pattern: /[a-z-]+/, keyword: E, attribute: d.join(" ") },
            contains: [
              { begin: f, className: "keyword" },
              { begin: /[a-z-]+(?=:)/, className: "attribute" },
              N,
              s.QUOTE_STRING_MODE,
              s.APOS_STRING_MODE,
              a.HEXCOLOR,
              a.CSS_NUMBER_MODE,
            ],
          },
          a.FUNCTION_DISPATCH,
        ],
      };
    }
    return (In = p), In;
  }
  var xn, Ct;
  function Sr() {
    if (Ct) return xn;
    Ct = 1;
    function r(e) {
      return {
        name: "Shell Session",
        aliases: ["console", "shellsession"],
        contains: [
          {
            className: "meta.prompt",
            begin: /^\s{0,3}[/~\w\d[\]()@-]*[>%$#][ ]?/,
            starts: { end: /[^\\](?=\s*$)/, subLanguage: "bash" },
          },
        ],
      };
    }
    return (xn = r), xn;
  }
  var Ln, It;
  function wr() {
    if (It) return Ln;
    It = 1;
    function r(e) {
      const t = e.regex,
        o = e.COMMENT("--", "$"),
        d = { scope: "string", variants: [{ begin: /'/, end: /'/, contains: [{ match: /''/ }] }] },
        c = { begin: /"/, end: /"/, contains: [{ match: /""/ }] },
        b = ["true", "false", "unknown"],
        _ = ["double precision", "large object", "with timezone", "without timezone"],
        p = [
          "bigint",
          "binary",
          "blob",
          "boolean",
          "char",
          "character",
          "clob",
          "date",
          "dec",
          "decfloat",
          "decimal",
          "float",
          "int",
          "integer",
          "interval",
          "nchar",
          "nclob",
          "national",
          "numeric",
          "real",
          "row",
          "smallint",
          "time",
          "timestamp",
          "varchar",
          "varying",
          "varbinary",
        ],
        s = ["add", "asc", "collation", "desc", "final", "first", "last", "view"],
        a = [
          "abs",
          "acos",
          "all",
          "allocate",
          "alter",
          "and",
          "any",
          "are",
          "array",
          "array_agg",
          "array_max_cardinality",
          "as",
          "asensitive",
          "asin",
          "asymmetric",
          "at",
          "atan",
          "atomic",
          "authorization",
          "avg",
          "begin",
          "begin_frame",
          "begin_partition",
          "between",
          "bigint",
          "binary",
          "blob",
          "boolean",
          "both",
          "by",
          "call",
          "called",
          "cardinality",
          "cascaded",
          "case",
          "cast",
          "ceil",
          "ceiling",
          "char",
          "char_length",
          "character",
          "character_length",
          "check",
          "classifier",
          "clob",
          "close",
          "coalesce",
          "collate",
          "collect",
          "column",
          "commit",
          "condition",
          "connect",
          "constraint",
          "contains",
          "convert",
          "copy",
          "corr",
          "corresponding",
          "cos",
          "cosh",
          "count",
          "covar_pop",
          "covar_samp",
          "create",
          "cross",
          "cube",
          "cume_dist",
          "current",
          "current_catalog",
          "current_date",
          "current_default_transform_group",
          "current_path",
          "current_role",
          "current_row",
          "current_schema",
          "current_time",
          "current_timestamp",
          "current_path",
          "current_role",
          "current_transform_group_for_type",
          "current_user",
          "cursor",
          "cycle",
          "date",
          "day",
          "deallocate",
          "dec",
          "decimal",
          "decfloat",
          "declare",
          "default",
          "define",
          "delete",
          "dense_rank",
          "deref",
          "describe",
          "deterministic",
          "disconnect",
          "distinct",
          "double",
          "drop",
          "dynamic",
          "each",
          "element",
          "else",
          "empty",
          "end",
          "end_frame",
          "end_partition",
          "end-exec",
          "equals",
          "escape",
          "every",
          "except",
          "exec",
          "execute",
          "exists",
          "exp",
          "external",
          "extract",
          "false",
          "fetch",
          "filter",
          "first_value",
          "float",
          "floor",
          "for",
          "foreign",
          "frame_row",
          "free",
          "from",
          "full",
          "function",
          "fusion",
          "get",
          "global",
          "grant",
          "group",
          "grouping",
          "groups",
          "having",
          "hold",
          "hour",
          "identity",
          "in",
          "indicator",
          "initial",
          "inner",
          "inout",
          "insensitive",
          "insert",
          "int",
          "integer",
          "intersect",
          "intersection",
          "interval",
          "into",
          "is",
          "join",
          "json_array",
          "json_arrayagg",
          "json_exists",
          "json_object",
          "json_objectagg",
          "json_query",
          "json_table",
          "json_table_primitive",
          "json_value",
          "lag",
          "language",
          "large",
          "last_value",
          "lateral",
          "lead",
          "leading",
          "left",
          "like",
          "like_regex",
          "listagg",
          "ln",
          "local",
          "localtime",
          "localtimestamp",
          "log",
          "log10",
          "lower",
          "match",
          "match_number",
          "match_recognize",
          "matches",
          "max",
          "member",
          "merge",
          "method",
          "min",
          "minute",
          "mod",
          "modifies",
          "module",
          "month",
          "multiset",
          "national",
          "natural",
          "nchar",
          "nclob",
          "new",
          "no",
          "none",
          "normalize",
          "not",
          "nth_value",
          "ntile",
          "null",
          "nullif",
          "numeric",
          "octet_length",
          "occurrences_regex",
          "of",
          "offset",
          "old",
          "omit",
          "on",
          "one",
          "only",
          "open",
          "or",
          "order",
          "out",
          "outer",
          "over",
          "overlaps",
          "overlay",
          "parameter",
          "partition",
          "pattern",
          "per",
          "percent",
          "percent_rank",
          "percentile_cont",
          "percentile_disc",
          "period",
          "portion",
          "position",
          "position_regex",
          "power",
          "precedes",
          "precision",
          "prepare",
          "primary",
          "procedure",
          "ptf",
          "range",
          "rank",
          "reads",
          "real",
          "recursive",
          "ref",
          "references",
          "referencing",
          "regr_avgx",
          "regr_avgy",
          "regr_count",
          "regr_intercept",
          "regr_r2",
          "regr_slope",
          "regr_sxx",
          "regr_sxy",
          "regr_syy",
          "release",
          "result",
          "return",
          "returns",
          "revoke",
          "right",
          "rollback",
          "rollup",
          "row",
          "row_number",
          "rows",
          "running",
          "savepoint",
          "scope",
          "scroll",
          "search",
          "second",
          "seek",
          "select",
          "sensitive",
          "session_user",
          "set",
          "show",
          "similar",
          "sin",
          "sinh",
          "skip",
          "smallint",
          "some",
          "specific",
          "specifictype",
          "sql",
          "sqlexception",
          "sqlstate",
          "sqlwarning",
          "sqrt",
          "start",
          "static",
          "stddev_pop",
          "stddev_samp",
          "submultiset",
          "subset",
          "substring",
          "substring_regex",
          "succeeds",
          "sum",
          "symmetric",
          "system",
          "system_time",
          "system_user",
          "table",
          "tablesample",
          "tan",
          "tanh",
          "then",
          "time",
          "timestamp",
          "timezone_hour",
          "timezone_minute",
          "to",
          "trailing",
          "translate",
          "translate_regex",
          "translation",
          "treat",
          "trigger",
          "trim",
          "trim_array",
          "true",
          "truncate",
          "uescape",
          "union",
          "unique",
          "unknown",
          "unnest",
          "update",
          "upper",
          "user",
          "using",
          "value",
          "values",
          "value_of",
          "var_pop",
          "var_samp",
          "varbinary",
          "varchar",
          "varying",
          "versioning",
          "when",
          "whenever",
          "where",
          "width_bucket",
          "window",
          "with",
          "within",
          "without",
          "year",
        ],
        l = [
          "abs",
          "acos",
          "array_agg",
          "asin",
          "atan",
          "avg",
          "cast",
          "ceil",
          "ceiling",
          "coalesce",
          "corr",
          "cos",
          "cosh",
          "count",
          "covar_pop",
          "covar_samp",
          "cume_dist",
          "dense_rank",
          "deref",
          "element",
          "exp",
          "extract",
          "first_value",
          "floor",
          "json_array",
          "json_arrayagg",
          "json_exists",
          "json_object",
          "json_objectagg",
          "json_query",
          "json_table",
          "json_table_primitive",
          "json_value",
          "lag",
          "last_value",
          "lead",
          "listagg",
          "ln",
          "log",
          "log10",
          "lower",
          "max",
          "min",
          "mod",
          "nth_value",
          "ntile",
          "nullif",
          "percent_rank",
          "percentile_cont",
          "percentile_disc",
          "position",
          "position_regex",
          "power",
          "rank",
          "regr_avgx",
          "regr_avgy",
          "regr_count",
          "regr_intercept",
          "regr_r2",
          "regr_slope",
          "regr_sxx",
          "regr_sxy",
          "regr_syy",
          "row_number",
          "sin",
          "sinh",
          "sqrt",
          "stddev_pop",
          "stddev_samp",
          "substring",
          "substring_regex",
          "sum",
          "tan",
          "tanh",
          "translate",
          "translate_regex",
          "treat",
          "trim",
          "trim_array",
          "unnest",
          "upper",
          "value_of",
          "var_pop",
          "var_samp",
          "width_bucket",
        ],
        g = [
          "current_catalog",
          "current_date",
          "current_default_transform_group",
          "current_path",
          "current_role",
          "current_schema",
          "current_transform_group_for_type",
          "current_user",
          "session_user",
          "system_time",
          "system_user",
          "current_time",
          "localtime",
          "current_timestamp",
          "localtimestamp",
        ],
        f = [
          "create table",
          "insert into",
          "primary key",
          "foreign key",
          "not null",
          "alter table",
          "add constraint",
          "grouping sets",
          "on overflow",
          "character set",
          "respect nulls",
          "ignore nulls",
          "nulls first",
          "nulls last",
          "depth first",
          "breadth first",
        ],
        E = l,
        y = [...a, ...s].filter((D) => !l.includes(D)),
        N = { scope: "variable", match: /@[a-z0-9][a-z0-9_]*/ },
        T = { scope: "operator", match: /[-+*/=%^~]|&&?|\|\|?|!=?|<(?:=>?|<|>)?|>[>=]?/, relevance: 0 },
        O = { match: t.concat(/\b/, t.either(...E), /\s*\(/), relevance: 0, keywords: { built_in: E } };
      function R(D) {
        return t.concat(/\b/, t.either(...D.map((B) => B.replace(/\s+/, "\\s+"))), /\b/);
      }
      const F = { scope: "keyword", match: R(f), relevance: 0 };
      function x(D, { exceptions: B, when: M } = {}) {
        const U = M;
        return (B = B || []), D.map((K) => (K.match(/\|\d+$/) || B.includes(K) ? K : U(K) ? "".concat(K, "|0") : K));
      }
      return {
        name: "SQL",
        case_insensitive: !0,
        illegal: /[{}]|<\//,
        keywords: {
          $pattern: /\b[\w\.]+/,
          keyword: x(y, { when: (D) => D.length < 3 }),
          literal: b,
          type: p,
          built_in: g,
        },
        contains: [{ scope: "type", match: R(_) }, F, O, N, d, c, e.C_NUMBER_MODE, e.C_BLOCK_COMMENT_MODE, o, T],
      };
    }
    return (Ln = r), Ln;
  }
  var Dn, xt;
  function Or() {
    if (xt) return Dn;
    xt = 1;
    function r(M) {
      return M ? (typeof M == "string" ? M : M.source) : null;
    }
    function e(M) {
      return t("(?=", M, ")");
    }
    function t(...M) {
      return M.map((K) => r(K)).join("");
    }
    function o(M) {
      const U = M[M.length - 1];
      return typeof U == "object" && U.constructor === Object ? (M.splice(M.length - 1, 1), U) : {};
    }
    function d(...M) {
      return "(" + (o(M).capture ? "" : "?:") + M.map((G) => r(G)).join("|") + ")";
    }
    const c = (M) => t(/\b/, M, /\w$/.test(M) ? /\b/ : /\B/),
      b = ["Protocol", "Type"].map(c),
      _ = ["init", "self"].map(c),
      p = ["Any", "Self"],
      s = [
        "actor",
        "any",
        "associatedtype",
        "async",
        "await",
        /as\?/,
        /as!/,
        "as",
        "borrowing",
        "break",
        "case",
        "catch",
        "class",
        "consume",
        "consuming",
        "continue",
        "convenience",
        "copy",
        "default",
        "defer",
        "deinit",
        "didSet",
        "distributed",
        "do",
        "dynamic",
        "each",
        "else",
        "enum",
        "extension",
        "fallthrough",
        /fileprivate\(set\)/,
        "fileprivate",
        "final",
        "for",
        "func",
        "get",
        "guard",
        "if",
        "import",
        "indirect",
        "infix",
        /init\?/,
        /init!/,
        "inout",
        /internal\(set\)/,
        "internal",
        "in",
        "is",
        "isolated",
        "nonisolated",
        "lazy",
        "let",
        "macro",
        "mutating",
        "nonmutating",
        /open\(set\)/,
        "open",
        "operator",
        "optional",
        "override",
        "package",
        "postfix",
        "precedencegroup",
        "prefix",
        /private\(set\)/,
        "private",
        "protocol",
        /public\(set\)/,
        "public",
        "repeat",
        "required",
        "rethrows",
        "return",
        "set",
        "some",
        "static",
        "struct",
        "subscript",
        "super",
        "switch",
        "throws",
        "throw",
        /try\?/,
        /try!/,
        "try",
        "typealias",
        /unowned\(safe\)/,
        /unowned\(unsafe\)/,
        "unowned",
        "var",
        "weak",
        "where",
        "while",
        "willSet",
      ],
      a = ["false", "nil", "true"],
      l = ["assignment", "associativity", "higherThan", "left", "lowerThan", "none", "right"],
      g = [
        "#colorLiteral",
        "#column",
        "#dsohandle",
        "#else",
        "#elseif",
        "#endif",
        "#error",
        "#file",
        "#fileID",
        "#fileLiteral",
        "#filePath",
        "#function",
        "#if",
        "#imageLiteral",
        "#keyPath",
        "#line",
        "#selector",
        "#sourceLocation",
        "#warning",
      ],
      f = [
        "abs",
        "all",
        "any",
        "assert",
        "assertionFailure",
        "debugPrint",
        "dump",
        "fatalError",
        "getVaList",
        "isKnownUniquelyReferenced",
        "max",
        "min",
        "numericCast",
        "pointwiseMax",
        "pointwiseMin",
        "precondition",
        "preconditionFailure",
        "print",
        "readLine",
        "repeatElement",
        "sequence",
        "stride",
        "swap",
        "swift_unboxFromSwiftValueWithType",
        "transcode",
        "type",
        "unsafeBitCast",
        "unsafeDowncast",
        "withExtendedLifetime",
        "withUnsafeMutablePointer",
        "withUnsafePointer",
        "withVaList",
        "withoutActuallyEscaping",
        "zip",
      ],
      E = d(
        /[/=\-+!*%<>&|^~?]/,
        /[\u00A1-\u00A7]/,
        /[\u00A9\u00AB]/,
        /[\u00AC\u00AE]/,
        /[\u00B0\u00B1]/,
        /[\u00B6\u00BB\u00BF\u00D7\u00F7]/,
        /[\u2016-\u2017]/,
        /[\u2020-\u2027]/,
        /[\u2030-\u203E]/,
        /[\u2041-\u2053]/,
        /[\u2055-\u205E]/,
        /[\u2190-\u23FF]/,
        /[\u2500-\u2775]/,
        /[\u2794-\u2BFF]/,
        /[\u2E00-\u2E7F]/,
        /[\u3001-\u3003]/,
        /[\u3008-\u3020]/,
        /[\u3030]/
      ),
      y = d(E, /[\u0300-\u036F]/, /[\u1DC0-\u1DFF]/, /[\u20D0-\u20FF]/, /[\uFE00-\uFE0F]/, /[\uFE20-\uFE2F]/),
      N = t(E, y, "*"),
      T = d(
        /[a-zA-Z_]/,
        /[\u00A8\u00AA\u00AD\u00AF\u00B2-\u00B5\u00B7-\u00BA]/,
        /[\u00BC-\u00BE\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u00FF]/,
        /[\u0100-\u02FF\u0370-\u167F\u1681-\u180D\u180F-\u1DBF]/,
        /[\u1E00-\u1FFF]/,
        /[\u200B-\u200D\u202A-\u202E\u203F-\u2040\u2054\u2060-\u206F]/,
        /[\u2070-\u20CF\u2100-\u218F\u2460-\u24FF\u2776-\u2793]/,
        /[\u2C00-\u2DFF\u2E80-\u2FFF]/,
        /[\u3004-\u3007\u3021-\u302F\u3031-\u303F\u3040-\uD7FF]/,
        /[\uF900-\uFD3D\uFD40-\uFDCF\uFDF0-\uFE1F\uFE30-\uFE44]/,
        /[\uFE47-\uFEFE\uFF00-\uFFFD]/
      ),
      O = d(T, /\d/, /[\u0300-\u036F\u1DC0-\u1DFF\u20D0-\u20FF\uFE20-\uFE2F]/),
      R = t(T, O, "*"),
      F = t(/[A-Z]/, O, "*"),
      x = [
        "attached",
        "autoclosure",
        t(/convention\(/, d("swift", "block", "c"), /\)/),
        "discardableResult",
        "dynamicCallable",
        "dynamicMemberLookup",
        "escaping",
        "freestanding",
        "frozen",
        "GKInspectable",
        "IBAction",
        "IBDesignable",
        "IBInspectable",
        "IBOutlet",
        "IBSegueAction",
        "inlinable",
        "main",
        "nonobjc",
        "NSApplicationMain",
        "NSCopying",
        "NSManaged",
        t(/objc\(/, R, /\)/),
        "objc",
        "objcMembers",
        "propertyWrapper",
        "requires_stored_property_inits",
        "resultBuilder",
        "Sendable",
        "testable",
        "UIApplicationMain",
        "unchecked",
        "unknown",
        "usableFromInline",
        "warn_unqualified_access",
      ],
      D = [
        "iOS",
        "iOSApplicationExtension",
        "macOS",
        "macOSApplicationExtension",
        "macCatalyst",
        "macCatalystApplicationExtension",
        "watchOS",
        "watchOSApplicationExtension",
        "tvOS",
        "tvOSApplicationExtension",
        "swift",
      ];
    function B(M) {
      const U = { match: /\s+/, relevance: 0 },
        K = M.COMMENT("/\\*", "\\*/", { contains: ["self"] }),
        G = [M.C_LINE_COMMENT_MODE, K],
        re = { match: [/\./, d(...b, ..._)], className: { 2: "keyword" } },
        ee = { match: t(/\./, d(...s)), relevance: 0 },
        ne = s.filter((z) => typeof z == "string").concat(["_|0"]),
        W = s
          .filter((z) => typeof z != "string")
          .concat(p)
          .map(c),
        Z = { variants: [{ className: "keyword", match: d(...W, ..._) }] },
        J = { $pattern: d(/\b\w+/, /#\w+/), keyword: ne.concat(g), literal: a },
        se = [re, ee, Z],
        Me = { match: t(/\./, d(...f)), relevance: 0 },
        ke = { className: "built_in", match: t(/\b/, d(...f), /(?=\()/) },
        Ne = [Me, ke],
        Se = { match: /->/, relevance: 0 },
        Ce = { className: "operator", relevance: 0, variants: [{ match: N }, { match: "\\.(\\.|".concat(y, ")+") }] },
        pe = [Se, Ce],
        me = "([0-9]_*)+",
        ye = "([0-9a-fA-F]_*)+",
        X = {
          className: "number",
          relevance: 0,
          variants: [
            { match: "\\b(".concat(me, ")(\\.(").concat(me, "))?") + "([eE][+-]?(".concat(me, "))?\\b") },
            { match: "\\b0x(".concat(ye, ")(\\.(").concat(ye, "))?") + "([pP][+-]?(".concat(me, "))?\\b") },
            { match: /\b0o([0-7]_*)+\b/ },
            { match: /\b0b([01]_*)+\b/ },
          ],
        },
        Y = (z = "") => ({
          className: "subst",
          variants: [{ match: t(/\\/, z, /[0\\tnr"']/) }, { match: t(/\\/, z, /u\{[0-9a-fA-F]{1,8}\}/) }],
        }),
        le = (z = "") => ({ className: "subst", match: t(/\\/, z, /[\t ]*(?:[\r\n]|\r\n)/) }),
        ae = (z = "") => ({ className: "subst", label: "interpol", begin: t(/\\/, z, /\(/), end: /\)/ }),
        de = (z = "") => ({ begin: t(z, /"""/), end: t(/"""/, z), contains: [Y(z), le(z), ae(z)] }),
        ge = (z = "") => ({ begin: t(z, /"/), end: t(/"/, z), contains: [Y(z), ae(z)] }),
        _e = {
          className: "string",
          variants: [de(), de("#"), de("##"), de("###"), ge(), ge("#"), ge("##"), ge("###")],
        },
        $e = [M.BACKSLASH_ESCAPE, { begin: /\[/, end: /\]/, relevance: 0, contains: [M.BACKSLASH_ESCAPE] }],
        $n = { begin: /\/[^\s](?=[^/\n]*\/)/, end: /\//, contains: $e },
        Be = (z) => {
          const De = t(z, /\//),
            we = t(/\//, z);
          return {
            begin: De,
            end: we,
            contains: [...$e, { scope: "comment", begin: "#(?!.*".concat(we, ")"), end: /$/ }],
          };
        },
        Ke = { scope: "regexp", variants: [Be("###"), Be("##"), Be("#"), $n] },
        Ge = { match: t(/`/, R, /`/) },
        Kn = { className: "variable", match: /\$\d+/ },
        qe = { className: "variable", match: "\\$".concat(O, "+") },
        Ue = [Ge, Kn, qe],
        Gn = {
          match: /(@|#(un)?)available/,
          scope: "keyword",
          starts: { contains: [{ begin: /\(/, end: /\)/, keywords: D, contains: [...pe, X, _e] }] },
        },
        He = { scope: "keyword", match: t(/@/, d(...x), e(d(/\(/, /\s+/))) },
        ve = { scope: "meta", match: t(/@/, R) },
        Ie = [Gn, He, ve],
        ue = {
          match: e(/\b[A-Z]/),
          relevance: 0,
          contains: [
            { className: "type", match: t(/(AV|CA|CF|CG|CI|CL|CM|CN|CT|MK|MP|MTK|MTL|NS|SCN|SK|UI|WK|XC)/, O, "+") },
            { className: "type", match: F, relevance: 0 },
            { match: /[?!]+/, relevance: 0 },
            { match: /\.\.\./, relevance: 0 },
            { match: t(/\s+&\s+/, e(F)), relevance: 0 },
          ],
        },
        Le = { begin: /</, end: />/, keywords: J, contains: [...G, ...se, ...Ie, Se, ue] };
      ue.contains.push(Le);
      const We = { match: t(R, /\s*:/), keywords: "_|0", relevance: 0 },
        Ye = {
          begin: /\(/,
          end: /\)/,
          relevance: 0,
          keywords: J,
          contains: ["self", We, ...G, Ke, ...se, ...Ne, ...pe, X, _e, ...Ue, ...Ie, ue],
        },
        Pe = { begin: /</, end: />/, keywords: "repeat each", contains: [...G, ue] },
        qn = {
          begin: d(e(t(R, /\s*:/)), e(t(R, /\s+/, R, /\s*:/))),
          end: /:/,
          relevance: 0,
          contains: [
            { className: "keyword", match: /\b_\b/ },
            { className: "params", match: R },
          ],
        },
        Ze = {
          begin: /\(/,
          end: /\)/,
          keywords: J,
          contains: [qn, ...G, ...se, ...pe, X, _e, ...Ie, ue, Ye],
          endsParent: !0,
          illegal: /["']/,
        },
        Hn = {
          match: [/(func|macro)/, /\s+/, d(Ge.match, R, N)],
          className: { 1: "keyword", 3: "title.function" },
          contains: [Pe, Ze, U],
          illegal: [/\[/, /%/],
        },
        Xe = {
          match: [/\b(?:subscript|init[?!]?)/, /\s*(?=[<(])/],
          className: { 1: "keyword" },
          contains: [Pe, Ze, U],
          illegal: /\[|%/,
        },
        Wn = { match: [/operator/, /\s+/, N], className: { 1: "keyword", 3: "title" } },
        Yn = {
          begin: [/precedencegroup/, /\s+/, F],
          className: { 1: "keyword", 3: "title" },
          contains: [ue],
          keywords: [...l, ...a],
          end: /}/,
        },
        Zn = {
          match: [/class\b/, /\s+/, /func\b/, /\s+/, /\b[A-Za-z_][A-Za-z0-9_]*\b/],
          scope: { 1: "keyword", 3: "keyword", 5: "title.function" },
        },
        Fe = { match: [/class\b/, /\s+/, /var\b/], scope: { 1: "keyword", 3: "keyword" } },
        Ve = {
          begin: [/(struct|protocol|class|extension|enum|actor)/, /\s+/, R, /\s*/],
          beginScope: { 1: "keyword", 3: "title.class" },
          keywords: J,
          contains: [
            Pe,
            ...se,
            {
              begin: /:/,
              end: /\{/,
              keywords: J,
              contains: [{ scope: "title.class.inherited", match: F }, ...se],
              relevance: 0,
            },
          ],
        };
      for (const z of _e.variants) {
        const De = z.contains.find((Oe) => Oe.label === "interpol");
        De.keywords = J;
        const we = [...se, ...Ne, ...pe, X, _e, ...Ue];
        De.contains = [...we, { begin: /\(/, end: /\)/, contains: ["self", ...we] }];
      }
      return {
        name: "Swift",
        keywords: J,
        contains: [
          ...G,
          Hn,
          Xe,
          Zn,
          Fe,
          Ve,
          Wn,
          Yn,
          { beginKeywords: "import", end: /$/, contains: [...G], relevance: 0 },
          Ke,
          ...se,
          ...Ne,
          ...pe,
          X,
          _e,
          ...Ue,
          ...Ie,
          ue,
          Ye,
        ],
      };
    }
    return (Dn = B), Dn;
  }
  var Bn, Lt;
  function Ar() {
    if (Lt) return Bn;
    Lt = 1;
    function r(e) {
      const t = "true false yes no null",
        o = "[\\w#;/?:@&=+$,.~*'()[\\]]+",
        d = {
          className: "attr",
          variants: [
            { begin: /[\w*@][\w*@ :()\./-]*:(?=[ \t]|$)/ },
            { begin: /"[\w*@][\w*@ :()\./-]*":(?=[ \t]|$)/ },
            { begin: /'[\w*@][\w*@ :()\./-]*':(?=[ \t]|$)/ },
          ],
        },
        c = {
          className: "template-variable",
          variants: [
            { begin: /\{\{/, end: /\}\}/ },
            { begin: /%\{/, end: /\}/ },
          ],
        },
        b = {
          className: "string",
          relevance: 0,
          begin: /'/,
          end: /'/,
          contains: [{ match: /''/, scope: "char.escape", relevance: 0 }],
        },
        _ = {
          className: "string",
          relevance: 0,
          variants: [{ begin: /"/, end: /"/ }, { begin: /\S+/ }],
          contains: [e.BACKSLASH_ESCAPE, c],
        },
        p = e.inherit(_, {
          variants: [
            { begin: /'/, end: /'/, contains: [{ begin: /''/, relevance: 0 }] },
            { begin: /"/, end: /"/ },
            { begin: /[^\s,{}[\]]+/ },
          ],
        }),
        f = {
          className: "number",
          begin:
            "\\b" +
            "[0-9]{4}(-[0-9][0-9]){0,2}" +
            "([Tt \\t][0-9][0-9]?(:[0-9][0-9]){2})?" +
            "(\\.[0-9]*)?" +
            "([ \\t])*(Z|[-+][0-9][0-9]?(:[0-9][0-9])?)?" +
            "\\b",
        },
        E = { end: ",", endsWithParent: !0, excludeEnd: !0, keywords: t, relevance: 0 },
        y = { begin: /\{/, end: /\}/, contains: [E], illegal: "\\n", relevance: 0 },
        N = { begin: "\\[", end: "\\]", contains: [E], illegal: "\\n", relevance: 0 },
        T = [
          d,
          { className: "meta", begin: "^---\\s*$", relevance: 10 },
          { className: "string", begin: "[\\|>]([1-9]?[+-])?[ ]*\\n( +)[^ ][^\\n]*\\n(\\2[^\\n]+\\n?)*" },
          { begin: "<%[%=-]?", end: "[%-]?%>", subLanguage: "ruby", excludeBegin: !0, excludeEnd: !0, relevance: 0 },
          { className: "type", begin: "!\\w+!" + o },
          { className: "type", begin: "!<" + o + ">" },
          { className: "type", begin: "!" + o },
          { className: "type", begin: "!!" + o },
          { className: "meta", begin: "&" + e.UNDERSCORE_IDENT_RE + "$" },
          { className: "meta", begin: "\\*" + e.UNDERSCORE_IDENT_RE + "$" },
          { className: "bullet", begin: "-(?=[ ]|$)", relevance: 0 },
          e.HASH_COMMENT_MODE,
          { beginKeywords: t, keywords: { literal: t } },
          f,
          { className: "number", begin: e.C_NUMBER_RE + "\\b", relevance: 0 },
          y,
          N,
          b,
          _,
        ],
        O = [...T];
      return (
        O.pop(), O.push(p), (E.contains = O), { name: "YAML", case_insensitive: !0, aliases: ["yml"], contains: T }
      );
    }
    return (Bn = r), Bn;
  }
  var Un, Dt;
  function Rr() {
    if (Dt) return Un;
    Dt = 1;
    const r = "[A-Za-z$_][0-9A-Za-z$_]*",
      e = [
        "as",
        "in",
        "of",
        "if",
        "for",
        "while",
        "finally",
        "var",
        "new",
        "function",
        "do",
        "return",
        "void",
        "else",
        "break",
        "catch",
        "instanceof",
        "with",
        "throw",
        "case",
        "default",
        "try",
        "switch",
        "continue",
        "typeof",
        "delete",
        "let",
        "yield",
        "const",
        "class",
        "debugger",
        "async",
        "await",
        "static",
        "import",
        "from",
        "export",
        "extends",
        "using",
      ],
      t = ["true", "false", "null", "undefined", "NaN", "Infinity"],
      o = [
        "Object",
        "Function",
        "Boolean",
        "Symbol",
        "Math",
        "Date",
        "Number",
        "BigInt",
        "String",
        "RegExp",
        "Array",
        "Float32Array",
        "Float64Array",
        "Int8Array",
        "Uint8Array",
        "Uint8ClampedArray",
        "Int16Array",
        "Int32Array",
        "Uint16Array",
        "Uint32Array",
        "BigInt64Array",
        "BigUint64Array",
        "Set",
        "Map",
        "WeakSet",
        "WeakMap",
        "ArrayBuffer",
        "SharedArrayBuffer",
        "Atomics",
        "DataView",
        "JSON",
        "Promise",
        "Generator",
        "GeneratorFunction",
        "AsyncFunction",
        "Reflect",
        "Proxy",
        "Intl",
        "WebAssembly",
      ],
      d = [
        "Error",
        "EvalError",
        "InternalError",
        "RangeError",
        "ReferenceError",
        "SyntaxError",
        "TypeError",
        "URIError",
      ],
      c = [
        "setInterval",
        "setTimeout",
        "clearInterval",
        "clearTimeout",
        "require",
        "exports",
        "eval",
        "isFinite",
        "isNaN",
        "parseFloat",
        "parseInt",
        "decodeURI",
        "decodeURIComponent",
        "encodeURI",
        "encodeURIComponent",
        "escape",
        "unescape",
      ],
      b = [
        "arguments",
        "this",
        "super",
        "console",
        "window",
        "document",
        "localStorage",
        "sessionStorage",
        "module",
        "global",
      ],
      _ = [].concat(c, o, d);
    function p(a) {
      const l = a.regex,
        g = (Y, { after: le }) => {
          const ae = "</" + Y[0].slice(1);
          return Y.input.indexOf(ae, le) !== -1;
        },
        f = r,
        E = { begin: "<>", end: "</>" },
        y = /<[A-Za-z0-9\\._:-]+\s*\/>/,
        N = {
          begin: /<[A-Za-z0-9\\._:-]+/,
          end: /\/[A-Za-z0-9\\._:-]+>|\/>/,
          isTrulyOpeningTag: (Y, le) => {
            const ae = Y[0].length + Y.index,
              de = Y.input[ae];
            if (de === "<" || de === ",") {
              le.ignoreMatch();
              return;
            }
            de === ">" && (g(Y, { after: ae }) || le.ignoreMatch());
            let ge;
            const _e = Y.input.substring(ae);
            if ((ge = _e.match(/^\s*=/))) {
              le.ignoreMatch();
              return;
            }
            if ((ge = _e.match(/^\s+extends\s+/)) && ge.index === 0) {
              le.ignoreMatch();
              return;
            }
          },
        },
        T = { $pattern: r, keyword: e, literal: t, built_in: _, "variable.language": b },
        O = "[0-9](_?[0-9])*",
        R = "\\.(".concat(O, ")"),
        F = "0|[1-9](_?[0-9])*|0[0-7]*[89][0-9]*",
        x = {
          className: "number",
          variants: [
            { begin: "(\\b(".concat(F, ")((").concat(R, ")|\\.)?|(").concat(R, "))") + "[eE][+-]?(".concat(O, ")\\b") },
            { begin: "\\b(".concat(F, ")\\b((").concat(R, ")\\b|\\.)?|(").concat(R, ")\\b") },
            { begin: "\\b(0|[1-9](_?[0-9])*)n\\b" },
            { begin: "\\b0[xX][0-9a-fA-F](_?[0-9a-fA-F])*n?\\b" },
            { begin: "\\b0[bB][0-1](_?[0-1])*n?\\b" },
            { begin: "\\b0[oO][0-7](_?[0-7])*n?\\b" },
            { begin: "\\b0[0-7]+n?\\b" },
          ],
          relevance: 0,
        },
        D = { className: "subst", begin: "\\$\\{", end: "\\}", keywords: T, contains: [] },
        B = {
          begin: ".?html`",
          end: "",
          starts: { end: "`", returnEnd: !1, contains: [a.BACKSLASH_ESCAPE, D], subLanguage: "xml" },
        },
        M = {
          begin: ".?css`",
          end: "",
          starts: { end: "`", returnEnd: !1, contains: [a.BACKSLASH_ESCAPE, D], subLanguage: "css" },
        },
        U = {
          begin: ".?gql`",
          end: "",
          starts: { end: "`", returnEnd: !1, contains: [a.BACKSLASH_ESCAPE, D], subLanguage: "graphql" },
        },
        K = { className: "string", begin: "`", end: "`", contains: [a.BACKSLASH_ESCAPE, D] },
        re = {
          className: "comment",
          variants: [
            a.COMMENT(/\/\*\*(?!\/)/, "\\*/", {
              relevance: 0,
              contains: [
                {
                  begin: "(?=@[A-Za-z]+)",
                  relevance: 0,
                  contains: [
                    { className: "doctag", begin: "@[A-Za-z]+" },
                    { className: "type", begin: "\\{", end: "\\}", excludeEnd: !0, excludeBegin: !0, relevance: 0 },
                    { className: "variable", begin: f + "(?=\\s*(-)|$)", endsParent: !0, relevance: 0 },
                    { begin: /(?=[^\n])\s/, relevance: 0 },
                  ],
                },
              ],
            }),
            a.C_BLOCK_COMMENT_MODE,
            a.C_LINE_COMMENT_MODE,
          ],
        },
        ee = [a.APOS_STRING_MODE, a.QUOTE_STRING_MODE, B, M, U, K, { match: /\$\d+/ }, x];
      D.contains = ee.concat({ begin: /\{/, end: /\}/, keywords: T, contains: ["self"].concat(ee) });
      const ne = [].concat(re, D.contains),
        W = ne.concat([{ begin: /(\s*)\(/, end: /\)/, keywords: T, contains: ["self"].concat(ne) }]),
        Z = {
          className: "params",
          begin: /(\s*)\(/,
          end: /\)/,
          excludeBegin: !0,
          excludeEnd: !0,
          keywords: T,
          contains: W,
        },
        J = {
          variants: [
            {
              match: [/class/, /\s+/, f, /\s+/, /extends/, /\s+/, l.concat(f, "(", l.concat(/\./, f), ")*")],
              scope: { 1: "keyword", 3: "title.class", 5: "keyword", 7: "title.class.inherited" },
            },
            { match: [/class/, /\s+/, f], scope: { 1: "keyword", 3: "title.class" } },
          ],
        },
        se = {
          relevance: 0,
          match: l.either(
            /\bJSON/,
            /\b[A-Z][a-z]+([A-Z][a-z]*|\d)*/,
            /\b[A-Z]{2,}([A-Z][a-z]+|\d)+([A-Z][a-z]*)*/,
            /\b[A-Z]{2,}[a-z]+([A-Z][a-z]+|\d)*([A-Z][a-z]*)*/
          ),
          className: "title.class",
          keywords: { _: [...o, ...d] },
        },
        Me = { label: "use_strict", className: "meta", relevance: 10, begin: /^\s*['"]use (strict|asm)['"]/ },
        ke = {
          variants: [{ match: [/function/, /\s+/, f, /(?=\s*\()/] }, { match: [/function/, /\s*(?=\()/] }],
          className: { 1: "keyword", 3: "title.function" },
          label: "func.def",
          contains: [Z],
          illegal: /%/,
        },
        Ne = { relevance: 0, match: /\b[A-Z][A-Z_0-9]+\b/, className: "variable.constant" };
      function Se(Y) {
        return l.concat("(?!", Y.join("|"), ")");
      }
      const Ce = {
          match: l.concat(
            /\b/,
            Se([...c, "super", "import"].map((Y) => "".concat(Y, "\\s*\\("))),
            f,
            l.lookahead(/\s*\(/)
          ),
          className: "title.function",
          relevance: 0,
        },
        pe = {
          begin: l.concat(/\./, l.lookahead(l.concat(f, /(?![0-9A-Za-z$_(])/))),
          end: f,
          excludeBegin: !0,
          keywords: "prototype",
          className: "property",
          relevance: 0,
        },
        me = {
          match: [/get|set/, /\s+/, f, /(?=\()/],
          className: { 1: "keyword", 3: "title.function" },
          contains: [{ begin: /\(\)/ }, Z],
        },
        ye = "(\\([^()]*(\\([^()]*(\\([^()]*\\)[^()]*)*\\)[^()]*)*\\)|" + a.UNDERSCORE_IDENT_RE + ")\\s*=>",
        X = {
          match: [/const|var|let/, /\s+/, f, /\s*/, /=\s*/, /(async\s*)?/, l.lookahead(ye)],
          keywords: "async",
          className: { 1: "keyword", 3: "title.function" },
          contains: [Z],
        };
      return {
        name: "JavaScript",
        aliases: ["js", "jsx", "mjs", "cjs"],
        keywords: T,
        exports: { PARAMS_CONTAINS: W, CLASS_REFERENCE: se },
        illegal: /#(?![$_A-z])/,
        contains: [
          a.SHEBANG({ label: "shebang", binary: "node", relevance: 5 }),
          Me,
          a.APOS_STRING_MODE,
          a.QUOTE_STRING_MODE,
          B,
          M,
          U,
          K,
          re,
          { match: /\$\d+/ },
          x,
          se,
          { scope: "attr", match: f + l.lookahead(":"), relevance: 0 },
          X,
          {
            begin: "(" + a.RE_STARTERS_RE + "|\\b(case|return|throw)\\b)\\s*",
            keywords: "return throw case",
            relevance: 0,
            contains: [
              re,
              a.REGEXP_MODE,
              {
                className: "function",
                begin: ye,
                returnBegin: !0,
                end: "\\s*=>",
                contains: [
                  {
                    className: "params",
                    variants: [
                      { begin: a.UNDERSCORE_IDENT_RE, relevance: 0 },
                      { className: null, begin: /\(\s*\)/, skip: !0 },
                      { begin: /(\s*)\(/, end: /\)/, excludeBegin: !0, excludeEnd: !0, keywords: T, contains: W },
                    ],
                  },
                ],
              },
              { begin: /,/, relevance: 0 },
              { match: /\s+/, relevance: 0 },
              {
                variants: [
                  { begin: E.begin, end: E.end },
                  { match: y },
                  { begin: N.begin, "on:begin": N.isTrulyOpeningTag, end: N.end },
                ],
                subLanguage: "xml",
                contains: [{ begin: N.begin, end: N.end, skip: !0, contains: ["self"] }],
              },
            ],
          },
          ke,
          { beginKeywords: "while if switch catch for" },
          {
            begin:
              "\\b(?!function)" +
              a.UNDERSCORE_IDENT_RE +
              "\\([^()]*(\\([^()]*(\\([^()]*\\)[^()]*)*\\)[^()]*)*\\)\\s*\\{",
            returnBegin: !0,
            label: "func.def",
            contains: [Z, a.inherit(a.TITLE_MODE, { begin: f, className: "title.function" })],
          },
          { match: /\.\.\./, relevance: 0 },
          pe,
          { match: "\\$" + f, relevance: 0 },
          { match: [/\bconstructor(?=\s*\()/], className: { 1: "title.function" }, contains: [Z] },
          Ce,
          Ne,
          J,
          me,
          { match: /\$[(.]/ },
        ],
      };
    }
    function s(a) {
      const l = a.regex,
        g = p(a),
        f = r,
        E = ["any", "void", "number", "boolean", "string", "object", "never", "symbol", "bigint", "unknown"],
        y = { begin: [/namespace/, /\s+/, a.IDENT_RE], beginScope: { 1: "keyword", 3: "title.class" } },
        N = {
          beginKeywords: "interface",
          end: /\{/,
          excludeEnd: !0,
          keywords: { keyword: "interface extends", built_in: E },
          contains: [g.exports.CLASS_REFERENCE],
        },
        T = { className: "meta", relevance: 10, begin: /^\s*['"]use strict['"]/ },
        O = [
          "type",
          "interface",
          "public",
          "private",
          "protected",
          "implements",
          "declare",
          "abstract",
          "readonly",
          "enum",
          "override",
          "satisfies",
        ],
        R = { $pattern: r, keyword: e.concat(O), literal: t, built_in: _.concat(E), "variable.language": b },
        F = { className: "meta", begin: "@" + f },
        x = (U, K, G) => {
          const re = U.contains.findIndex((ee) => ee.label === K);
          if (re === -1) throw new Error("can not find mode to replace");
          U.contains.splice(re, 1, G);
        };
      Object.assign(g.keywords, R), g.exports.PARAMS_CONTAINS.push(F);
      const D = g.contains.find((U) => U.scope === "attr"),
        B = Object.assign({}, D, { match: l.concat(f, l.lookahead(/\s*\?:/)) });
      g.exports.PARAMS_CONTAINS.push([g.exports.CLASS_REFERENCE, D, B]),
        (g.contains = g.contains.concat([F, y, N, B])),
        x(g, "shebang", a.SHEBANG()),
        x(g, "use_strict", T);
      const M = g.contains.find((U) => U.label === "func.def");
      return (M.relevance = 0), Object.assign(g, { name: "TypeScript", aliases: ["ts", "tsx", "mts", "cts"] }), g;
    }
    return (Un = s), Un;
  }
  var Pn, Bt;
  function Mr() {
    if (Bt) return Pn;
    Bt = 1;
    function r(e) {
      const t = e.regex,
        o = { className: "string", begin: /"(""|[^/n])"C\b/ },
        d = { className: "string", begin: /"/, end: /"/, illegal: /\n/, contains: [{ begin: /""/ }] },
        c = /\d{1,2}\/\d{1,2}\/\d{4}/,
        b = /\d{4}-\d{1,2}-\d{1,2}/,
        _ = /(\d|1[012])(:\d+){0,2} *(AM|PM)/,
        p = /\d{1,2}(:\d{1,2}){1,2}/,
        s = {
          className: "literal",
          variants: [
            { begin: t.concat(/# */, t.either(b, c), / *#/) },
            { begin: t.concat(/# */, p, / *#/) },
            { begin: t.concat(/# */, _, / *#/) },
            { begin: t.concat(/# */, t.either(b, c), / +/, t.either(_, p), / *#/) },
          ],
        },
        a = {
          className: "number",
          relevance: 0,
          variants: [
            { begin: /\b\d[\d_]*((\.[\d_]+(E[+-]?[\d_]+)?)|(E[+-]?[\d_]+))[RFD@!#]?/ },
            { begin: /\b\d[\d_]*((U?[SIL])|[%&])?/ },
            { begin: /&H[\dA-F_]+((U?[SIL])|[%&])?/ },
            { begin: /&O[0-7_]+((U?[SIL])|[%&])?/ },
            { begin: /&B[01_]+((U?[SIL])|[%&])?/ },
          ],
        },
        l = { className: "label", begin: /^\w+:/ },
        g = e.COMMENT(/'''/, /$/, { contains: [{ className: "doctag", begin: /<\/?/, end: />/ }] }),
        f = e.COMMENT(null, /$/, { variants: [{ begin: /'/ }, { begin: /([\t ]|^)REM(?=\s)/ }] });
      return {
        name: "Visual Basic .NET",
        aliases: ["vb"],
        case_insensitive: !0,
        classNameAliases: { label: "symbol" },
        keywords: {
          keyword:
            "addhandler alias aggregate ansi as async assembly auto binary by byref byval call case catch class compare const continue custom declare default delegate dim distinct do each equals else elseif end enum erase error event exit explicit finally for friend from function get global goto group handles if implements imports in inherits interface into iterator join key let lib loop me mid module mustinherit mustoverride mybase myclass namespace narrowing new next notinheritable notoverridable of off on operator option optional order overloads overridable overrides paramarray partial preserve private property protected public raiseevent readonly redim removehandler resume return select set shadows shared skip static step stop structure strict sub synclock take text then throw to try unicode until using when where while widening with withevents writeonly yield",
          built_in:
            "addressof and andalso await directcast gettype getxmlnamespace is isfalse isnot istrue like mod nameof new not or orelse trycast typeof xor cbool cbyte cchar cdate cdbl cdec cint clng cobj csbyte cshort csng cstr cuint culng cushort",
          type: "boolean byte char date decimal double integer long object sbyte short single string uinteger ulong ushort",
          literal: "true false nothing",
        },
        illegal: "//|\\{|\\}|endif|gosub|variant|wend|^\\$ ",
        contains: [
          o,
          d,
          s,
          a,
          l,
          g,
          f,
          {
            className: "meta",
            begin: /[\t ]*#(const|disable|else|elseif|enable|end|externalsource|if|region)\b/,
            end: /$/,
            keywords: { keyword: "const disable else elseif enable end externalsource if region then" },
            contains: [f],
          },
        ],
      };
    }
    return (Pn = r), Pn;
  }
  var Fn, Ut;
  function kr() {
    if (Ut) return Fn;
    Ut = 1;
    function r(e) {
      e.regex;
      const t = e.COMMENT(/\(;/, /;\)/);
      t.contains.push("self");
      const o = e.COMMENT(/;;/, /$/),
        d = [
          "anyfunc",
          "block",
          "br",
          "br_if",
          "br_table",
          "call",
          "call_indirect",
          "data",
          "drop",
          "elem",
          "else",
          "end",
          "export",
          "func",
          "global.get",
          "global.set",
          "local.get",
          "local.set",
          "local.tee",
          "get_global",
          "get_local",
          "global",
          "if",
          "import",
          "local",
          "loop",
          "memory",
          "memory.grow",
          "memory.size",
          "module",
          "mut",
          "nop",
          "offset",
          "param",
          "result",
          "return",
          "select",
          "set_global",
          "set_local",
          "start",
          "table",
          "tee_local",
          "then",
          "type",
          "unreachable",
        ],
        c = {
          begin: [/(?:func|call|call_indirect)/, /\s+/, /\$[^\s)]+/],
          className: { 1: "keyword", 3: "title.function" },
        },
        b = { className: "variable", begin: /\$[\w_]+/ },
        _ = { match: /(\((?!;)|\))+/, className: "punctuation", relevance: 0 },
        p = {
          className: "number",
          relevance: 0,
          match:
            /[+-]?\b(?:\d(?:_?\d)*(?:\.\d(?:_?\d)*)?(?:[eE][+-]?\d(?:_?\d)*)?|0x[\da-fA-F](?:_?[\da-fA-F])*(?:\.[\da-fA-F](?:_?[\da-fA-D])*)?(?:[pP][+-]?\d(?:_?\d)*)?)\b|\binf\b|\bnan(?::0x[\da-fA-F](?:_?[\da-fA-D])*)?\b/,
        },
        s = { match: /(i32|i64|f32|f64)(?!\.)/, className: "type" },
        a = {
          className: "keyword",
          match:
            /\b(f32|f64|i32|i64)(?:\.(?:abs|add|and|ceil|clz|const|convert_[su]\/i(?:32|64)|copysign|ctz|demote\/f64|div(?:_[su])?|eqz?|extend_[su]\/i32|floor|ge(?:_[su])?|gt(?:_[su])?|le(?:_[su])?|load(?:(?:8|16|32)_[su])?|lt(?:_[su])?|max|min|mul|nearest|neg?|or|popcnt|promote\/f32|reinterpret\/[fi](?:32|64)|rem_[su]|rot[lr]|shl|shr_[su]|store(?:8|16|32)?|sqrt|sub|trunc(?:_[su]\/f(?:32|64))?|wrap\/i64|xor))\b/,
        };
      return {
        name: "WebAssembly",
        keywords: { $pattern: /[\w.]+/, keyword: d },
        contains: [
          o,
          t,
          { match: [/(?:offset|align)/, /\s*/, /=/], className: { 1: "keyword", 3: "operator" } },
          b,
          _,
          c,
          e.QUOTE_STRING_MODE,
          s,
          a,
          p,
        ],
      };
    }
    return (Fn = r), Fn;
  }
  var zn, Pt;
  function Cr() {
    if (Pt) return zn;
    Pt = 1;
    var r = Zt();
    return (
      r.registerLanguage("xml", Xt()),
      r.registerLanguage("bash", Vt()),
      r.registerLanguage("c", Qt()),
      r.registerLanguage("cpp", Jt()),
      r.registerLanguage("csharp", jt()),
      r.registerLanguage("css", er()),
      r.registerLanguage("markdown", nr()),
      r.registerLanguage("diff", tr()),
      r.registerLanguage("ruby", rr()),
      r.registerLanguage("go", ar()),
      r.registerLanguage("graphql", ir()),
      r.registerLanguage("ini", sr()),
      r.registerLanguage("java", or()),
      r.registerLanguage("javascript", cr()),
      r.registerLanguage("json", lr()),
      r.registerLanguage("kotlin", dr()),
      r.registerLanguage("less", ur()),
      r.registerLanguage("lua", gr()),
      r.registerLanguage("makefile", br()),
      r.registerLanguage("perl", pr()),
      r.registerLanguage("objectivec", mr()),
      r.registerLanguage("php", _r()),
      r.registerLanguage("php-template", fr()),
      r.registerLanguage("plaintext", Er()),
      r.registerLanguage("python", hr()),
      r.registerLanguage("python-repl", Nr()),
      r.registerLanguage("r", yr()),
      r.registerLanguage("rust", vr()),
      r.registerLanguage("scss", Tr()),
      r.registerLanguage("shell", Sr()),
      r.registerLanguage("sql", wr()),
      r.registerLanguage("swift", Or()),
      r.registerLanguage("yaml", Ar()),
      r.registerLanguage("typescript", Rr()),
      r.registerLanguage("vbnet", Mr()),
      r.registerLanguage("wasm", kr()),
      (r.HighlightJS = r),
      (r.default = r),
      (zn = r),
      zn
    );
  }
  var Ir = Cr(),
    xr = Yt(Ir);
  self.onmessage = (r) => {
    const { code: e, language: t } = r.data;
    let o = "";
    try {
      o = xr.highlight(e, { language: t }).value;
    } catch (d) {
      o = e;
    }
    self.postMessage({ highlighted: o });
  };
})();
