import axios from "axios";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
/**
 *
node script/websiteMap/index.js china test
node script/websiteMap/index.js china prod
node script/websiteMap/index.js us test
node script/websiteMap/index.js us prod
 *
 */
// 获取 __dirname
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 从命令行获取 type 和 env 参数
const type = process.argv[2];
const env = process.argv[3];

if (!["china", "us"].includes(type)) {
  console.error("❌ 请传入正确的 type 参数: china 或 us");
  process.exit(1);
}

if (!["test", "prod"].includes(env)) {
  console.error("❌ 请传入正确的环境参数: test 或 prod");
  process.exit(1);
}

// 域名映射表（只存域名，不包含路径）
const domainMap = {
  china: {
    test: "https://api-cn-test.tiangong.cn",
    prod: "https://api-cn.tiangong.cn",
  },
  us: {
    test: "https://api-test.skywork.ai",
    prod: "https://api.skywork.ai", // 假设正式地址
  },
};

// 统一的接口路径
const apiPath = "/creation/share/query_search_list";

// 拼接完整请求地址
const apiUrl = domainMap[type][env] + apiPath;

const filename = `tg-share-${type}-${env}-sitemap.xml`;

// 请求接口分页获取全部数据
async function fetchAllData() {
  const pageSize = 1000;
  let pageIndex = 1;
  let total = 0;
  let allList = [];
  console.log(`开始获取`, apiUrl, pageIndex, pageSize);
  do {
    try {
      const response = await axios.post(
        apiUrl,
        {
          page_index: pageIndex,
          page_size: pageSize,
        },
        {
          headers: {
            Accept: "*/*",
            "Accept-Encoding": "gzip, deflate, br",
            Connection: "keep-alive",
            "Content-Type": "application/json",
            "User-Agent": "PostmanRuntime-ApipostRuntime/1.1.0",
          },
        }
      );
      // console.log(`response`,response);
      const resData = response.data;
      if (resData.code !== 0) {
        throw new Error(`接口返回错误: ${resData.message}`);
      }

      const data = resData.data;
      total = data.total;
      allList = allList.concat(data.list);

      pageIndex++;

      if (allList.length >= total) break;
    } catch (err) {
      console.error("请求出错:", err.message);
      break;
    }
  } while (allList.length < total);

  return allList;
}

// 生成 sitemap XML 内容
const generateSitemapXml = (pages) => {
  const header =
    `<?xml version="1.0" encoding="UTF-8"?>\n` +
    `<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
             xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9
             http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd">\n`;

  // const body = pages
  //   .map((page) => {
  //     return `  <url>
  //   <loc>${page.loc}</loc>
  //   <lastmod>${page.lastmod}</lastmod>
  //   <changefreq>${page.changefreq}</changefreq>
  //   <priority>${page.priority}</priority>
  // </url>`;
  //   })
  //   .join("\n");

  const body = pages
    .map((page) => {
      return `  <url>
  <loc>${page.loc}</loc>
  <lastmod>${page.lastmod}</lastmod>
</url>`;
    })
    .join("\n");

  const footer = `\n</urlset>`;

  return header + body + footer;
};
function escapeXml(str) {
  return str
    .replace(/&(?!amp;|lt;|gt;|quot;|apos;|#\d+;|#x[a-fA-F0-9]+;)/g, "&amp;")
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;")
    .replace(/"/g, "&quot;")
    .replace(/'/g, "&apos;");
}
// 主程序入口
(async () => {
  console.log(`开始获取数据 type=${type} 环境=${env}，请求接口：${apiUrl}`);

  const allList = await fetchAllData();

  const pages = allList.map((item) => {
    let link = item.link + "&st=webSearching";
    return {
      loc: escapeXml(link),
      lastmod: item.updated,
    };
  });

  const sitemapXml = generateSitemapXml(pages);

  const outputDir = path.join(__dirname, "../../public/public");
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }

  fs.writeFileSync(path.join(outputDir, filename), sitemapXml, "utf8");
  console.log(`✅ ${filename} 已生成，包含 ${pages.length} 条数据`);
})();
