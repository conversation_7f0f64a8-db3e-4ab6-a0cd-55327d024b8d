import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

// 处理路径
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const targetDir = path.resolve(__dirname, "../src/views");

// 获取功能名称
const moduleName = process.argv[2];
if (!moduleName) {
  console.error("请提供要创建的文件夹名称！");
  process.exit(1);
}

// 功能路径
const viewPath = path.join(targetDir, moduleName);

// 初始化内容
const vueTemplate = `
<template>
  <div class="${moduleName}">
    <!-- 页面内容 -->
  </div>
</template>

<script lang="ts" setup name="${moduleName}"></script>

<style lang="scss" scoped></style>

<style scoped lang="scss">
@use "./index.mobile.scss";
</style>
`;

const scssTemplate = `
  // @media (--phone) 需要在 @media (--pad) 之后，否则@media (--phone)内样式权重不够，无法生效
  @media (--pad) {
    /* pad屏幕宽度样式 */
  }
  @media (--phone) {
    /* phone屏幕宽度样式 */
  }
`;

// 创建文件夹和文件
if (!fs.existsSync(viewPath)) {
  fs.mkdirSync(viewPath, { recursive: true });
} else {
  console.error(`src/views/${moduleName} 文件夹已存在！！！请尝试更换名称`);
  process.exit(1);
}

fs.writeFileSync(path.join(viewPath, "index.vue"), vueTemplate);
fs.writeFileSync(path.join(viewPath, "index.mobile.scss"), scssTemplate);

console.log(`src/views/${moduleName} 文件夹创建完成~`);
