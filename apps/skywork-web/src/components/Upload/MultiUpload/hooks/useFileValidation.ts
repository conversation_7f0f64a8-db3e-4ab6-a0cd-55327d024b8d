import { MAX_FILES_LIMIT, UPLOAD_ERROR_MSG } from "@/components/Upload/const";
import type { ResourceType, WaitFileInfo } from "@/components/Upload/types";
import { validateAllFiles } from "@/components/Upload/utils";
import { DocMessage } from "@tg-fe/ui";
import { type Ref } from "vue";

/**
 * 文件验证和添加 Hook
 * 负责文件的验证、类型判断和添加到待上传列表
 */
export function useFileValidation() {
  /**
   * 添加文件到待上传列表
   * @param files 待上传文件列表
   * @param totalFileNum 当前总文件数量
   * @param curWaitFileNum 当前待上传文件数量
   * @param waitUploadFileList 待上传文件列表引用
   */
  const addFilesToWaitUpload = (
    files: WaitFileInfo[],
    totalFileNum: number,
    curWaitFileNum: number,
    waitUploadFileList: Ref<WaitFileInfo[]>
  ) => {
    // 数量限制校验
    if (totalFileNum + curWaitFileNum + files?.length > MAX_FILES_LIMIT) {
      DocMessage.error(UPLOAD_ERROR_MSG.OVER_COUNT());
      return false;
    }

    // 文件格式和大小校验
    const { valid, errorFiles, successFiles } = validateAllFiles(files);
    if (!valid) {
      DocMessage({ message: errorFiles[0].message, type: "error", duration: 5000 });
    }

    // 添加到待上传列表，并设置资源类型
    waitUploadFileList.value.push(
      ...successFiles.map((item) => {
        let { file, info, ...rest } = item;
        let resource_type: ResourceType = 1;

        if (file) {
          resource_type = 1; // 实体文件（本地上传、粘贴text）
        } else if (info.file_type === "website_url" || info.file_type === "youtube_url") {
          resource_type = 2; // 网络资源
        } else {
          resource_type = 3; // 网盘文件
        }

        return {
          file,
          info: {
            ...info,
            resource_type,
          },
          ...rest,
        };
      })
    );

    return valid;
  };

  /**
   * 验证上传参数
   * @param checkedFileList 选中的文件列表
   * @param params 上传参数
   */
  const validateUploadParams = (
    checkedFileList: WaitFileInfo[],
    params?: {
      upload_from?: "home" | "chat_left" | "chat_sheet";
      currentCount?: number;
    }
  ) => {
    // 未选择文件
    if (checkedFileList.length === 0) {
      return { valid: false, error: "No files selected" };
    }

    const { currentCount, upload_from } = params || {};

    // chat_sheet 场景下的特殊限制
    if (upload_from === "chat_sheet" && checkedFileList.length > 5) {
      const error = UPLOAD_ERROR_MSG.OVER_EXCEL_COUNT();
      DocMessage.error(error);
      return { valid: false, error };
    }

    // 最大上传数量限制
    if (currentCount && currentCount + checkedFileList.length > MAX_FILES_LIMIT) {
      const error = UPLOAD_ERROR_MSG.OVER_COUNT();
      DocMessage.error(error);
      return { valid: false, error };
    }

    // 文件格式和大小校验
    const { valid, errorFiles } = validateAllFiles(checkedFileList);
    if (!valid) {
      const error = errorFiles[0].message;
      DocMessage({ message: error, type: "error", duration: 5000 });
      return { valid: false, error };
    }

    return { valid: true, error: null };
  };

  return {
    addFilesToWaitUpload,
    validateUploadParams,
  };
}
