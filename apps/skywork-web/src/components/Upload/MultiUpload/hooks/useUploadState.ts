import type { ProgressFileInfo, WaitFileInfo } from "@/components/Upload/types";
import { computed, ref } from "vue";

/**
 * 上传状态管理 Hook
 * 负责管理上传相关的状态变量
 */
export function useUploadState() {
  // 弹框状态
  const textLinkDialogVisible = ref(false); // 文本、链接弹框是否显示
  const spaceDialogVisible = ref(false); // 知识库选择弹框是否显示

  // 文件列表状态
  const waitUploadFileList = ref<WaitFileInfo[]>([]); // 待上传文件列表
  const uploadFileList = ref<ProgressFileInfo[]>([]); // 已提交上传的文件列表(包含上传中、上传成功、上传失败)

  // 上传状态
  const isUploading = ref(false); // 是否正在上传
  const totalFileNum = ref(0); // 当前上传文件数量

  // 计算属性
  const checkedWaitUploadFileList = computed(() => waitUploadFileList.value.filter((item) => item.selected)); // 选中的待上传文件列表

  const curWaitFileNum = computed(() => waitUploadFileList.value.length); // 当前选中的文件数量

  // 状态设置方法
  const setTextLinkDialogVisible = (visible: boolean) => {
    textLinkDialogVisible.value = visible;
  };

  const setSpaceDialogVisible = (visible: boolean) => {
    spaceDialogVisible.value = visible;
  };

  const setTotalFileNum = (num: number) => {
    totalFileNum.value = num;
  };

  const setIsUploading = (uploading: boolean) => {
    isUploading.value = uploading;
  };

  // 列表操作方法
  const clearWaitUploadList = () => {
    waitUploadFileList.value = [];
  };

  const clearUploadList = () => {
    uploadFileList.value = [];
  };

  const addFilesToUploadFileList = (files: ProgressFileInfo[]) => {
    uploadFileList.value.unshift(...files);
  };

  const updateUploadFileListByOne = (file: Partial<ProgressFileInfo> & { file_id: string }) => {
    const index = uploadFileList.value.findIndex((item) => item.file_id === file.file_id);
    if (index !== -1) {
      uploadFileList.value[index] = {
        ...uploadFileList.value[index],
        ...file,
      };
    }
  };

  const deleteUploadFileListById = (file_id: string) => {
    const index = uploadFileList.value.findIndex((item) => item.file_id === file_id);
    if (index !== -1) {
      uploadFileList.value.splice(index, 1);
    }
  };

  return {
    // 状态
    textLinkDialogVisible,
    spaceDialogVisible,
    waitUploadFileList,
    uploadFileList,
    isUploading,
    totalFileNum,
    checkedWaitUploadFileList,
    curWaitFileNum,

    // 方法
    setTextLinkDialogVisible,
    setSpaceDialogVisible,
    setTotalFileNum,
    setIsUploading,
    clearWaitUploadList,
    clearUploadList,
    addFilesToUploadFileList,
    updateUploadFileListByOne,
    deleteUploadFileListById,
  };
}
