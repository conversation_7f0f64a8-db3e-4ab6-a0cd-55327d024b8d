import { apiAnalysis, apiAnalysisSimple } from "@/api/project";

/**
 * 文件分析 Hook
 * 负责处理文件分析相关的逻辑
 */
export function useFileAnalysis() {
  
  /**
   * 开始文件分析
   * @param file_ids 文件ID列表
   * @returns 分析结果列表
   */
  const startAnalysis = async (file_ids: string[]) => {
    const analysisRes = await apiAnalysis(file_ids);
    return analysisRes.data?.analysis_result_list || [];
  };

  /**
   * 简单文件分析
   * @param data 分析数据
   * @returns 分析结果
   */
  const analysisSimple = async (data: any) => {
    const analysisRes = await apiAnalysisSimple(data);
    return analysisRes;
  };

  return {
    startAnalysis,
    analysisSimple,
  };
}
