import { apiBeforeUpload } from "@/api/project";
import { DEFAULT_ACCEPT_FILE_TYPES, MAX_FILES_LIMIT } from "@/components/Upload/const";
import type { ApiBeforeUploadFile, ProgressFileInfo, WaitFileInfo } from "@/components/Upload/types";
import { limitAsyncConcurrency } from "@/components/Upload/utils";
import { disconnectFallback } from "@/utils/disconnect";
import { AnalysisStatus } from "@/views/projectV2/type";
import { defineStore } from "pinia";

// 导入拆分的 hooks
import { useFileAnalysis } from "./useFileAnalysis";
import { useFileValidation } from "./useFileValidation";
import { useOssUpload } from "./useOssUpload";
import { useUploadProgress } from "./useUploadProgress";
import { useUploadState } from "./useUploadState";
import { useUploadTracking } from "./useUploadTracking";

export { DEFAULT_ACCEPT_FILE_TYPES, MAX_FILES_LIMIT };
export type { WaitFileInfo };

export const useUploadStore = (moduleName?: string) => {
  return defineStore(`upload:${moduleName || "common"}`, () => {
    // 使用拆分的 hooks
    const uploadState = useUploadState();
    const fileValidation = useFileValidation();
    const ossUpload = useOssUpload();
    const fileAnalysis = useFileAnalysis();
    const uploadProgress = useUploadProgress();
    const uploadTracking = useUploadTracking();

    /**
     * 添加文件到待上传列表
     * @param files 待上传文件列表
     */
    const addFilesToWaitUpload = (files: WaitFileInfo[]) => {
      return fileValidation.addFilesToWaitUpload(
        files,
        uploadState.totalFileNum.value,
        uploadState.curWaitFileNum.value,
        uploadState.waitUploadFileList
      );
    };

    /**
     * 上传所有（已选中的待上传列表）文件
     * @param params 上传参数
     * @returns 上传结果列表
     */
    const uploadFilesInner = async (params?: {
      project_id?: string;
      space_id?: string;
      currentCount?: number;
      async?: boolean;
      upload_from?: "home" | "chat_left" | "chat_sheet";
      asyncSuccess?: (fileList: ProgressFileInfo[]) => void;
    }): Promise<ProgressFileInfo[]> => {
      const { currentCount, async, upload_from, asyncSuccess, ...rest } = params || {};

      // 校验上传参数
      const validation = fileValidation.validateUploadParams(uploadState.checkedWaitUploadFileList.value, {
        currentCount,
        upload_from,
      });

      if (!validation.valid) {
        throw new Error(validation.error || "Validation failed");
      }

      // 设置上传状态
      uploadState.setIsUploading(true);

      // 准备上传文件列表
      const beforeUploadFiles: ApiBeforeUploadFile[] = uploadState.checkedWaitUploadFileList.value.map((item) => ({
        ...item.info,
        resource_type: (item.info as any).resource_type || 1, // 默认为本地文件
      }));

      // 调用上传前接口
      const beforeUploadRes = await apiBeforeUpload({
        file_list: beforeUploadFiles,
        ...rest,
      });

      // 创建上传任务
      const uploadTasks = uploadState.checkedWaitUploadFileList.value.map((fileItem, index) => async () => {
        const result = beforeUploadRes.data.upload_result_list[index];
        const startTime = Date.now();

        try {
          let uploadResult: ProgressFileInfo;

          if (fileItem.file) {
            // 实体文件上传到OSS
            uploadResult = await ossUpload.uploadToOss(
              {
                token: result.token,
                fileItem,
                result,
              },
              fileAnalysis.startAnalysis
            );
          } else {
            // 非实体文件上传
            uploadResult = await ossUpload.handleNonPhysicalFileUpload(
              { result, fileItem },
              fileAnalysis.startAnalysis
            );
          }

          // 上传成功埋点
          uploadTracking.trackUploadResult(
            { ...uploadResult, startTime, state: 1 },
            { upload_from, file_from: (fileItem.trackData as any)?.file_from }
          );

          return uploadResult;
        } catch (error) {
          // 上传失败埋点
          uploadTracking.trackUploadResult(
            {
              file_id: result.file_id,
              file_name: result.file_name,
              file_size: fileItem.info.file_size,
              file_type: fileItem.info.file_type as any,
              startTime,
              state: 0,
            },
            { upload_from, file_from: (fileItem.trackData as any)?.file_from }
          );

          return {
            file_id: result.file_id,
            file_name: result.file_name,
            file_type: fileItem.info.file_type as any,
            file_size: fileItem.info.file_size,
            file_url: "",
            preview_url: "",
            progress: 0,
            knowledge_file_id: "",
            status: AnalysisStatus.UPLOAD_FAIL,
          };
        }
      });

      // 并发上传
      const uploadResults = await limitAsyncConcurrency(uploadTasks, 3);

      // 清理状态
      uploadState.clearWaitUploadList();
      uploadState.setIsUploading(false);

      // 处理异步上传
      if (async) {
        uploadState.addFilesToUploadFileList(uploadResults);
        uploadProgress.pollingUploadFileListProgress(uploadState.uploadFileList, asyncSuccess);
      } else {
        // 广告转化埋点
        uploadTracking.trackUploadConversion(uploadResults);
      }

      return uploadResults;
    };

    // 轮询上传文件列表进度
    const pollingUploadFileListProgress = (success?: (fileList: ProgressFileInfo[]) => void) => {
      uploadProgress.pollingUploadFileListProgress(uploadState.uploadFileList, success);
    };

    // 返回所有状态和方法
    return {
      // 状态
      ...uploadState,

      // 方法
      addFilesToWaitUpload,
      uploadFilesInner,
      pollingUploadFileListProgress,

      // 断线重连处理
      disconnectFallback: () => disconnectFallback(uploadFilesInner),
    };
  })();
};
