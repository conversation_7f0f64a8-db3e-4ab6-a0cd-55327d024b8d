import { apiBeforeUpload } from "@/api/project";
import { DEFAULT_ACCEPT_FILE_TYPES, MAX_FILES_LIMIT } from "@/components/Upload/const";
import type { ApiBeforeUploadFile, ProgressFileInfo, WaitFileInfo } from "@/components/Upload/types";
import { limitAsyncConcurrency } from "@/components/Upload/utils";
import { $t } from "@/locales";
import { disconnectFallback } from "@/utils/disconnect";
import { AnalysisStatus } from "@/views/projectV2/type";
import { DocMessage } from "@tg-fe/ui";
import { defineStore } from "pinia";

// 导入拆分的 hooks
import { useFileAnalysis } from "./useFileAnalysis";
import { useFileValidation } from "./useFileValidation";
import { useOssUpload } from "./useOssUpload";
import { useUploadProgress } from "./useUploadProgress";
import { useUploadState } from "./useUploadState";
import { useUploadTracking } from "./useUploadTracking";

export { DEFAULT_ACCEPT_FILE_TYPES, MAX_FILES_LIMIT };
export type { WaitFileInfo };

export const useUploadStore = (moduleName?: string) => {
  return defineStore(`upload:${moduleName || "common"}`, () => {
    // 使用拆分的 hooks
    const uploadState = useUploadState();
    const fileValidation = useFileValidation();
    const ossUpload = useOssUpload();
    const fileAnalysis = useFileAnalysis();
    const uploadProgress = useUploadProgress();
    const uploadTracking = useUploadTracking();

    /**
     * 添加文件到待上传列表
     * @param files 待上传文件列表
     */
    const addFilesToWaitUpload = (files: WaitFileInfo[]) => {
      return fileValidation.addFilesToWaitUpload(
        files,
        uploadState.totalFileNum.value,
        uploadState.curWaitFileNum.value,
        uploadState.waitUploadFileList
      );
    };

    /**
     * 上传所有（已选中的待上传列表）文件
     * @param params 上传参数
     * @returns 上传结果列表
     */
    const uploadFilesInner = async (params?: {
      project_id?: string;
      upload_from?: "home" | "chat_left" | "chat_sheet";
      space_id?: string; // 知识库id
      page_source?: number; // 业务来源
      currentCount?: number; // 当前已上传文件数量，用于校验最大上传数量限制
      async?: boolean; // 是否异步上传
      asyncSuccess?: (fileList: ProgressFileInfo[]) => void; // 异步上传完成回调
    }): Promise<ProgressFileInfo[]> => {
      // 0. 校验
      if (uploadState.checkedWaitUploadFileList.value.length === 0) {
        // 0.1 未选择文件
        return [];
      }
      const { currentCount, async, upload_from, asyncSuccess, ...rest } = params || {};

      // 校验上传参数
      const validation = fileValidation.validateUploadParams(uploadState.checkedWaitUploadFileList.value, {
        currentCount,
        upload_from,
      });

      if (!validation.valid) {
        throw new Error(validation.error || "Validation failed");
      }

      try {
        // 1. 获取oss_url及流水号
        const uploadData = {
          ...(rest || {}),
          file_list: uploadState.checkedWaitUploadFileList.value.map((item) => item.info as ApiBeforeUploadFile),
        };
        uploadState.setIsUploading(true);
        const res = await apiBeforeUpload(uploadData);
        const { upload_result_list = [] } = res.data;
        const successResultList = upload_result_list.filter((item) => item.is_success);

        if (successResultList.length < upload_result_list.length) {
          DocMessage("Some files failed to upload.");
        }

        // [hook] 添加文件到上传列表
        uploadState.addFilesToUploadFileList(
          successResultList.map((result) => {
            const fileItem = uploadState.checkedWaitUploadFileList.value.find(
              (item) => item.info.id === result.id
            ) as WaitFileInfo;
            return {
              file_id: result.file_id,
              file_name: fileItem?.info?.file_name,
              file_type: fileItem?.info?.file_type as any,
              file_size: fileItem?.info?.file_size,
              status: AnalysisStatus.UPLOADING,
              file_url: "",
              preview_url: "",
              knowledge_file_id: "",
              progress: 0,
            };
          }) as ProgressFileInfo[]
        );

        // 2. 遍历所有文件，创建上传到oss的异步任务
        const uploadTasks: (() => Promise<ProgressFileInfo>)[] = successResultList.map((result) => {
          const fileItem = uploadState.checkedWaitUploadFileList.value.find(
            (item) => item.info.id === result.id
          ) as WaitFileInfo;
          return async () => {
            let uploadRes: ProgressFileInfo = {} as ProgressFileInfo;
            const startTime = Date.now();
            try {
              // 实体文件上传（本地文件、粘贴text）
              if (fileItem?.file) {
                if (!result.token) {
                  throw new Error("oss token is empty.");
                }
                uploadRes = await ossUpload.uploadToOss(
                  { token: result.token, fileItem, result: result },
                  fileAnalysis.startAnalysis
                );
              } else {
                // 非实体文件（网盘文件、website_url、youtube_url）上传成功，开始解析
                uploadRes = await ossUpload.handleNonPhysicalFileUpload(
                  { result, fileItem },
                  fileAnalysis.startAnalysis
                );
              }
            } catch (e) {
              uploadState.updateUploadFileListByOne({
                file_id: result.file_id,
                status: AnalysisStatus.UPLOAD_FAIL,
              });
              uploadTracking.trackUploadResult({ ...result, startTime, state: 0 } as any, fileItem.trackData);
              throw e;
            }

            uploadState.updateUploadFileListByOne(uploadRes);
            uploadTracking.trackUploadResult({ ...uploadRes, startTime, state: 1 } as any, fileItem.trackData);
            return uploadRes;
          };
        });

        // 3. 限制并发数量5个，上传所有文件
        if (async) {
          // 异步上传
          limitAsyncConcurrency<ProgressFileInfo>(uploadTasks, 5);
          // 保证只有一个轮询任务执行
          if (!uploadProgress.getPollingStatus()) {
            // 开始轮询上传文件列表进度
            uploadProgress.pollingUploadFileListProgress(uploadState.uploadFileList, asyncSuccess);
          }
          uploadState.clearWaitUploadList();
          uploadState.setIsUploading(false);
        } else {
          // 同步上传
          await limitAsyncConcurrency<ProgressFileInfo>(uploadTasks, 5);
          uploadState.clearWaitUploadList();
          uploadState.setIsUploading(false);
          setTimeout(() => {
            // 清空上传列表
            uploadState.clearUploadList();
          }, 500);
          // 上报广告转化配置埋点
          if (uploadState.uploadFileList.value.length) {
            uploadTracking.trackUploadConversion(uploadState.uploadFileList.value);
          }
        }
        return uploadState.uploadFileList.value;
      } catch (e: any) {
        uploadState.setIsUploading(false);
        DocMessage.error(e?.response?.data?.msg || e.message || $t("space.uploadFailed"));
        throw e;
      }
    };

    // 开始解析
    const analysisSimple = async (data: any) => {
      return await fileAnalysis.analysisSimple(data);
    };

    // 开始分析
    const startAnalysis = async (file_ids: string[]) => {
      return await fileAnalysis.startAnalysis(file_ids);
    };

    // 断线重连包装的上传方法
    const uploadFiles = disconnectFallback(uploadFilesInner) as typeof uploadFilesInner;

    // 返回所有状态和方法
    return {
      // 状态
      waitUploadFileList: uploadState.waitUploadFileList,
      checkedWaitUploadFileList: uploadState.checkedWaitUploadFileList,
      uploadFileList: uploadState.uploadFileList,
      totalFileNum: uploadState.totalFileNum,
      curWaitFileNum: uploadState.curWaitFileNum,
      textLinkDialogVisible: uploadState.textLinkDialogVisible,
      spaceDialogVisible: uploadState.spaceDialogVisible,
      isUploading: uploadState.isUploading,

      // 方法
      setTotalFileNum: uploadState.setTotalFileNum,
      setTextLinkDialogVisible: uploadState.setTextLinkDialogVisible,
      setSpaceDialogVisible: uploadState.setSpaceDialogVisible,
      addFilesToWaitUpload,
      deleteUploadFileListById: uploadState.deleteUploadFileListById,
      clearWaitUploadList: uploadState.clearWaitUploadList,
      clearUploadList: uploadState.clearUploadList,
      uploadFiles,
      analysisSimple,
      startAnalysis,
    };
  })();
};
