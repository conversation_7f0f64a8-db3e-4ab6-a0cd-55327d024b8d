import { apiAnalysis, apiAnalysisSimple, apiBeforeUpload, apiGetFileInfo } from "@/api/project";
import { DEFAULT_ACCEPT_FILE_TYPES, MAX_FILES_LIMIT, UPLOAD_ERROR_MSG } from "@/components/Upload/const";
import type {
  ApiBeforeUploadFile,
  FileType,
  ProgressFileInfo,
  ResFileInfo,
  ResourceType,
  WaitFileInfo,
} from "@/components/Upload/types";
import { limitAsyncConcurrency, validateAllFiles } from "@/components/Upload/utils";
import { $t } from "@/locales";
import { upload_uploading_state } from "@/tracker/upload";
import { ad_track_all } from "@/utils/ad_track.ts";
import { disconnectFallback } from "@/utils/disconnect";
import { AnalysisStatus } from "@/views/projectV2/type";
import { DocMessage } from "@tg-fe/ui";
import { defineStore } from "pinia";

export { DEFAULT_ACCEPT_FILE_TYPES, MAX_FILES_LIMIT };
export type { WaitFileInfo };

export const useUploadStore = (moduleName?: string) => {
  return defineStore(`upload:${moduleName || "common"}`, () => {
    const textLinkDialogVisible = ref(false); // 文本、链接 弹框是否显示
    const spaceDialogVisible = ref(false); // 知识库选择 弹框是否显示
    const waitUploadFileList = ref<WaitFileInfo[]>([]); // 待上传文件列表
    const checkedWaitUploadFileList = computed(() => waitUploadFileList.value.filter((item) => item.selected)); // 选中的待上传文件列表
    const uploadFileList = ref<ProgressFileInfo[]>([]); // 已提交上传的文件列表(包含上传中、上传成功、上传失败)
    const isUploading = ref(false); // 是否正在上传
    const totalFileNum = ref(0); // 当前上传文件数量
    const curWaitFileNum = computed(() => waitUploadFileList.value.length); // 当前选中的文件数量

    const setTextLinkDialogVisible = (visible: boolean) => {
      textLinkDialogVisible.value = visible;
    };

    const setSpaceDialogVisible = (visible: boolean) => {
      spaceDialogVisible.value = visible;
    };

    const setTotalFileNum = (num: number) => {
      totalFileNum.value = num;
    };

    /**
     * 添加文件到待上传列表
     * @param files 待上传文件列表
     * @param files[].info 文件信息（所有文件都有）
     * @param files[].file 实体文件（本地上传、粘贴text），非实体文件（website_url、youtube_url、网盘）传null
     * @param files[].selected 是否选中
     * @param forceValid 是否强制校验
     */
    const addFilesToWaitUpload = (files: WaitFileInfo[]) => {
      if (totalFileNum.value + curWaitFileNum.value + files?.length > MAX_FILES_LIMIT) {
        DocMessage.error(UPLOAD_ERROR_MSG.OVER_COUNT());
        return;
      }
      const { valid, errorFiles, successFiles } = validateAllFiles(files);
      if (!valid) {
        DocMessage({ message: errorFiles[0].message, type: "error", duration: 5000 });
      }
      waitUploadFileList.value.push(
        ...successFiles.map((item) => {
          let { file, info, ...rest } = item;
          let resource_type: ResourceType = 1;
          if (file) {
            resource_type = 1;
          } else if (info.file_type === "website_url" || info.file_type === "youtube_url") {
            resource_type = 2;
          } else {
            resource_type = 3;
          }
          return {
            file,
            info: {
              ...info,
              resource_type,
            },
            ...rest,
          };
        })
      );

      return valid;
    };

    const clearWaitUploadList = () => {
      waitUploadFileList.value = [];
    };

    const clearUploadList = () => {
      uploadFileList.value = [];
    };

    // 根据file_no获取文件信息
    async function getFileInfoByFileNo(file_nos: string[]): Promise<ResFileInfo[]> {
      const res = await apiGetFileInfo({ file_no_list: file_nos, is_get_cdn_url: true });
      return file_nos.map((id) => res.data.file_record_map[id]);
    }

    // 开始解析
    const startAnalysis = async (file_ids: string[]) => {
      const analysisRes = await apiAnalysis(file_ids);
      return analysisRes.data?.analysis_result_list;
    };

    // 单个文件上传到oss，并返回文件信息
    async function uploadToOss(params: {
      token: string;
      fileItem: WaitFileInfo;
      result: { file_id: string; file_name: string; url: string; oss_object_name: string };
    }): Promise<ProgressFileInfo> {
      const { token, fileItem, result } = params;
      let tokenJson: {
        dir: string;
        host: string;
        policy: string;
        signature: string;
        x_oss_credential: string;
        x_oss_date: string;
        x_oss_signature_version: string;
        security_token?: string;
      };
      tokenJson = JSON.parse(token);

      const formData = new FormData();
      formData.append("success_action_status", "200");
      formData.append("policy", tokenJson.policy);
      formData.append("x-oss-signature", tokenJson.signature);
      formData.append("x-oss-signature-version", tokenJson.x_oss_signature_version);
      formData.append("x-oss-credential", tokenJson.x_oss_credential);
      formData.append("x-oss-date", tokenJson.x_oss_date);
      formData.append("key", `${tokenJson.dir}${/\/$/.test(tokenJson.dir) ? "" : "/"}${result.oss_object_name}`);
      formData.append("x-oss-security-token", tokenJson.security_token || "");
      formData.append("file", fileItem.file!); // file 必须为最后一个表单域

      const res = await fetch(tokenJson.host, {
        method: "POST",
        body: formData,
      });

      if (res.ok) {
        // 上传成功，开始解析（异步）
        const [analysisResult = {}] = await startAnalysis([result.file_id]);
        // 获取文件信息并返回
        const [fileInfo] = await getFileInfoByFileNo([result.file_id]);
        return {
          file_id: result.file_id,
          file_name: analysisResult.file_name, // file_name从解析结果中取
          file_type: fileInfo.file_type as FileType,
          file_size: fileInfo.file_size,
          file_url: fileInfo.file_cdn_url!,
          preview_url: ["pdf", "txt", "docx"].includes(fileInfo.file_type) ? fileInfo.file_cdn_url! : "",
          knowledge_file_id: analysisResult.knowledge_file_id,
          progress: 0,
          status: analysisResult?.is_success ? AnalysisStatus.UPLOAD_ANALYSIS : AnalysisStatus.UPLOAD_FAIL, // 上传成功，解析中
        };
      }
      throw new Error(fileItem.info.file_name + " upload failed.");
    }

    /**
     * 上传所有（已选中的待上传列表）文件
     * @param data.project_id 项目id
     * @param data.space_id 知识库id
     * @returns 上传结果列表
     */
    async function uploadFilesInner(params?: {
      project_id?: string;
      upload_from?: "home" | "chat_left" | "chat_sheet";
      space_id?: string; // 知识库id
      page_source?: number; // 业务来源
      currentCount?: number; // 当前已上传文件数量，用于校验最大上传数量限制
      async?: boolean; // 是否异步上传
      asyncSuccess?: (fileList: ProgressFileInfo[]) => void; // 异步上传完成回调
    }): Promise<ProgressFileInfo[]> {
      // 0. 校验
      if (checkedWaitUploadFileList.value.length === 0) {
        // 0.1 未选择文件
        return [];
      }
      const { currentCount, async, upload_from, asyncSuccess, ...rest } = params || {};

      if (upload_from === "chat_sheet" && checkedWaitUploadFileList.value.length > 5) {
        DocMessage.error(UPLOAD_ERROR_MSG.OVER_EXCEL_COUNT());
        throw new Error(UPLOAD_ERROR_MSG.OVER_EXCEL_COUNT());
      }
      if (currentCount && currentCount + checkedWaitUploadFileList.value.length > MAX_FILES_LIMIT) {
        // 0.3 校验最大上传数量限制
        DocMessage.error(UPLOAD_ERROR_MSG.OVER_COUNT());
        throw new Error(UPLOAD_ERROR_MSG.OVER_COUNT());
      }
      const { valid, errorFiles } = validateAllFiles(checkedWaitUploadFileList.value);
      if (!valid) {
        // 0.4 校验文件size、type
        DocMessage({ message: errorFiles[0].message, type: "error", duration: 5000 });
        throw new Error(errorFiles[0].message);
      }

      try {
        // 1. 获取oss_url及流水号
        const uploadData = {
          ...(rest || {}),
          file_list: checkedWaitUploadFileList.value.map((item) => item.info as ApiBeforeUploadFile),
        };
        isUploading.value = true;
        const res = await apiBeforeUpload(uploadData);
        const { upload_result_list = [] } = res.data;
        const successResultList = upload_result_list.filter((item) => item.is_success);

        if (successResultList.length < upload_result_list.length) {
          DocMessage("Some files failed to upload.");
        }

        // [hook] 添加文件到上传列表
        addFilesToUploadFileList(
          successResultList.map((result) => {
            const fileItem = checkedWaitUploadFileList.value.find((item) => item.info.id === result.id) as WaitFileInfo;
            return {
              file_id: result.file_id,
              file_name: fileItem?.info?.file_name,
              file_type: fileItem?.info?.file_type,
              file_size: fileItem?.info?.file_size,
              status: AnalysisStatus.UPLOADING,
              file_url: "",
              preview_url: "",
              knowledge_file_id: "",
              progress: 0,
            };
          }) as ProgressFileInfo[]
        );

        // 2. 遍历所有文件，创建上传到oss的异步任务
        const uploadTasks: (() => Promise<ProgressFileInfo>)[] = successResultList.map((result) => {
          const fileItem = checkedWaitUploadFileList.value.find((item) => item.info.id === result.id) as WaitFileInfo;
          return async () => {
            let uploadRes: ProgressFileInfo = {} as ProgressFileInfo;
            const startTime = Date.now();
            try {
              // 实体文件上传（本地文件、粘贴text）
              if (fileItem?.file) {
                if (!result.token) {
                  throw new Error("oss token is empty.");
                }
                uploadRes = await uploadToOss({ token: result.token, fileItem, result: result });
              } else {
                // 非实体文件（网盘文件、website_url、youtube_url）上传成功，开始解析
                const [analysisResult = {}] = await startAnalysis([result.file_id]);
                uploadRes = {
                  file_id: result.file_id,
                  file_name: analysisResult.file_name, // file_name从解析结果中取
                  file_type: fileItem?.info?.file_type as FileType,
                  file_size: fileItem?.info?.file_size,
                  file_url: "",
                  preview_url: "",
                  progress: 0,
                  knowledge_file_id: analysisResult.knowledge_file_id,
                  status: analysisResult?.is_success ? AnalysisStatus.UPLOAD_ANALYSIS : AnalysisStatus.UPLOAD_FAIL, // 上传成功，解析中
                };
              }
            } catch (e) {
              updateUploadFileListByOne({
                file_id: result.file_id,
                status: AnalysisStatus.UPLOAD_FAIL,
              });
              track({ ...result, startTime, state: 0 }, fileItem.trackData);
              throw e;
            }

            updateUploadFileListByOne(uploadRes);
            track({ ...uploadRes, startTime, state: 1 }, fileItem.trackData);
            return uploadRes;
          };
        });

        // 3. 限制并发数量5个，上传所有文件
        if (async) {
          // 异步上传
          limitAsyncConcurrency<ProgressFileInfo>(uploadTasks, 5);
          // 保证只有一个轮询任务执行
          if (!isPolling) {
            // 开始轮询上传文件列表进度
            pollingUploadFileListProgress(asyncSuccess);
          }
          clearWaitUploadList();
          isUploading.value = false;
        } else {
          // 同步上传
          await limitAsyncConcurrency<ProgressFileInfo>(uploadTasks, 5);
          clearWaitUploadList();
          isUploading.value = false;
          setTimeout(() => {
            // 清空上传列表
            clearUploadList();
          }, 500);
          // 上报广告转化配置埋点
          if (uploadFileList.value.length) {
            ad_track_all("Upload_files");
          }
          // 返回上传结果
        }
        return uploadFileList.value;
      } catch (e: any) {
        isUploading.value = false;
        DocMessage.error(e?.response?.data?.msg || e.message || $t("space.uploadFailed"));
        throw e;
      }
    }

    const uploadFiles = disconnectFallback(uploadFilesInner) as typeof uploadFilesInner;

    // 开始解析
    const analysisSimple = async (data) => {
      const analysisRes = await apiAnalysisSimple(data);
      return analysisRes;
    };

    // 添加文件到上传列表
    const addFilesToUploadFileList = async (files: ProgressFileInfo[]) => {
      uploadFileList.value.unshift(...(files as ProgressFileInfo[]));
    };

    // 更新上传文件列表中的某一项
    const updateUploadFileListByOne = async (file: Partial<ProgressFileInfo> & { file_id: string }) => {
      const index = uploadFileList.value.findIndex((item) => item.file_id === file.file_id);
      if (index !== -1) {
        uploadFileList.value[index] = {
          ...uploadFileList.value[index],
          ...file,
        };
      }
    };

    /**
     * 单文件上传结束 上报埋点
     * @param params
     * @param params.state 1-成功，0-失败
     */
    const track = (file: Partial<ProgressFileInfo> & { startTime: number; state: 0 | 1 }, track: any) => {
      try {
        const upload_time = parseFloat(((Date.now() - file.startTime) / 1000).toFixed(2));
        const trackData = track || {};
        upload_uploading_state({
          file_id: file.file_id || "",
          file_name: file.file_name || "",
          file_size: file.file_size || 0,
          file_type: file.file_type || "",
          state: file.state,
          upload_from: trackData.upload_from || "",
          file_from: trackData.file_from || "",
          upload_time,
        });
      } catch (e) {
        console.error("upload track error:", e);
      }
    };

    const deleteUploadFileListById = async (file_id: string) => {
      const index = uploadFileList.value.findIndex((item) => item.file_id === file_id);
      if (index !== -1) {
        uploadFileList.value.splice(index, 1);
      }
    };

    /**
     * 轮询上传文件列表进度
     * @returns
     */
    let isPolling = false;
    const pollingUploadFileListProgress = async (success?: (fileList: ProgressFileInfo[]) => void) => {
      isPolling = true;
      // 全部上传完成，执行回调，清空上传文件列表
      const allUploaded = uploadFileList.value.every((item) => item.status !== AnalysisStatus.UPLOADING);
      if (allUploaded) {
        isPolling = false;
        await success?.(uploadFileList.value);
        uploadFileList.value = [];
      } else {
        setTimeout(() => {
          pollingUploadFileListProgress(success);
        }, 3000); // 3s后再次查询
      }
    };

    return {
      waitUploadFileList,
      checkedWaitUploadFileList,
      uploadFileList,
      totalFileNum,
      curWaitFileNum,
      textLinkDialogVisible,
      spaceDialogVisible,
      isUploading,
      setTotalFileNum,
      setTextLinkDialogVisible,
      setSpaceDialogVisible,
      addFilesToWaitUpload,
      deleteUploadFileListById,
      clearWaitUploadList,
      clearUploadList,
      uploadFiles,
      analysisSimple,
      startAnalysis,
    };
  })(); // 注意这里有一个括号，立即调用，根据 moduleName 创建一个 store 实例，防止多处使用同一个uploadStore时出现冲突
};
