import type { ProgressFileInfo } from "@/components/Upload/types";
import { upload_uploading_state } from "@/tracker/upload";
import { ad_track_all } from "@/utils/ad_track.ts";

/**
 * 上传埋点追踪 Hook
 * 负责处理上传过程中的数据埋点和追踪
 */
export function useUploadTracking() {
  /**
   * 单文件上传结束埋点上报
   * @param file 文件信息（包含开始时间和状态）
   * @param trackData 追踪数据
   */
  const trackUploadResult = (
    file: Partial<ProgressFileInfo> & { startTime: number; state: 0 | 1 },
    trackData?: { upload_from?: string; file_from?: string }
  ) => {
    try {
      const upload_time = parseFloat(((Date.now() - file.startTime) / 1000).toFixed(2));
      const track = trackData || {};

      upload_uploading_state({
        file_id: file.file_id || "",
        file_name: file.file_name || "",
        file_size: file.file_size || 0,
        file_type: file.file_type || "",
        state: file.state, // 1-成功，0-失败
        upload_from: (track.upload_from as any) || "",
        file_from: (track.file_from as any) || "",
        upload_time,
      });
    } catch (e) {
      console.error("upload track error:", e);
    }
  };

  /**
   * 上传完成后的广告转化埋点
   * @param uploadFileList 上传文件列表
   */
  const trackUploadConversion = (uploadFileList: ProgressFileInfo[]) => {
    if (uploadFileList.length > 0) {
      ad_track_all("Upload_files");
    }
  };

  return {
    trackUploadResult,
    trackUploadConversion,
  };
}
