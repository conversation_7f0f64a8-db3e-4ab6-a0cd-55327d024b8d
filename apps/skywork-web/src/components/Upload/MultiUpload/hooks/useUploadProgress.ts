import type { ProgressFileInfo } from "@/components/Upload/types";
import { AnalysisStatus } from "@/views/projectV2/type";
import { type Ref } from "vue";

/**
 * 上传进度管理 Hook
 * 负责管理上传进度和轮询逻辑
 */
export function useUploadProgress() {
  let isPolling = false;

  /**
   * 轮询上传文件列表进度
   * @param uploadFileList 上传文件列表引用
   * @param success 成功回调函数
   */
  const pollingUploadFileListProgress = async (
    uploadFileList: Ref<ProgressFileInfo[]>,
    success?: (fileList: ProgressFileInfo[]) => void
  ) => {
    isPolling = true;

    // 检查是否全部上传完成
    const allUploaded = uploadFileList.value.every((item) => item.status !== AnalysisStatus.UPLOADING);

    if (allUploaded) {
      isPolling = false;
      await success?.(uploadFileList.value);
      uploadFileList.value = [];
    } else {
      setTimeout(() => {
        pollingUploadFileListProgress(uploadFileList, success);
      }, 3000); // 3秒后再次查询
    }
  };

  /**
   * 获取轮询状态
   */
  const getPollingStatus = () => isPolling;

  /**
   * 停止轮询
   */
  const stopPolling = () => {
    isPolling = false;
  };

  return {
    pollingUploadFileListProgress,
    getPollingStatus,
    stopPolling,
  };
}
