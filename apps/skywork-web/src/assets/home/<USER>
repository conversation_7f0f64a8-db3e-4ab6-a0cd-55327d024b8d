<svg width="1025" height="184" viewBox="0 0 1025 184" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_13226_377542)">
<g clip-path="url(#clip0_13226_377542)">
<rect x="6.04688" y="5" width="1013.01" height="171.958" rx="85.9789" transform="rotate(0.0156486 6.04688 5)" fill="white" fill-opacity="0.03" shape-rendering="crispEdges"/>
<g filter="url(#filter1_f_13226_377542)">
<ellipse cx="105.047" cy="221.773" rx="244" ry="103.5" fill="#4D5EFF" fill-opacity="0.07"/>
</g>
<g filter="url(#filter2_f_13226_377542)">
<ellipse cx="851.047" cy="-8.5" rx="244" ry="103.5" fill="#4D5EFF" fill-opacity="0.07"/>
</g>
<g filter="url(#filter3_f_13226_377542)">
<ellipse cx="56.0469" cy="-68.5" rx="235" ry="86.5" fill="#00FFCE" fill-opacity="0.23"/>
</g>
<g filter="url(#filter4_f_13226_377542)">
<circle cx="963.547" cy="204.5" r="66.5" fill="#05139A" fill-opacity="0.46"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_13226_377542" x="-19.3895" y="-20.3895" width="1063.83" height="223.013" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="2.75"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_13226_377542"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_13226_377542" result="shape"/>
</filter>
<filter id="filter1_f_13226_377542" x="-218.953" y="38.2734" width="648" height="367" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="40" result="effect1_foregroundBlur_13226_377542"/>
</filter>
<filter id="filter2_f_13226_377542" x="527.047" y="-192" width="648" height="367" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="40" result="effect1_foregroundBlur_13226_377542"/>
</filter>
<filter id="filter3_f_13226_377542" x="-258.953" y="-235" width="630" height="333" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="40" result="effect1_foregroundBlur_13226_377542"/>
</filter>
<filter id="filter4_f_13226_377542" x="817.047" y="58" width="293" height="293" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="40" result="effect1_foregroundBlur_13226_377542"/>
</filter>
<clipPath id="clip0_13226_377542">
<rect x="6.04688" y="5" width="1013.01" height="171.958" rx="85.9789" transform="rotate(0.0156486 6.04688 5)" fill="white"/>
</clipPath>
</defs>
</svg>
