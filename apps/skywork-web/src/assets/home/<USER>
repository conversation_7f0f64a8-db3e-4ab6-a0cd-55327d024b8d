<svg width="1426" height="1026" viewBox="0 0 1426 1026" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_f_17_10362)">
<circle cx="1145" cy="700" r="126" fill="#05139A"/>
</g>
<g filter="url(#filter1_f_17_10362)">
<circle cx="171.5" cy="389.5" r="91.5" fill="#05139A"/>
</g>
<g filter="url(#filter2_f_17_10362)">
<circle cx="777.5" cy="474.5" r="86.5" fill="#00FFCE"/>
</g>
<g filter="url(#filter3_f_17_10362)">
<circle cx="637.5" cy="529.5" r="91.5" fill="#05139A" fill-opacity="0.39"/>
</g>
<g filter="url(#filter4_f_17_10362)">
<circle cx="727" cy="333" r="2" fill="#D9D9D9"/>
</g>
<g opacity="0.51" filter="url(#filter5_f_17_10362)">
<circle cx="332" cy="146" r="2" fill="#D9D9D9"/>
</g>
<g filter="url(#filter6_f_17_10362)">
<circle cx="1220" cy="252" r="5" fill="#D9D9D9"/>
</g>
<g filter="url(#filter7_f_17_10362)">
<circle cx="105" cy="418" r="1" fill="#D9D9D9"/>
</g>
<g filter="url(#filter8_f_17_10362)">
<circle cx="131.5" cy="307.5" r="1.5" fill="#D9D9D9"/>
</g>
<g filter="url(#filter9_f_17_10362)">
<circle cx="1042.5" cy="333.5" r="1.5" fill="#D9D9D9"/>
</g>
<g filter="url(#filter10_f_17_10362)">
<circle cx="93" cy="510" r="1" fill="#D9D9D9"/>
</g>
<g filter="url(#filter11_f_17_10362)">
<circle cx="1215" cy="3" r="1" fill="#D9D9D9"/>
</g>
<g filter="url(#filter12_f_17_10362)">
<circle cx="937" cy="236" r="1" fill="#D9D9D9"/>
</g>
<g filter="url(#filter13_f_17_10362)">
<circle cx="846" cy="370" r="2" fill="#D9D9D9"/>
</g>
<g filter="url(#filter14_f_17_10362)">
<circle cx="1066" cy="779" r="1" fill="#D9D9D9"/>
</g>
<g filter="url(#filter15_f_17_10362)">
<circle cx="213" cy="99" r="1" fill="#D9D9D9"/>
</g>
<defs>
<filter id="filter0_f_17_10362" x="819" y="374" width="652" height="652" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="100" result="effect1_foregroundBlur_17_10362"/>
</filter>
<filter id="filter1_f_17_10362" x="0" y="218" width="343" height="343" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="40" result="effect1_foregroundBlur_17_10362"/>
</filter>
<filter id="filter2_f_17_10362" x="491" y="188" width="573" height="573" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="100" result="effect1_foregroundBlur_17_10362"/>
</filter>
<filter id="filter3_f_17_10362" x="466" y="358" width="343" height="343" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="40" result="effect1_foregroundBlur_17_10362"/>
</filter>
<filter id="filter4_f_17_10362" x="722.8" y="328.8" width="8.4" height="8.4" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1.1" result="effect1_foregroundBlur_17_10362"/>
</filter>
<filter id="filter5_f_17_10362" x="325.6" y="139.6" width="12.8" height="12.8" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="2.2" result="effect1_foregroundBlur_17_10362"/>
</filter>
<filter id="filter6_f_17_10362" x="1202.25" y="234.25" width="35.5" height="35.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="6.375" result="effect1_foregroundBlur_17_10362"/>
</filter>
<filter id="filter7_f_17_10362" x="102.9" y="415.9" width="4.2" height="4.2" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.55" result="effect1_foregroundBlur_17_10362"/>
</filter>
<filter id="filter8_f_17_10362" x="128.35" y="304.35" width="6.3" height="6.3" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.825" result="effect1_foregroundBlur_17_10362"/>
</filter>
<filter id="filter9_f_17_10362" x="1039.35" y="330.35" width="6.3" height="6.3" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.825" result="effect1_foregroundBlur_17_10362"/>
</filter>
<filter id="filter10_f_17_10362" x="90.9" y="507.9" width="4.2" height="4.2" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.55" result="effect1_foregroundBlur_17_10362"/>
</filter>
<filter id="filter11_f_17_10362" x="1212.9" y="0.9" width="4.2" height="4.2" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.55" result="effect1_foregroundBlur_17_10362"/>
</filter>
<filter id="filter12_f_17_10362" x="934.9" y="233.9" width="4.2" height="4.2" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.55" result="effect1_foregroundBlur_17_10362"/>
</filter>
<filter id="filter13_f_17_10362" x="841.8" y="365.8" width="8.4" height="8.4" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1.1" result="effect1_foregroundBlur_17_10362"/>
</filter>
<filter id="filter14_f_17_10362" x="1063.9" y="776.9" width="4.2" height="4.2" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.55" result="effect1_foregroundBlur_17_10362"/>
</filter>
<filter id="filter15_f_17_10362" x="210.9" y="96.9" width="4.2" height="4.2" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.55" result="effect1_foregroundBlur_17_10362"/>
</filter>
</defs>
</svg>
