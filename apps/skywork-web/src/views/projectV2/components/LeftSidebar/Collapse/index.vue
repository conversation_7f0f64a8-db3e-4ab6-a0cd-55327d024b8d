<script lang="ts" setup>
import Toggle from "./Toggle.vue";
import Add from "./Add.vue";
import Sources from "./Sources.vue";
import { useArtifactStore } from "@/store/projectDetail";
import AddMenu from "./AddMenu/index.vue";
defineProps<{
  onExpand: () => void;
}>();
const { isArtifactEdit } = storeToRefs(useArtifactStore());
</script>
<template>
  <div class="flex h-full flex-col justify-center">
    <div
      class="bg-fill-fill-5 box flex w-[60px] flex-col items-center gap-y-4 rounded-xl px-3 py-5"
      :class="{
        mask: isArtifactEdit,
      }"
    >
      <Toggle @click="onExpand" />
      <div class="line" />
      <Add :onExpand="onExpand" />
      <AddMenu />
      <div class="line" />
      <Sources />
    </div>
  </div>
</template>
<style scoped lang="scss">
.box {
  box-shadow: 0px 1px 6px 0px rgba(0, 8, 24, 0.04);
  position: relative;

  &.mask {
    cursor: not-allowed;
    &::after {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: var(--fill-fill-5);
      opacity: 0.6;
    }
  }
}
.line {
  background-color: var(--line-line-4);
  width: 20px;
  height: 1px;
  display: none;

  &:not(:last-child) {
    display: block;
  }
}
</style>
