<template>
  <div
    class="flex h-full w-full flex-col items-center justify-center gap-[8px]"
    v-if="getSearchConfigLoading || getUserSkillLoading || getPointEstimateInfoLoading"
  >
    <img class="h-[110px] w-[110px]" :src="getIcon()" />
    <div class="loading-text">{{ loadingText }}...</div>
  </div>
  <div class="new_home_container flex flex-col items-center justify-start" v-else>
    <!-- <Banner /> -->
    <SearchArea
      class="z-20 mt-[54px] w-[860px]"
      :class="{ 'fixed top-[30px] z-10': isSearchAreaFixed }"
      :showBtnName="true"
      from="home"
      :skillId="userSkillId"
      :netSearch="netSearch"
    ></SearchArea>
    <div class="bg-background-main-1 absolute z-10 flex min-h-[398px] w-[866px]" v-show="isSearchAreaFixed"></div>
    <div class="pb-[10px]" :class="{ 'mt-[398px]': isSearchAreaFixed }">
      <TemplateCase :showScrollTop="showScrollTop" />
    </div>
    <Footer />
  </div>
</template>
<script lang="tsx" setup>
import Footer from "./Footer.vue";
import SearchArea from "@/components/SearchArea/SearchArea.vue";
import TemplateCase from "@/views/newHome/TemplateCase.vue";
import { useEventListener } from "@vueuse/core";
import { apiGetHomeUserSkill } from "@/api/firstPages";
import { DocMessage } from "@tg-fe/ui";
import { $t } from "@/locales";
import { useDark } from "@/views/projectV2/hooks/useDark";
import defaultLoadingL from "@/assets/images/loading/loading.gif";
import defaultLoadingD from "@/assets/images/loading/loadingDark.gif";
import useSearchAreaStore from "@/components/SearchArea/store/useSearchAreaStoreV2";
import { useUserStore } from "@/store/user/index";
import { home_overall_show } from "@/tracker/homeV2";
// import Banner from "./Banner.vue";
import setPageTitle from "@/utils/set-page-title";
import { useNotificationsStore } from "@/store/notifications/index";
import { useGuideStore } from "@/store/guide/index";
import { isChina } from "@/lib/region";
import usePointStore from "@/components/Commercialization/store/point";
import MultiUpload from "@/components/Upload/MultiUpload/index.vue";
import Button from "../doc/components/DocEditor/BubbleMenu/Tools/Button.vue";

const useGuide = useGuideStore();
const { openGuideMainModal } = useGuide;
const { isDarkMode } = useDark();
const { getSearchConfig } = useSearchAreaStore("home");
const { getSearchConfigLoading } = storeToRefs(useSearchAreaStore("home"));
const useNotifications = useNotificationsStore();
const { oldOpenNewNotice } = useNotifications;
const userStore = useUserStore();
const { userInfo } = storeToRefs(userStore);
const { getPointEstimateInfoLoading } = storeToRefs(usePointStore());
const { getPointEstimateInfo } = usePointStore();
const loadingText = computed(() => {
  return $t("projectDetail.common.loading");
});
const getIcon = () => {
  if (isDarkMode.value) {
    return defaultLoadingD;
  }
  return defaultLoadingL;
};

const isSearchAreaFixed = ref(false);
const showScrollTop = ref(false);
const handleScroll = () => {
  if (!document.querySelector(".new_home_container")) return;
  const scrollTop = document.querySelector(".new_home_container")!.scrollTop;
  if (scrollTop > 32) {
    isSearchAreaFixed.value = true;
  } else {
    isSearchAreaFixed.value = false;
  }
  // 滚动距离超过一屏显示回到顶部按钮
  // console.log(scrollTop, window.innerHeight);
  if (scrollTop > window.innerHeight) {
    showScrollTop.value = true;
  } else {
    showScrollTop.value = false;
  }
};

const getUserSkillLoading = ref(false);
const userSkillId = ref<string>("");
const netSearch = ref<boolean>(false);

const getHomeUserSkill = async () => {
  try {
    getUserSkillLoading.value = true;
    const res = await apiGetHomeUserSkill();
    userSkillId.value = res.data?.skill_id || "";
    getUserSkillLoading.value = false;
    netSearch.value = res.data?.net_search || false;
  } catch (e: any) {
    console.log(e);
    DocMessage.warning({
      message: e.message,
      type: "warning",
      duration: 2000,
    });
    getUserSkillLoading.value = false;
  }
};
const homeMetaTitle = computed(() => $t("common.meta.home"));
watch(
  () => homeMetaTitle.value,
  () => {
    setPageTitle(homeMetaTitle.value);
  },
  { immediate: true }
);
onMounted(() => {
  home_overall_show();
  // if (useUserStore().isLogin()) {
  getSearchConfig();
  // getHomeUserSkill();
  // }
  useEventListener(document.querySelector(".new_home_container"), "scroll", handleScroll);

  const smsLoginData = localStorage.getItem("smsIsOneLoginData");
  const loginData = localStorage.getItem("isOneLoginData");

  if (isChina()) {
    //新用户默认触发新功能提示
    if ((smsLoginData && Number(smsLoginData) === 1) || (loginData && Number(loginData) === 1)) {
      openGuideMainModal();
    }
    if (smsLoginData && Number(smsLoginData) === 0) {
      oldOpenNewNotice();
    }
  } else {
    //老用户默认触发新功能提示
    const isOrderData = localStorage.getItem("isShowGuide_" + userInfo.value.email);
    if (isOrderData && Number(isOrderData) === 1) {
      oldOpenNewNotice();
    }
  }
});
</script>
<style scoped lang="scss"></style>
