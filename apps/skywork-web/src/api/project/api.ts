const projectUploadApiBase = "/mulit";
const uploadApiBase = "/infra";
export default {};

export const uploadApi = {
  beforeUpload: projectUploadApiBase + "/upload/sign", // (项目、知识库)上传前获取oss_url、流水号[多文件]
  analysis: projectUploadApiBase + "/upload/analysis", // 文件解析
  analysisSimple: projectUploadApiBase + "/upload/analysis_simplify", // 文件解析
  progress: projectUploadApiBase + "/upload/progress", // 项目文件列表&解析进度
  fileAddUrl: uploadApiBase + "/file/add", // （通用）上传前获取oss_url、流水号[单文件]
  fileGetUrl: uploadApiBase + "/file/get", // （通用）根据文件id获取文件信息[单文件]
  getAccessToken: uploadApiBase + "/google_driver/get_access_token", // 获取google drive的access_token
};
