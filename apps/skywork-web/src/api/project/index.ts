import { ppt } from "@/api/pptApi";
import { http } from "@/utils/http/index";
import { AnalysisSource, FileType } from "@/views/projectV2/type";
import { uploadApi } from "./api";

interface Response<T> {
  code: number;
  data: T;
  message: string;
}

export enum SpecialType {
  WebsiteLink = 1,
  GoogleDrive = 2,
  Dropbox = 3,
}

export type ResourceType = 1 | 2 | 3; // 1-本地文件，2-远程链接，3-网盘

export interface FileInfo {
  id: number;
  file_name: string; // 文件名称
  knowledge_type: 1 | 2; // 1: 私域知识, 2: 公域知识
  file_type: string; // 文件类型，例如 "pdf"
  file_size: number; // 文件大小，单位B
  outside_url?: string; // 外部链接：dropbox、website_url、youtube_url
  special_type?: SpecialType | -1; // 特殊类型
  outside_id?: string; // google drive 文件id
}

export type ApiBeforeUploadFile = FileInfo & { resource_type: ResourceType };

export interface FileSimpleInfo {
  file_id: number;
  file_name: string; // 文件名称
  file_type: string; // 文件类型，例如 "pdf"
  outside_url?: string; // 链接
  file_size: number; // 文件大小，单位B
}

export interface ResFileInfo {
  file_no: string;
  file_expire_url: string; // oss链接，有过期时间
  file_cdn_url?: string; // cdn链接，永不是失效
  file_name: string;
  file_type: string;
  file_size: number;
}

export type ResProgressFileInfo = Pick<
  AnalysisSource,
  "file_name" | "file_url" | "preview_url" | "file_id" | "file_size" | "file_type" | "progress" | "status"
>;
export { FileType };

// oss业务Key文档：https://rg975ojk5z.feishu.cn/wiki/VuYowe1IHiipdGkSARrcHlD5n6f
export enum BusinessFileType {
  avatar = "user_head_picture",
  // 其他与服务端沟通，按需添加
}

// 上传到知识库/项目之前，获取oss_url、流水号(file_no)
export async function apiBeforeUpload(data: {
  project_id?: string; // 项目id
  space_id?: string; // 文件夹id
  business_source?: number; // 业务来源：1-skywork
  page_source?: number; // 业务来源：1-上传页
  file_list: ApiBeforeUploadFile[]; // 文件列表
}) {
  return http.post<
    Response<{
      upload_result_list: {
        file_id: string;
        file_name: string;
        id: number;
        url: string;
        is_success: boolean;
        reason: string;
        token: string;
        oss_object_name: string; // oss文件名
      }[];
    }>
  >(uploadApi.beforeUpload, {
    business_source: 1,
    page_source: 1,
    ...data,
  } as unknown as JSONValue);
}

// 文件解析
export const apiAnalysis = async (file_ids: string[]) => http.post<Response<any>>(uploadApi.analysis, { file_ids });

// 文件解析simple
export const apiAnalysisSimple = async (data: {
  request_type: number;
  business_source: number;
  page_source: number;
  space_id: string;
  file_list: FileSimpleInfo[]; // 文件列表
}) => http.post<Response<any>>(uploadApi.analysisSimple, data as unknown as JSONValue);

// 项目文件列表&解析进度
export const apiProgress = async (data: { project_id?: string; file_ids: string[] }) =>
  http.post<Response<{ list: ResProgressFileInfo[] }>>(uploadApi.progress, data);

// 普通单文件上传前，获取oss_url、流水号(file_no)
export const apiBeforeUploadSingle = async (data: {
  business_file_type: BusinessFileType;
  file_name: string; // 文件名称，不带后缀
  file_type: string; // 文件类型
  file_size: number; // 文件大小，MB为单位
  expire_time?: number; // 过期时间（秒），不传服务端兜底
}) =>
  http.post<Response<{ file_expire_url: string; file_no: string; expire_time: number }>>(uploadApi.fileAddUrl, data);

// 根据file_no[]获取文件信息
export const apiGetFileInfo = async (data: {
  file_no_list: string[];
  file_name_list?: string[];
  expire_time?: number;
  is_get_cdn_url?: boolean; // 是否返回cdn链接
}) =>
  http.post<
    Response<{
      file_record_map: Record<string, ResFileInfo>;
    }>
  >(uploadApi.fileGetUrl, data);

/**
 * 获取google drive的access_token
 * @param code 首次生成需要传。不传，则会查询refresh_token生成access_token，若没有则报错
 * @returns access_token
 */
export const apiGetAccessToken = async (code?: string) =>
  http.post<Response<{ access_token: string }>>(uploadApi.getAccessToken, { code });

// ppt截图上传不登录
export const apiBeforeUploadSingleNoLogin = async (data: {
  user_id: string;
  file_name: string; // 文件名称，不带后缀
  file_type: string; // 文件类型
  file_size: number; // 文件大小，MB为单位
}) => http.post<Response<{ file_expire_url: string; file_no: string; expire_time: number }>>(ppt.uploadPptImg, data);
