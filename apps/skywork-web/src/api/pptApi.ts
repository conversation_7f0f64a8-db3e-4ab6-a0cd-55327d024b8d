const uploadFileUrl = import.meta.env.VITE_BASE_API;
export default {
  agentFileInfo: uploadFileUrl + "/chatdoc-api/chatdoc/v1/files/", // 获取文件信息
};

export const ppt = {
  outlineSettings: "/creation/ppt/instruction", // ppt大纲生成-默认个性化设置下发
  themesConfig: "/creation/ppt/theme/config", // ppt主题筛选选项
  themesList: "/creation/ppt/theme/list", // ppt主题列表
  themesToJson: "/creation/ppt/theme/xml_to_json", // xmlToJson
  themesUpdate: "/creation/ppt/theme/update", // ppt主题更新
  themesAddTag: "/creation/ppt/theme/tag/update", // ppt tag
  themesRelation: "/creation/ppt/theme/relation/add", // ppt关联tag
  uploadPptImg: "/creation/ppt/upload/sign", // ppt上传不认证
  imgUpload: uploadFileUrl + "/multi-api/writing-multi/v1/image/material/upload", // 上传图片
  urlImgUpload: uploadFileUrl + "/multi-api/writing-multi/v1/image/material/upload/url", // 通过url上传图片
  searchImage: "/creation_api/image/search", // 搜索图片
  imageLibrary: "/creation_api/image/material/search", // 图库、图标库
  insetImageList: "/creation_api/sketch/sketch/list", // 插画列表
  getTheme: "/creation_api/themes/get", // 获取ppt主题
  // ppt接口
  savePpt: "/creation_api/ppt/file/save", // 点击生成ppt时，先调用该接口 生成ppt_id
  generatePpt: "/creation/ppt/generate", // 生成PPT
  updateOutline: "/creation/ppt/outline/update", // 修改大纲
  sseGenerateOutline: "/creation/ppt/outline/generate", // 生成大纲
  sseGeneratePptFile: import.meta.env.VITE_BASE_API + "/creation/ppt/generate", // 生成PPT文件
  sseImprovePpt: import.meta.env.VITE_BASE_API + "/creation/ppt/generate/page", // improve PPT文件
  getOutlineDetail: "/creation/ppt/outline/detail", // ppt大纲查询详情
  getPptDetail: "/creation/ppt/detail", // 获取ppt详情
  getPptDetailByTempToken: "/creation/ppt/crawl/detail", // 通过pt详情，爬虫加载ppt-pure页面使用
  getPublishedPptDetailByTempToken: "/creation_api/ppt/publish/crawl/detail", // 通过temp_token获取已发布的ppt详情，爬虫加载ppt-pure页面使用
  updatePptTitle: "/creation/ppt/update", // ppt编辑
  updatePptCover: "/creation/ppt/update/cover", // ppt 封面
  saveCoverPage: "/creation_api/image/material/upload/cover_img", // 保存封面图, 导出pptx截图上传
  saveCoverPageHeadless: "/creation_api/image/material/headless_browser/upload/cover_img", // 无头浏览器保存封面图（需要额外temp_access_token）
  generateSinglePpt: "/creation_api/ppt/file/single", // 生成单页ppt
  rewritePpt: "/creation_api/ppt/file/text/rewrite", // ppt文本重写
  updatePpt: "/creation_api/ppt/file/update", // ppt编辑保存(old)
  pptFileExport: "/creation_api/ppt/file/export", // ppt下载pdf
  exportPptxTaskCreate: "/creation_api/ppt/file/export/task/create", // 导出pptx任务创建
  exportPptxTaskProcess: "/creation/ppt/export/process", // 导出pptx任务执行，无头浏览器中
  exportPptxTaskQuery: "/creation/ppt/export/file", // 导出pptx任务查询，轮询接口，获取pptx下载地址
};
