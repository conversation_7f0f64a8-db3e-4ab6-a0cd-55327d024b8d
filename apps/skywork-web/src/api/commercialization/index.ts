import {
  Currency,
  DailyPoint,
  EstimatedPointInfo,
  MarkEnum,
  PayChannel,
  PersonalSubscribeInfo,
  PointEstimateTextContent,
  PointEstimateTextEnum,
  PointPackageDetail,
  ProductItem,
  RecentEstimatedPointInfo,
  SubmitPointActionEnum,
  SubscribeOrderResponse,
  SubscribeProduct,
  SupportChannel,
} from "@/components/Commercialization/type";
import { SkillId } from "@/components/SearchArea/types";
import { Response } from "@/typings/common";
import { http, httpChinaTest } from "@/utils/http";
import commercializationApi from "./api";

// 订阅续订
export const apiSubscribeRenew = (params: { payment_channel: PayChannel; produce_id?: string | null }) => {
  return http.post<Response<any>>(commercializationApi.commercializationSubscribeRenewal, params);
};

// 取消订阅
export const apiCancelSubscribe = (params: { plan_id?: string | null }) => {
  return http.post<Response<any>>(commercializationApi.commercializationCancelSubscribe, params);
};

// 个人中心获取订阅信息
export const apiGetSubscribeInfo = () => {
  return http.post<Response<PersonalSubscribeInfo>>(commercializationApi.commercializationSubscribeDetail);
};

// 获取订阅订单
export const apiGetSubscribeOrder = (params: {
  product_id: string;
  payment_channel: PayChannel;
  client_id: any;
  channel_version?: number;
  bury_extend?: Record<string, any>;
}) => {
  return http.post<Response<SubscribeOrderResponse>>(
    commercializationApi.commercializationCreateSubscribeOrder,
    params
  );
};

// 获取订阅套餐
export const apiGetSubscribeProductList = (params: { type: "month" | "year" | "all" }) => {
  return http.post<Response<SubscribeProduct>>(commercializationApi.commercializationSubscribePlist, params);
};

// 当前支持的支付渠道
export const apiGetPayChannel = (params: { business_source: string }) => {
  return httpChinaTest.get<Response<{ support_channels: SupportChannel[]; all_channels: SupportChannel[] }>>(
    commercializationApi.commercializationPayChannel,
    params
  );
};
// 用户积分流水
export const apiGetPointRecord = (params: { page_num: number; page_size: number; record_type: MarkEnum }) => {
  return http.post<Response<any>>(commercializationApi.commercializationMarkRecord, params);
};
// 用户积分包明细
export const apiGetPointPackageDetail = () => {
  return http.post<Response<PointPackageDetail>>(commercializationApi.commercializationMarkDetail);
};

// 积分套餐列表
export const apiGetPointPackageList = () => {
  return http.post<Response<{ mark_product: ProductItem[]; append_content: string }>>(
    commercializationApi.commercializationProductList
  );
};

// 每日积分检查/获取用户积分总量接口
export const apiGetDailyPoint = () => {
  return http.post<Response<DailyPoint & { user_flag: boolean }>>(commercializationApi.commercializationDailyCheck);
};

// 创建订单
export const apiCreateOrder = (params: {
  sku_id: string;
  payment_channel: PayChannel;
  office_id?: SkillId | null;
  currency?: Currency;
  price?: string;
  client_id?: string;
  extend: Record<string, any>;
}) => {
  return http.post<Response<any>>(commercializationApi.commercializationCreateOrder, params);
};

// 查询订单详情
export const apiGetOrderDetail = (params: { business_no: string }) => {
  return http.post<Response<any>>(commercializationApi.commercializationGetOrder, params);
};

// 获取邀请好友链接
export const getShareUrl = () => {
  return http.post<Response<any>>(commercializationApi.commercializationShareUrl);
};

// 查询积分预估信息
export const apiGetPointEstimateInfo = () => {
  return http.post<Response<EstimatedPointInfo>>(commercializationApi.commercializationPointEstimate, {});
};

// 查询用户最近预估积分包明细
export const apiGetRecentPointEstimateInfo = () => {
  return http.post<Response<{ list: RecentEstimatedPointInfo[] }>>(
    commercializationApi.commercializationRecentPointEstimate,
    {}
  );
};

// 提交预估
export const apiSubmitPointEstimate = (params: {
  action: SubmitPointActionEnum;
  mark_estimated_info_msg: Record<string, any>;
}) => {
  return http.post<Response<any>>(commercializationApi.commercializationPointEstimateSubmit, params);
};

// 查询积分预估文案
export const apiGetPointEstimateText = (params: { template_name: Array<PointEstimateTextEnum> }) => {
  return http.post<Response<{ template_config: Array<PointEstimateTextContent> }>>(
    commercializationApi.commercializationPointEstimateText,
    params
  );
};
