import { SkillId } from "@/components/SearchArea/types";
import { Response } from "@/typings/common";
import { http } from "@/utils/http";
import type { AnalysisSource } from "@/views/projectV2/type";
import api, { chatApi, left } from "./api";

// 文件名校验
export const apiFileNameCheck = async (data: { filename_list: string[] }) => {
  return http.post<Response<any>>(api.fileNameCheck, data);
};

// load recommend
export const apiLoadRecommend = async (data: { project_id: string }) => {
  return http.post<Response<any>>(api.loadRecommend, data);
};

// 强制信源不足校验
export const apiSourceCheck = async (data: { files: string[] }) => {
  return http.post<Response<any>>(left.sourceCheck, data);
};

// 推荐信源
export const apiGetRecommendSources = async (data: { project_id: string }) => {
  return http.post<Response<{ list: any[] }>>(left.recommendResourceList, data);
};

// 查询文件解析列表
export const apiGetFileParseList = async (data: {
  project_id?: string;
  file_ids: string[];
  is_export?: boolean;
  export_name?: string;
}) => {
  return http.post<Response<{ list: AnalysisSource[]; export_url?: string }>>(left.analysisList, data);
};

// 删除文件解析列表
export const apiDeleteFileParseList = async (data: { file_ids: string[]; project_id: string }) => {
  return http.post<Response<any>>(left.del, data);
};

// 文件解析列表导出
export const apiExportFileParseList = async (data: { file_ids: string[]; is_export: boolean }) => {
  return http.post<Response<any>>(api.exportExcel, data);
};

// 公域&&私域知识查询
export const apiSearchKnowledge = async (data: {
  project_id?: string;
  query: string;
  request_type: 1 | 2; //1:私域 2:公域
  query_list?: any[];
  file_format?: string[] | null; //文件格式
}) => {
  return http.post<Response<{ private_list: any[]; public_list: any[] }>>(left.search, data);
};
// 查询最近知识库
export const apiQueryRecentKnowledge = async (data: { project_id?: string; file_type: string[] }) => {
  return http.post<Response<{ private_list }>>(api.queryRecentKnowledge, data);
};

// 获取知识库树形结构
export const apiGetKnowledgeTree = async (data: { project_id?: string }) => {
  return http.get<Response<any>>(api.getKnowledgeTree, data);
};

// 搜索历史
export const apiGetSearchHistory = async (data: { project_id: string; request_type: 1 | 2 }) => {
  return http.post<Response<any>>(left.searchHistory, data);
};

// 删除搜索历史
export const apiDeleteSearchHistory = async (data: { ids: string[] }) => {
  return http.post<Response<any>>(left.delSearchHistory, data);
};

// 获取默认推荐query
export const apiGetDefaultQuery = async (data: { project_id: string }) => {
  return http.post<Response<{ query: string }>>(left.defaultQuery, data);
};

// 添加私域到信源
export const apiAddPrivateToSource = async (data: { page_source?: number; project_id: string; file_ids: string[] }) => {
  return http.post<Response<any>>(left.addToKnowledge, data);
};

// 查询补充信源 tab
export const apiGetSourceTab = async () => {
  return http.post<Response<{ search_type: any[] }>>(left.addSourceDialogTab);
};

// 文档模板
export const apiGetTemplates = async () => {
  return http.get<Response<{ list: any[] }>>(
    api.template,
    {},
    {
      headers: {
        mockuser: 1,
      },
    }
  );
};

// 我的作品
export const apiGetMyWorks = async (data: { project_id: string; offset: number | string }) => {
  return http.get<Response<{ list: any[]; offset: number; has_more: boolean }>>(api.outList, {
    project_id: data.project_id,
    offset: data.offset,
  });
};

// 获取作品状态
export const apiGetWorkStatus = async (data: { id_list: number[] }) => {
  return http.post<Response<any>>(api.getOutListStatus, data);
};

// 设置激活 tab
export const apiSetActiveTab = async (data: { tab: "analysis" | "study" | "work"; project_id: string }) => {
  return http.post<Response<any>>(api.changeTab, data);
};

// 查询项目激活页
export const apiGetActiveTab = async (data: { project_id: string }) => {
  return http.get<Response<{ tab: "analysis" | "study" | "work" }>>(api.getActiveTab, data);
};

// 查询项目信息
export const apiGetProjectInfo = async (data: { id: string }) => {
  return http.post<Response<any>>(api.getProjectInfo, data);
};
export const apiGetProjectInfoShare = async (data: { id: string }) => {
  return http.post<Response<any>>(api.getProjectInfoShare, data);
};

// 项目反馈配置
export const apiFeedbackConfig = async (data: { office_id: SkillId }) => {
  return http.get<Response<any>>(chatApi.feedbackConfig, data);
};
