// const apiBase = import.meta.env.VITE_DOCCIE_BASE_URL;

export default {
  template: "/creation/project/template",
  outList: "/creation/project/out_list",
  getOutListStatus: "/creation/project/out_state",
  changeTab: "/creation/project/change_tab",
  getActiveTab: "/creation/project/my_tab",
  getProjectInfo: "creation/resource/project/info",
  getProjectInfoShare: "creation/resource/project/info/share",
  exportExcel: "/mulit/upload/analysis",
  getKnowledgeTree: "/creation/knowledge/get_knowledge_tree",
  queryRecentKnowledge: "/creation/knowledge/query_recent_knowledge",
  loadRecommend: "/creation/generate/load_recommend",
  fileNameCheck: "/mulit/upload/filename_check",
};
export const left = {
  recommendResourceList: "/mulit/information_source/get_recommend_list",
  analysisList: "/mulit/upload/progress",
  del: "/mulit/upload/del",
  addSourceDialogTab: "/mulit/information_source/label",
  search: "/mulit/information_source/search",
  searchHistory: "/mulit/information_source/get_history_query",
  delSearchHistory: "mulit/information_source/del_history_query",
  addToKnowledge: "/mulit/information_source/add_to_knowledge",
  defaultQuery: "/mulit/information_source/get_default_query",
  sourceCheck: "/creation/generate/source_check",
};
export const chatApi = {
  chatSse: "/creation/chat/sse",
  chatSseStop: "/creation/chat/sse/stop",
  chatHistory: "/chat/chat/history",
  chatSessionIdGet: "/chat/chat/session_id/get",
  chatExportLimiter: "/creation/chat/exportation/task/limiter",
  memoryExtract: "creation/memory/extract", //查询并记录chat卡片接口
  memoryUndo: "creation/memory/undo", //回滚更新接口
  citation: "/chat/citation/get", // 信源获取
  citationShare: "/chat/citation/get/share", // 信源获取 分享
  getArtifactViewBtns: "/chat/artifact/detail", // 获取Artifact视口信息
  getArtifactViewBtnsShare: "/chat/artifact/detail/share", // 获取Artifact视口信息 分享
  getArtifactDetail: "/chat/artifact/version/detail", // 获取Artifact版本详情
  getArtifactDetailShare: "/chat/artifact/version/detail/share", // 获取Artifact版本详情 分享
  systemLike: "/chat/system/like",
  feedbackConfig: "/chat/task/feedback/config",
  feedbackSubmit: "/chat/task/feedback/submit",
};

export const operationApi = {
  exportList: "/tool/generate/export/file_type/list",
  createExport: "/tool/generate/export",
  GetExportTask: "/tool/generate/export/task",
};

export const PPTv2 = {
  versionDetail: "/chat/artifact/version/detail",
  versionDetailShare: "/chat/artifact/version/detail/share",
  artifactSavePPT: "/chat/ppt/update",
  getOutlineDetail: "/chat/ppt/outline/detail",
  getOutlineDetailShare: "/chat/ppt/outline/detail/share",
  updateOutline: "/chat/ppt/outline/update",
};

export const googleSlides = {
  toolGoogleSlides: "/chat/tool/export/task",
  googleStatus: "/chat/tool/export/status",
};
