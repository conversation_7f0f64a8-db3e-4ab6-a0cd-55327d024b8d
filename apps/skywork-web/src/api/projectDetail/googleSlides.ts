import { http } from "@/utils/http";
import { googleSlides } from "./api";

interface Response<T> {
  code: number;
  data: T;
  msg: string;
  message: string;
}

//保存到google slides接口
export const apiToolGoogleSlides = async (data: {
  token: string;
  resource_id?: string;
  resource_type?: string;
  export_file_type?: string;
  project_id?: string;
}) => {
  return http.post<Response<any>>(googleSlides.toolGoogleSlides, data);
};
//轮询任务结果
export const apiGoogleStatus = async (data: { task_id?: string; resource_id?: string }) => {
  return http.post<Response<any>>(googleSlides.googleStatus, data);
};
