import { useProjectDetailStore } from "@/store/projectDetail";
import { http } from "@/utils/http";
import { AgentFeedback } from "@/views/projectV2/type";
import { AgentHistory, AgentId, AgentLike, AgentSource } from "@tg-fe/shared";
import { chatApi } from "./api";

interface Response<T> {
  code: number;
  data: T;
  msg: string;
  message: string;
}

interface apiChatHistoryParams {
  project_id?: string;
  source?: AgentSource;
  offset?: number;
  session_id?: string;
}

const { isShare } = storeToRefs(useProjectDetailStore());

/** 获取chat 历史记录 */
export const apiGetChatHistory = (params: apiChatHistoryParams) => {
  return http.post<Response<AgentHistory>>(chatApi.chatHistory, params as unknown as JSONValue);
};

/** 获取sessionId */
export const apiGetChatSessionId = (params: { project_id: string; agent_id: AgentId; source: AgentSource }) => {
  return http.post<Response<{ session_id: string }>>(chatApi.chatSessionIdGet, params);
};

/** 停止sse */
export const apiChatSseStop = (params: { session_id: string; question_id: string }) => {
  return http.post<{
    is_stop: boolean;
  }>(chatApi.chatSseStop, params);
};

export const apiGetChatExportLimiter = (params: { source: AgentSource }) => {
  return http.post<Response<{ allow: boolean }>>(chatApi.chatExportLimiter, params);
};

/** 点赞 */
export const apiSystemLike = (params: {
  source: AgentSource;
  session_id: string;
  task_id: string;
  op_type: AgentLike;
}) => {
  return http.post(chatApi.systemLike, params);
};

export const apiChatMemoryExtract = (params: { query: string; session_id: string; question_id: string }) => {
  return http.post(chatApi.memoryExtract, params);
};

export const apiChatMemoryUndo = (params: { add: any[]; del: any[]; session_id: string; question_id: string }) => {
  return http.post(chatApi.memoryUndo, params);
};

export const apiCitation = (params: { output_id?: string; version_id?: string; artifact_id?: string }) => {
  return http.post(isShare.value ? chatApi.citationShare : chatApi.citation, params);
};
export const apiCitationShare = (params: { output_id?: string; version_id?: string; artifact_id?: string }) => {
  return http.post(chatApi.citationShare, params);
};

// 获取Artifact视口信息
export const apiGetArtifactVersionList = async (data: { artifact_id: string }) => {
  return http.post<Response<any>>(isShare.value ? chatApi.getArtifactViewBtnsShare : chatApi.getArtifactViewBtns, data);
};

// 获取Artifact版本详情
export const apiGetArtifactDetail = async (data: {
  artifact_id?: string;
  version_id?: string;
  output_id?: string;
  output_type?: string;
}) => {
  return http.post<Response<any>>(isShare.value ? chatApi.getArtifactDetailShare : chatApi.getArtifactDetail, data);
};

// 项目反馈提交
export const apiFeedbackSubmit = async (data: {
  project_id: string;
  task_id: string;
  office_id: string;
  type: AgentFeedback; // 1 点赞 2点踩 0卡片内容反馈
  main_tag: any[];
  sub_tag?: any[];
  detail?: string;
  card_id?: string;
  card_type?: string;
}) => {
  return http.post<Response<any>>(chatApi.feedbackSubmit, data);
};
