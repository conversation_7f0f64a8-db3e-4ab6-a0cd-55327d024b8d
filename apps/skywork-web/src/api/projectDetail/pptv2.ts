import { http } from "@/utils/http";
import { PPTv2 } from "./api";

interface Response<T> {
  code: number;
  data: T;
  msg: string;
  message: string;
}

// 获取Artifact ppt版本详情
export const apiGetPPTDetail = async (data: { artifact_id: string; version_id?: string; output_id?: string }) => {
  return http.post<Response<any>>(PPTv2.versionDetail, data);
};
export const apiGetPPTDetailShare = async (data: { artifact_id: string; version_id?: string; output_id?: string }) => {
  return http.post<Response<any>>(PPTv2.versionDetailShare, data);
};

// 获取Artifact ppt版本详情
export const apiArtifactSavePPT = async (data) => {
  return http.post<Response<any>>(PPTv2.artifactSavePPT, data);
};
