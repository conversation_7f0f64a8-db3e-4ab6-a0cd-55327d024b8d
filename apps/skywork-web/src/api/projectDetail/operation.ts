import { http } from "@/utils/http";
import { operationApi } from "./api";

interface Response<T> {
  code: number;
  data: T;
  msg: string;
  message: string;
}

export interface apiExportParams {
  resource_id: string; // 导出资源id，如pptlid/docid
  resource_type: string; // 资源类型，如ppt/doc
  export_file_type: "pdf" | "pptx" | "doc" | "docx" | "zip"; //pdf/ppt/doc/docx
  bussiness_type: string; // 业务类型， skywork
  project_id: string;
  channel_id?: number; // 渠道ID，0：阿里云 1：wps
}

export interface Task {
  task_id: string;
  task_status: number; // 0：初始化 1：成功 2：失败
  task_status_desc?: string; // 任务状态描述
  down_load_url: string;
}

export enum TaskStatusEnum {
  INIT = 0, // 初始化
  SUCCESS = 1, // 成功
  FAILED = 2, // 失败
}

export interface DownloadChannel {
  channel_type: number; // 渠道类型，0=免费，1=付费
  channel_id: number; // 渠道ID，0：阿里云 1：wps
  channel_desc: string; // 渠道描述，如 "免费下载"
  point_nums: number; // 所需积分，如 0 或 200
  floating_desc: string; // 浮层文案
}

/**
 * 标签类型
 * 次数：time
 * 积分：point
 * 免费：free
 * 已下载：downloaded
 * **/
export enum LabelEnum {
  TIMES = "times",
  POINT = "point",
  FREE = "free",
  DOWNLOADED = "downloaded",
}

export type LabelType = (typeof LabelEnum)[keyof typeof LabelEnum];

export interface ExportFileType {
  attempt_desc?: string; // 剩余次数文案描述
  remain_attempt?: number; // 剩余次数
  file_type_name: string; // 文件类型名称，如 "DOCX"
  file_type_desc: string; // 文件类型描述（可能为空）
  channel_list: DownloadChannel[]; // 渠道列表
  label_desc?: string; // 标签提示文案
  label_type: LabelType;
  label_floating_desc?: string; // 标签浮层文案
}

export interface ExportListRes {
  down_load_menu_name: string;
  title_list: {
    title: string;
    title_desc: string;
  } | null;
  rule_list:
    | {
        rule_desc: string;
        floating_desc: string[];
      }[]
    | null;
  file_type_list: ExportFileType[];
}

export const apiExportList = async (params: {
  source: "skywork";
  file_type: string;
  resource_id: string;
  project_id: string;
}) => {
  return http.post<Response<ExportListRes>>(operationApi.exportList, params as unknown as JSONValue);
};

/** 创建导出任务 */
export const apiCreateExportTask = async (params: apiExportParams) => {
  return http.post<Response<{ task_id: string }>>(operationApi.createExport, params as unknown as JSONValue);
};

/** 创建导出任务 */
export const apiGetExportTask = async (params: Pick<Task, "task_id">) => {
  return http.post<Response<Task>>(operationApi.GetExportTask, params as unknown as JSONValue);
};
