import { http } from "@/utils/http";
import notificationApi from "./api";
import type { messageCountResult, messageListResult, messageUpdateResult } from "./type";
const UrlBase = "";
/**
 * 获取列表
 * @param data
 * @returns
 */
export function messageList(data): Promise<messageListResult> {
  return http.request({
    url: UrlBase + notificationApi.messageList,
    method: "post",
    data,
  });
}
/**
 * 已读
 * @param data
 * @returns
 */
export function messageUpdate(data): Promise<messageUpdateResult> {
  return http.request({
    url: UrlBase + notificationApi.messageUpdate,
    method: "post",
    data,
  });
}
/**
 * 轮询 最新消息
 * @param data
 * @returns
 */
export function messageCount(): Promise<messageCountResult> {
  return http.request({
    url: UrlBase + notificationApi.messageCount,
    method: "post",
  });
}
