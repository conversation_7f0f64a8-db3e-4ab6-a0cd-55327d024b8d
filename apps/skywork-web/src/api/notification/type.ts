export type messageCountItemType = {
  message_id: number;
  message_type: String;
  action_type: String;
  message_info: String;
  message_status: number; //0 未读 1 已读
  message_time: String;
};
export type messageCountResult = {
  code: Number;
  message: String;
  msg: String;
  trace_id: String;
  data: {
    message_count: number;
    list: messageCountItemType[];
  };
};

type messageListResultItem = {
  message_id;
  message_type;
  action_type;
  message_info;
  message_status;
  message_time;
  message_dialog_info;
};

export type messageListResult = {
  code: Number;
  message: String;
  msg: String;
  trace_id: String;
  data: {
    offset: string;
    hash_more: boolean;
    list: messageListResultItem[];
  };
};
export type messageUpdateResult = {
  code: number;
  message: string;
  msg: string;
  trace_id: string;
  data: {
    update_flag: boolean;
  };
};
