import { SkillId } from "@/components/SearchArea/types";
import { Response } from "@/typings/common";
import { http } from "@/utils/http";
import { AgentContentCheckStatus, AgentHistory, AgentId, AgentSource } from "@tg-fe/shared";
import chatApi from "./api";

export const apiChatWsHistory = (params) => {
  return http.post<Response<any>>(chatApi.chatWsHistory, params);
};

export const apiChatWsStop = (params) => {
  return http.post<Response<any>>(chatApi.chatWsStop, params);
};

export const apiChatWsCancel = (params) => {
  return http.post<Response<any>>(chatApi.chatWsCancel, params);
};

export interface apiChatHistoryParams {
  project_id?: string;
  source?: AgentSource;
  offset?: number;
  session_id?: string;
}
/** 获取chat 历史记录 */
export const apiGetChatHistory = (params: apiChatHistoryParams) => {
  return http.post<Response<AgentHistory>>(chatApi.chatHistory, params as unknown as JSONValue);
};
export const apiGetChatHistoryShare = (params: apiChatHistoryParams) => {
  return http.post<Response<AgentHistory>>(chatApi.chatHistoryShare, params as unknown as JSONValue);
};

/** 获取sessionId */
export const apiGetChatSessionId = (params: { project_id: string; agent_id: AgentId; source: AgentSource }) => {
  return http.post<Response<{ session_id: string; however: string; is_reconnect: boolean }>>(
    chatApi.chatSessionIdGet,
    params
  );
};
export const apiGetChatSessionIdShare = (params: { project_id: string; agent_id: AgentId; source: AgentSource }) => {
  return http.post<Response<{ session_id: string; however: string; is_reconnect: boolean }>>(
    chatApi.chatSessionIdGetShare,
    params
  );
};

/** 获取脑图 */
export const apiChatMindMap = (params: { file_id: string; project_id: string }) => {
  return http.post<Response<{ mind_map: string }>>(chatApi.chatMindMap, params);
};

/** 获取摘要 */
export const apiChatSummary = (params: { file_id: string; project_id: string }) => {
  return http.post<Response<{ title: string; summary: string }>>(chatApi.chatSummary, params);
};

/** 获取外链的response header，用于判断外链是否被iframe嵌套 */
export const apiChatQueryHeader = (params: { url: string }) => {
  return http.post<Response<{ "Content-Security-Policy": string; "X-Frame-Options": string }>>(
    chatApi.chatQueryHeader,
    params
  );
};

/** my work列表 */
export const apiChatQueryMyWork = <T = Record<string, any>>(params: Record<string, any>) => {
  return http.post<
    Response<{
      my_works: T[];
    }>
  >(chatApi.chatQueryMyWork, params);
};
export const apiChatQueryMyWorkShare = <T = Record<string, any>>(params: Record<string, any>) => {
  return http.post<
    Response<{
      my_works: T[];
    }>
  >(chatApi.chatQueryMyWorkShare, params);
};

/** my work tab */
export const apiChatQueryWorkTab = <T = Record<string, any>>(params: {
  project_id: string;
  session_id: string;
  office_id: SkillId;
}) => {
  return http.post<
    Response<{
      tab_list: T[];
    }>
  >(chatApi.chatQueryWorkTab, params);
};

/** 刷新历史 */
export const apiChatHistoryRefresh = (params: Record<string, any>) => {
  return http.post<Response<AgentHistory>>(chatApi.chatHistoryRefresh, params);
};

/** 内容检查
 *
 * 0: 默认(用户输入)
 * 1:清面板/todolist审核/ppt大纲
 * 2:生成物编辑后审核
 */
export const apiChatRiskCheck = (params: { content: string; request_type: 0 | 1 | 2; agent_type: string }) => {
  return http.post<
    Response<{
      is_hit: boolean;
      reason: string;
      request_id: string;
      status: AgentContentCheckStatus;
      tips: string;
    }>
  >(chatApi.chatRiskCheck, params, {
    timeout: 60 * 1000,
  });
};

export interface ApiChatMarkWallStatusRes {
  wall_status: number;
  period_second: number;
  timeout: number;
  recharge_button_status: number;
  continue_button_status: number;
  count_down_status: number;
  wall_expired_at: number;
  final_state: boolean;
  text: string;
  append_text: string;
  can_continue: boolean;
}
/** 内容检查 */
export const apiChatMarkWallStatus = (params: { project_id: string }) => {
  return http.post<Response<ApiChatMarkWallStatusRes | null>>(chatApi.chatMarkWallStatus, params);
};

/**
 * 取消自动提交
 * @param params
 * @returns
 */
export const cancelAutoSkip = (params: { question_id: string }) => {
  return http.post(chatApi.cancelAutoSkip, params);
};
