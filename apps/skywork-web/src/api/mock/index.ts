import { http } from "@/utils/http";

type ListResult = {
  code: number;
  message: string;
  result: {
    list: Array<any>;
  };
};

export function getListApi(params?: object): Promise<ListResult> {
  return http.request({
    url: "/dev-api/list/get",
    method: "get",
    params,
  });
}

export function getListApiError(data?: object): Promise<ListResult> {
  return http.request({
    url: "/dev-api/list/error",
    method: "post",
    data,
  });
}

export function getTestListApi(params?: object): Promise<ListResult & { total: number }> {
  return http.request({
    url: "/list/test",
    method: "get",
    params,
  });
  // return http.get("/list/test", { params });
}

export function apiExportTest(params?: object): Promise<ListResult & { mockBlobData: Blob }> {
  return http.request({
    url: "/list/export/excel",
    method: "get",
    params,
  });
}

export function apiPreviewSSETest(params?: object): Promise<ListResult & { mockBlobData: Blob }> {
  return http.request({
    url: "/preview-sse",
    // method: "get",
    params,
  });
}
