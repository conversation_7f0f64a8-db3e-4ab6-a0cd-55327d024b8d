import { http } from "@/utils/http";
import questionnaireApi from "./api";
// import {getFeedbackResult} from "./type";
// const UrlBase = import.meta.env.VITE_BASE_API;
const UrlBase = "";

export function getFeedback(type) {
  return http.request({
    url: UrlBase + questionnaireApi.feedbackDistribute,
    method: "get",
    params: {
      template_type: type,
    },
  });
}

export function feedbackReport(data) {
  return http.request({
    url: UrlBase + questionnaireApi.feedbackReport,
    method: "post",
    data,
  });
}
