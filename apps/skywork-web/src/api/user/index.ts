import { http } from "@/utils/http";
import axiosUrl from "@/views/loginChina/utils/axiosUrl.ts";
import userApi from "./api";
import { MemoryResult, NoticeResult, UserInfoResult } from "./type";
// const UrlBase = import.meta.env.VITE_BASE_API;
const UrlBase = "";

export function userInfo(): Promise<UserInfoResult> {
  return http.request({
    url: UrlBase + userApi.userInfo,
    method: "get",
  });
}
export function infoUpdate(data: { nick_name: String; avatar_url: String }): Promise<UserInfoResult> {
  return http.request({
    url: UrlBase + userApi.infoUpdate,
    method: "post",
    data,
  });
}

export function userSettingSave(data: any): Promise<UserInfoResult> {
  return http.request({
    url: UrlBase + userApi.settingSave,
    method: "post",
    data,
  });
}

export function getShareData(params): Promise<UserInfoResult> {
  return http.request({
    url: UrlBase + userApi.shareData,
    method: "get",
    params,
  });
}

export function getShareDataV2(data): Promise<UserInfoResult> {
  return http.request({
    url: UrlBase + userApi.shareDataV2,
    method: "POST",
    data,
  });
}

export function getMemoryNotice(): Promise<NoticeResult> {
  return http.request({
    url: UrlBase + userApi.getMemoryNotice,
    method: "get",
  });
}
export function getMemoryList(params): Promise<MemoryResult> {
  return http.request({
    url: UrlBase + userApi.getMemoryList,
    method: "get",
    params,
  });
}
export function delMemory(data: any): Promise<UserInfoResult> {
  return http.request({
    url: UrlBase + userApi.delMemory,
    method: "post",
    data,
  });
}

// 国内版
// 检验登陆状态, 存储用户信息
export function userInfoChina(): Promise<UserInfoResult> {
  return http.request({
    url: axiosUrl.checkLogin,
    method: "post",
    headers: {
      "K-Client-Id": import.meta.env.VITE_DEFAULT_CLIENT_ID,
    },
  });
}

// 退出登录
export function userLogout(): Promise<UserInfoResult> {
  return http.request({
    url: axiosUrl.logout,
    method: "get",
    headers: {
      "K-Client-Id": import.meta.env.VITE_DEFAULT_CLIENT_ID,
    },
  });
}

// 同步登录状态
export const syncStatus = () => {
  return http.request({
    url: axiosUrl.syncStatus,
    method: "post",
  });
};

// 更新用户信息（用户名）
export const userUpdate = (data: { user_name: string }) => {
  return http.request({
    url: axiosUrl.userUpdate,
    method: "post",
    data,
  });
};

// 更新用户信息（头像）
export const userUpdateAvatar = (data: { avatar: string }) => {
  return http.request({
    url: axiosUrl.setUserInfo,
    method: "post",
    data,
  });
};

// 更新用户设置（主题等）
export function userSettingSaveChina(data: any): Promise<UserInfoResult> {
  return http.request({
    url: axiosUrl.updateSetting,
    method: "post",
    data,
    headers: {
      "K-Client-Id": import.meta.env.VITE_DEFAULT_CLIENT_ID,
    },
  });
}

// 反馈
export const feedbackReportChina = (data: any) => {
  return http.request<any>({
    url: axiosUrl.feedbackUrl,
    method: "post",
    data,
  });
};

export * from "./login";
