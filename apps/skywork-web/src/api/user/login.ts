import { http } from "@/utils/http";
import userApi from "./api";
import { EmailLoginResult, LoginEmailSendResult, logoutResult } from "./type";
// const UrlBase = import.meta.env.VITE_BASE_API;
const UrlBase = "";
export function loginEmailSend(data?: object): Promise<LoginEmailSendResult> {
  return http.request({
    url: UrlBase + userApi.loginEmailSend,
    method: "post",
    data,
  });
}

export function EmailLogin(data: {
  login_email_address: string;
  login_code: string;
  inviter_user_key?: string;
}): Promise<EmailLoginResult> {
  return http.request({
    url: UrlBase + userApi.EmailLogin,
    method: "post",
    data,
  });
}

export function logout(): Promise<logoutResult> {
  return http.request({
    url: UrlBase + userApi.logout,
    method: "get",
  });
}

export function delUser() {
  return http.request({
    url: UrlBase + userApi.deleteUser,
    method: "post",
  });
}
//del
export function googleLogin(): Promise<logoutResult> {
  return http.request({
    url: UrlBase + userApi.googleLogin,
    method: "get",
  });
}

export function googleCallback(code): Promise<logoutResult> {
  return http.request({
    url: UrlBase + userApi.googleCallback,
    method: "get",
    params: {
      code,
    },
  });
}

export function googleVerify(params: { credential: string; inviter_user_key?: string }): Promise<logoutResult> {
  return http.request({
    url: UrlBase + userApi.googleVerify,
    method: "post",
    data: params,
  });
}
