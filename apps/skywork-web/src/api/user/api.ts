const apiBase = "/usercenter";
export const shareDomainUrl = import.meta.env.VITE_API_HOST;
export default {
  // https://api-us-test.tiangong.cn/usercenter-api/user_api/email/send
  // https://rg975ojk5z.feishu.cn/docx/D25BdXxVrowIkZxRTq6ct5hrnUb
  loginEmailSend: apiBase + "/email/send", // 邮箱验证码下发
  EmailLogin: apiBase + "/email/login", //邮箱登录/注册：
  userInfo: apiBase + "/user/get", // 用户查询：
  infoUpdate: apiBase + "/user/update", // 用户更新：
  logout: apiBase + "/user/logout", // 退出登入？？？？
  //TODO 内部接口删除用户
  deleteUser: apiBase + "/user/delete", // 账号注销：
  settingSave: apiBase + "/user/setting/update", //保存设置：

  // googleLogin: apiBase + "/user_center/google/login", //google 登入
  // googleCallback: apiBase + "/user_center/google/callback", //google 返回

  googleVerify: apiBase + "/google/verify", //Google验证

  shareData: "/creation/share/detail", //获取分享的信息
  shareDataV2: "/creation/share/detail_info", //获取分享的信息 v2

  getMemoryNotice: "/creation/memory/notice", //查询memory说明页
  getMemoryList: "/creation/memory/page_list", //查询memory列表
  delMemory: "/creation/memory/remove", //删除memory
};

export const activity = {};
