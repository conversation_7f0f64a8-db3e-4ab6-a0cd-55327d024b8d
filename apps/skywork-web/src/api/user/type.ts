export type LoginEmailSendResult = {
  code: Number;
  message: String;
  msg: String;
  trace_id: String;
  data: any;
};

export type EmailLoginResult = {
  code: Number;
  message: String;
  msg: String;
  trace_id: String;
  data: {
    send_address: String;
    is_new_user: Boolean;
    token: String;
  };
};

export type UserResult = {
  code: Number;
  message: String;
  msg: String;
  trace_id: String;
  data: {
    nick_name: String;
    email: String;
    avatar_url: String;
  };
};

export type UserInfoResult = {
  code: Number;
  message: String;
  msg: String;
  trace_id: String;
  data: {
    user_info: {
      nick_name: string;
      email: string;
      avatar: string;
      avatar_url: string;
      status: number;
      country_iso_code: string;
      offset_utc: number;
    };
    user_setting: {
      task_notifications: number;
      product_updates: number;
      promotional_messages: number;
      theme: number;
      browser_notifications: number;
      discord_enter: number;
    };
  };
};

export type logoutResult = {
  code: Number;
  message: String;
  msg: String;
  trace_id: String;
};

export type MemoryResult = {
  code: number;
  message: string;
  msg: string;
  data: {
    offset: number;
    has_more: boolean;
    space: number;
    list: {
      content: string;
      id: number;
    }[];
  };
};

export type NoticeResult = {
  code: number;
  message: string;
  msg: string;
  data: {
    info: string;
  };
};
