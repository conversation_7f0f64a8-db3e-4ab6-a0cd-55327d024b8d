export type queryConfig = {
  project_id: string;
  question_id: String;
  data_type: string;
  data: {
    show: number;
    source_show: number;
    expire: number;
    play_back: number;
    search_record: number;
    status: number;
    created: number;
    long_link: string;
  };
};

export type createConfig = {
  project_id: string;
  question_id: String;
  data_type: string;
  show: number;
  expire: number;
  play_back: number;
  data: {};
};

export enum DataType {
  project = "project",
  outfile = "outfile",
}

export enum SourceShow {
  hidden = 2,
  show = 1,
}

export enum PlayBack {
  close = 0,
  open = 1,
}

export enum ShareAuthStatus {
  init = 1, //正常
  expire = 2, //失效
  hide = 3, //不可见
}

export type shareAuthConfig = {
  code: number;
  data: {
    source_show: SourceShow; // 信源展示 1 展示2不展示
    play_back: PlayBack; // 是否回放 0关闭 1 开启
    status: ShareAuthStatus; // 链接是否过期 1正常 2 失效 3 不可见
    long_link: string; // 长链接 status =1的时候才有值
    short_link: string; // 短链接status =1的时候才有值
  };
};
