import { http } from "@/utils/http";
import userApi from "./api";
import { createConfig, queryConfig, shareAuthConfig } from "./type";
const UrlBase = "";
export function getQueryConfig(data: {
  project_id: string;
  out_id: String;
  out_type: String;
  data_type: string;
}): Promise<queryConfig> {
  return http.request({
    url: UrlBase + userApi.queryConfig,
    method: "post",
    data,
  });
}
export function toCreateConfig(data: {
  project_id: string;
  out_id: String;
  out_type: String;
  data_type: string;
  show: number;
  expire: number;
  play_back: number;
}): Promise<createConfig> {
  return http.request({
    url: UrlBase + userApi.createConfig,
    method: "post",
    data,
  });
}

export function shareAuth(data: {
  project_id: string;
  out_id: String;
  out_type: String;
  data_type: string;
}): Promise<shareAuthConfig> {
  return http.request({
    url: UrlBase + userApi.shareAuth,
    method: "post",
    data,
  });
}
