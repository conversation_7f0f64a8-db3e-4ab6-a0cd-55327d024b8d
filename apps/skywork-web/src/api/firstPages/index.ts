import { http } from "@/utils/http";
import api, { recently } from "./api";

export interface Response<T> {
  code: number;
  data: T;
  message: string;
}

//风控
export const apiGetChatRisk = async (data: { query: string }) => {
  return http.post<Response<{ hit: boolean }>>(api.chatRisk, data);
};
//chat页项目技能
export const apiGetChatProjectSkill = async (data: { project_id: string }) => {
  return http.get<Response<{ skill_id: string; net_search: boolean }>>(api.getChatProjectSkill, data);
};
// 设置项目技能
export const apiSetChatProjectSkill = async (data: {
  project_id: string;
  skill_id: any;
  net_search?: boolean;
  scene_template: string;
}) => {
  return http.post<Response<{}>>(api.setChatProjectSkill, data);
};
// 设置首页用户技能
export const apiSetHomeUserSkill = async (data: { skill_id: any; net_search?: boolean }) => {
  return http.post<Response<{}>>(api.setHomeUserSkill, data);
};
// 首页用户技能
export const apiGetHomeUserSkill = async () => {
  return http.get<Response<{ skill_id: string; net_search: boolean }>>(api.homeUserSkill, {});
};
// 首页配置
export const apiGetHomeConfig = async () => {
  return http.get<Response<{ config_list: any }>>(api.homeConfig, {});
};

// 侧边栏搜索
export const apiSearch = async (data: { query: string; offset: string }) => {
  return http.post<Response<{ list: any[]; has_more: boolean; total: number; offset: string }>>(api.search, data);
};

// 侧边栏 - 修改是否点击discord卡片入口
export const apiDiscordUpdate = async (data: { discord_enter: number }) => {
  return http.post<Response<{}>>(api.discordUpdate, data);
};

// 最近的列表（项目&文件）
export const apiRecentList = async () => {
  return http.get<Response<{ p_list: any[]; k_list: any[] }>>(recently.list, {});
};

// 知识库文件夹列表
export const apiFolderList = async (data: { source: "project" | "knowledge" }) => {
  return http.post<Response<any>>(api.folderList, data);
};

// 创建文件夹
export const apiCreateFolder = async (data: { source: string; type: string; name?: string; file_ids?: string[] }) => {
  return http.post<Response<any>>(api.createFolder, data);
};

// 更新文件夹
export const apiUpdateName = async (data: { source: string; type: string; id: string; name: string }) => {
  return http.post<Response<any>>(api.updateName, data);
};

// 删除文件夹
export const apiDel = async (data: { source: string; ids: string[] }) => {
  return http.post<Response<any>>(api.del, data);
};

// 移动到文件夹
export const apiMoveToFolder = async (data: { source_ids: string; target_id: string; source: string }) => {
  return http.post<Response<any>>(api.moveToFolder, data);
};

// 项目/知识库列表
export const apiProSpaceList = async (data: { source: "project" | "knowledge"; offset: number; sort?: string }) => {
  return http.post<Response<any>>(api.proSpaceList, data);
};

// 知识库文件夹内部文件列表
export const apiFolderInnerList = async (data: { id: string; source: "project" | "knowledge"; offset: number }) => {
  return http.post<Response<{ k_list: any[]; k_folder: any; has_more: boolean; offset: number }>>(
    api.folderInnerList,
    data
  );
};
