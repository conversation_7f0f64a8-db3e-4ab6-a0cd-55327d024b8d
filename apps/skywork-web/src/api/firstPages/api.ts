const apiBase = "/creation";
const apiDiscord = "/usercenter";
export default {
  search: apiBase + "/home/<USER>", // 侧边栏搜索
  discordUpdate: apiDiscord + "/user/setting/update", // 侧边栏 - 修改是否点击discord卡片入口
  // 以下为通用接口
  folderList: apiBase + "/resource/folder/list", // 知识库文件夹列表
  createFolder: apiBase + "/resource/save", // 创建文件夹
  updateName: apiBase + "/resource/update", // 更新文件夹/文件/项目
  del: apiBase + "/resource/del", // 删除文件夹/文件/项目
  moveToFolder: apiBase + "/resource/move", // 移动到文件夹
  proSpaceList: apiBase + "/resource/list", // 项目/知识库列表
  folderInnerList: apiBase + "/resource/folder/file/list", // 知识库文件夹内部文件列表
  homeConfig: apiBase + "/home/<USER>", // 首页配置
  homeUserSkill: apiBase + "/home/<USER>", // 首页用户技能
  setHomeUserSkill: apiBase + "/home/<USER>", // 设置首页用户技能
  setChatProjectSkill: apiBase + "/project/record_project_skill", // 设置项目技能
  getChatProjectSkill: apiBase + "/project/project_skill", // 获取项目技能
  chatRisk: apiBase + "/chat/risk", // 风控
};

// 【最近的】
export const recently = {
  list: apiBase + "/recent/list", // 最近的列表（项目&文件）
};
