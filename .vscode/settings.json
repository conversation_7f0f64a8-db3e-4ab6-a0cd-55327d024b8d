{
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[vue]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[snippets]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "always"
  },
  "typescript.preferences.importModuleSpecifier": "non-relative",
  "typescript.updateImportsOnFileMove.enabled": "always",
  "scss.lint.unknownAtRules": "ignore",
  "css.lint.unknownAtRules": "ignore",
  "cSpell.words": [
    "canvg",
    "Pannable",
    "canvga",
    "doccie",
    "sandpack",
  ],
  "i18n-ally.localesPaths": [
    "src/locales/lang"
  ],
  "i18n-ally.displayLanguage": "zh-CN",
  "i18n-ally.enabledParsers": [
    "ts",
    "json"
  ],
  "i18n-ally.enabledFrameworks": [
    "vue"
  ],
  "i18n-ally.editor.preferEditor": true,
  "i18n-ally.keystyle": "nested",
  "files.exclude": {
    "**/.git": true,
    "**/.svn": true,
    "**/.hg": true,
    "**/CVS": true,
    "**/.DS_Store": true,
    "**/Thumbs.db": true,
    "**/.ruby-lsp": true,
    "**/.classpath": true,
    "**/.factorypath": true,
    "**/.history": true,
    "**/.project": true,
    "**/.settings": true,
    "**/*.acn": true,
    "**/*.acr": true,
    "**/*.alg": true,
    "**/*.aux": true,
    "**/*.bcf": true,
    "**/*.glg2": true,
    "**/*.glo2": true,
    "**/*.gls2": true,
    "**/*.ist": true,
    "**/*.log": true,
    "**/*.run.xml": true
  },
  "explorerExclude.backup": {}
}